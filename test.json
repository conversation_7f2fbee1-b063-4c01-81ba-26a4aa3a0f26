Google Sign-In Success: {
  "data": {
    "idToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImVlMTkzZDQ2NDdhYjRhMzU4NWFhOWIyYjNiNDg0YTg3YWE2OGJiNDIiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NCJt5_L_6EZH4Hdp86KrkeCE3VRaFsX2pJ8A3AR4M4_-jTqLn6LJOXZ1Cj-DU7IQwsyXesi9s3gpaQktKzComfaNC-L-ouAH_986zfX_wJKIZq3z7gSmzzaD01tepdVK0UB4UExl94Yo7xPmXhuiiorqvNklJ-KpFW7FZmYlEABwB6wAEiv_CTkNu2xwBmnPxHiBY6dPfMCkyCXVQ0JUVZJbGSVb4HMuYCwmYqkoYozZGREsRq3Rj9AMi0g3aD97Hrz9XxuBNjAXHqbbV8tuVY7bsDPYysFUJSkZx1-5N9QL7ca1khPxPrubWxssbVfZ7qDxH2VF_ME3cIaR8OSImQ",
    "scopes": [
      "https://www.googleapis.com/auth/userinfo.email",
      "https://www.googleapis.com/auth/userinfo.profile",
      "openid"
    ],
    "serverAuthCode": "4/0AQSTgQFEoD7FxrQ0FLh2XLNhySEl65CvxaWBJMr17Mc51pQxG9nFxPPIl9Nq7FMwAK3Fjg",
    "user": {
      "email": "<EMAIL>",
      "familyName": "<PERSON>",
      "givenName": "Aremu",
      "id": "105800804025017580722",
      "name": "Aremu Moses",
      "photo": "https://lh3.googleusercontent.com/a/ACg8ocKddXz7MJXJ_kIJ-wTVF_08eKGBQf9vpJ2RWKd2qn_PR6qgqcg=s120"
    }
  },
  "type": "success"
}