import PushNotification from 'react-native-push-notification';

export const configurePushNotifications = () => {
    PushNotification.createChannel(
        {
            channelId: "ridefuze", // Replace with your desired channel ID
            channelName: 'ridefuze', // Replace with your desired channel name
            channelDescription: 'ridefuze', // Replace with your desired channel description
            playSound: true,
            soundName: 'default',
            importance: 4,
            vibrate: true,
        },
        (created) => {
            console.log(`Channel 'YOUR_CHANNEL_ID' created: ${created}`);
        }
    );

    PushNotification.configure({
        onRegister: function (token) {
            console.log('TOKEN:', token);
        },
        onNotification: function (notification) {
            console.log('NOTIFICATION:', notification);
            notification.finish();
        },
        onAction: function (notification) {
            console.log('ACTION:', notification.action);
            console.log('NOTIFICATION:', notification);
        },
        onRegistrationError: function (err) {
            console.error(err.message, err);
        },
        permissions: {
            alert: true,
            badge: true,
            sound: true,
        },
        popInitialNotification: true,
        requestPermissions: true,
    });
};
