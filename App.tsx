import React, { useEffect } from 'react';
import { LogBox, SafeAreaView, StatusBar, View, Platform } from 'react-native';
import { COLORS } from './src/constants';
import AppNavigation from './src/navigations/AppNaviagtion';
import { Provider } from 'react-redux';
import { store } from './redux/store';
import { StripeProvider } from '@stripe/stripe-react-native';
import messaging from '@react-native-firebase/messaging';
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import socketService from './src/services/SocketService';
import PushNotification from 'react-native-push-notification';

PushNotification.configure({
  onRegister: function (token) {
    console.log('TOKEN:', token);
  },

  onNotification: function (notification) {
    console.log('NOTIFICATION:', notification);

  },

  senderID: '1022981107530', 
  permissions: {
    alert: true,
    badge: true,
    sound: true,
  },
  popInitialNotification: true,
  requestPermissions: true,
});

GoogleSignin.configure({
  webClientId: '1022981107530-q0enh00rr7dh47u2sioiqn5gufinr6rc.apps.googleusercontent.com',
  iosClientId: "1022981107530-l2o5l52c8ilridc0dik1eoap7qkg6dca.apps.googleusercontent.com",
  offlineAccess: true,
});

const App = () => {
  useEffect(() => {
    socketService.connect();
    return () => {
      socketService.disconnect();
    };
  }, []);

  useEffect(() => {
    // Foreground
    messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', remoteMessage);
      PushNotification.localNotification({
        title: remoteMessage?.notification?.title,
        message: remoteMessage?.notification?.body,
        playSound: true,
        soundName: 'default',
      });
    });

    // Background
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Message handled in the background!', remoteMessage);
    });
  }, []);

  const PUBLISHABLE_KEY = 'pk_test_51MccUgHurLamFWf3FKDVsNtR9PSBU5TaoD1ADv63jS3U8JuD0imwCABxyOEyvDgtw6k00HZMTrICZx8KR3PVlGLL008m4Ju8s3';

  return (
    <>
      <StatusBar
        backgroundColor={Platform.OS === 'android' ? COLORS.primary : 'transparent'}
        barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
        translucent={Platform.OS === 'android'}
      />
      <Provider store={store}>
        <StripeProvider publishableKey={PUBLISHABLE_KEY}>
          {/* <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.primary }}> */}
            <AppNavigation />
          {/* </SafeAreaView> */}
        </StripeProvider>
      </Provider>
    </>
  );
};

export default App;
