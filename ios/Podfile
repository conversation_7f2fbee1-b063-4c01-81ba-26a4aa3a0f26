require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip


platform :ios, min_ios_version_supported
prepare_react_native_project!
linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

# Resolve the path to `react_native_pods.rb` using Node.js
react_native_pods_path = Pod::Executable.execute_command('node', [
  '-p',
  'require.resolve("react-native/scripts/react_native_pods.rb", { paths: [process.cwd()] })'
]).strip

# Manually load the resolved `react_native_pods.rb`
require react_native_pods_path

# Load permissions setup script
permissions_setup_script = Pod::Executable.execute_command('node', [
  '-p',
  'require.resolve("react-native-permissions/scripts/setup.rb", { paths: [process.cwd()] })'
]).strip

require permissions_setup_script

setup_permissions([
  'Contacts',
  'LocationWhenInUse',
  'PhotoLibrary',
   'LocationAccuracy',
  'LocationAlways',
  'MediaLibrary',
  'Microphone',
])

target 'RideFusePassenger' do
  config = use_native_modules!
  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'
  pod 'FirebaseCore', :modular_headers => true
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'RideFusePassengerTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # Ensure proper post-install configuration
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
