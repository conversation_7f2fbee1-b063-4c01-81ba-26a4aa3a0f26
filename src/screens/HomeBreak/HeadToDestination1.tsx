import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import GradientBackground from '../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const HeadToDestination1 = () => {
  const navigation = useNavigation();

  const handleNavigation = (screen) => {
    navigation.navigate(screen);
  };

  return (
    <GradientBackground>
      {/* Header Section */}
      <View style={{paddingTop:60}}/>
      
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}>Head to destination</Text>
      </View>

      {/* Content Section */}
      <View style={styles.container}>
        {/* Location Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location</Text>
          <TouchableOpacity
            style={styles.row}
            onPress={() => handleNavigation('HeadToDestination2')}
          >
            <View style={styles.rowContent}>
              <Image source={icons.DestinationIcon} style={styles.rowIcon} />
              <Text style={styles.rowText}>Head to destination</Text>
            </View>
            <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.row}
            onPress={() => handleNavigation('StayInArea')}
          >
            <View style={styles.rowContent}>
              <Image source={icons.DestinationIcon} style={styles.rowIcon} />
              <Text style={styles.rowText}>Stay in area</Text>
            </View>
            <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
          </TouchableOpacity>
        </View>

        {/* Ride Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ride</Text>
          <TouchableOpacity
            style={styles.row}
            onPress={() => handleNavigation('RideType')}
          >
            <View style={styles.rowContent}>
              <Image source={icons.CarIcon} style={styles.rowIcon} />
              <View>
                <Text style={styles.rowText}>Ride types</Text>
                <Text style={styles.rowSubText}>
                  Choose the type of ride you will like
                </Text>
              </View>
            </View>
            <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.row}
            onPress={() => handleNavigation('QueuedRide')}
          >
            <View style={styles.rowContent}>
              <Image source={icons.QueueIcon} style={styles.rowIcon} />
              <View>
                <Text style={styles.rowText}>Queued ride</Text>
                <Text style={styles.rowSubText}>
                  Choose how to accept new ride while...
                </Text>
              </View>
            </View>
            <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
          </TouchableOpacity>
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 70,
  },
  container: {
    padding: 20,
  },
  section: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
  },
  sectionTitle: {
    ...FONTS.h4,
    lineHeight:20,
    paddingBottom:10,
    borderBottomWidth: 0.3,
    borderBottomColor: COLORS.light_grey,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.3,
    borderBottomColor: COLORS.light_grey,
  },
  rowContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowIcon: {
    width: 20,
    height: 20,
    marginRight: 15,
    tintColor:COLORS.black
  },
  rowText: {
    ...FONTS.body5,
    color: COLORS.black,

  },
  rowSubText: {
    ...FONTS.body6,
    fontSize:12,
    marginVertical:5,
    color: COLORS.grey,

  },
  arrowIcon: {
    width: 16,
    height: 16,
    tintColor: COLORS.black,
  },
});

export default HeadToDestination1;
