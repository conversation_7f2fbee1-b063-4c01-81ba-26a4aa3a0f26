 
import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  FlatList,
  ActivityIndicator,
  StyleSheet,
} from "react-native";
import io from "socket.io-client";
import Geolocation from "react-native-geolocation-service";
import GooglePlacesAutocompleteComponent from "../../components/shared/GooglePlaceAutoComplete";
import { BASE_URL } from "../../../Baseurl";

const SOCKET_URL = `${BASE_URL}/passenger`;
const socket = io(SOCKET_URL, {
  transports: ["websocket"],
  withCredentials: true,
});

const ArrangeYourRide = () => {
  const [selectedDriver, setSelectedDriver] = useState(null);
const [showPaymentOptions, setShowPaymentOptions] = useState(false);

// const handleSelectDriver = (driver) => {
//   setSelectedDriver(driver);
//   setShowPaymentOptions(true); // ✅ Show payment options after selecting driver
// };

// const handleRidePayment = (rideId, type) => {
//   setPaymentLoading(true);

//   const cardDetails = {
//     cardHolderName: "Ade One",
//     cardNumber: "****************",
//     cvv: "456",
//     expiryDate: "12/25",
//   };

//   let data;
//   if (type === "card") {
//     data = { rideId, paymentType: "card", cardId: "67952582bf03f1fa37e65fbd" };
//   } else if (type === "direct") {
//     data = { rideId, paymentType: "direct", cardDetails };
//   } else {
//     data = { rideId, paymentType: "wallet",cardDetails, };
//   }

//   console.log("💳 Initiating Payment Request:", JSON.stringify(data, null, 2));
//   socket.emit("payForRide", data);
// };



  const [rideDetails, setRideDetails] = useState({
    from: "",
    fromId: "",
    fromCoordinates: { type: "Point", coordinates: [null, null] },
    to: [],
    personnalRide: true,
    noOffPassengers: 1,
    pickupPoint: "",
    rideType: "personal",
  });

  const [responseMessage, setResponseMessage] = useState("");
  const [availableDrivers, setAvailableDrivers] = useState([]);
  const [newRideId, setNewRideId] = useState("");
  const [userLocation, setUserLocation] = useState({ type: "Point", coordinates: [null, null] });
  const [loading, setLoading] = useState(false);
  const [driverRequested, setDriverRequested] = useState(null);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [priceRange, setPriceRange] = useState("");

  useEffect(() => {
    // Get user's current location
    Geolocation.getCurrentPosition(
      (position) => {
        // const { latitude, longitude } = position.coords;
        const longitude = 7.818791099999999;
        const latitude = 3.908815999999999;
        setUserLocation({ type: "Point", coordinates: [longitude, latitude] });
      },
      (error) => console.error("Error getting location:", error),
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  }, []);

  useEffect(() => {
    if (userLocation.coordinates[0] !== null && userLocation.coordinates[1] !== null) {
      socket.emit("getNearbyDrivers", { location: userLocation.coordinates, radius: 5 });
    }
  }, [userLocation]);

  useEffect(() => {
    socket.on("payForRide", (response) => {
      console.log("✅ PAYMENT RESPONSE RECEIVED:", JSON.stringify(response, null, 2));
      setPaymentLoading(false);
  
      if (response.success) {
        Alert.alert("🎉 Payment Successful!", `Ride is now active.\n\nMessage: ${response.message}`);
      } else {
        Alert.alert("❌ Payment Failed", `Try again.\n\nReason: ${response.message}`);
      }
    });
  
    return () => {
      socket.off("payForRide");
    };
  }, []);
  
  useEffect(() => {
    socket.on("nearbyDrivers", (data) => {
      console.log("🚖 NEARBY DRIVERS:", JSON.stringify(data,null,2));
      setResponseMessage(data.success ? `Drivers found: ${data.drivers.length}` : "No drivers found");
    });

    socket.on("rideRequested", (data) => { 
      console.log("✅ Ride Requested:", data);
      setResponseMessage(data.success ? "Ride request successful!" : "Error requesting ride");
      setAvailableDrivers(data?.drivers || []);
      setNewRideId(data.rideId);
    });

    socket.on("availableDriversForRide", (data) => {
      console.log("🚗 AVAILABLE DRIVERS FOR RIDE:", JSON.stringify(data, null, 2));
    
      if (data?.success && data?.finalResult?.length > 0) {
        setAvailableDrivers(data?.finalResult); // ✅ Store drivers in state
      } else {
        setAvailableDrivers([]); // ✅ Ensure empty state if no drivers are found
        Alert.alert("No Available Drivers", "No drivers are currently available. Please try again later.");
      }
    });
    

    socket.on("requestDriver", (response) => {
      setAvailableDrivers([]);
      setDriverRequested(response);
      console.log("✅ Driver Requested Response:", response);
    });

    socket.on("payForRide", (response) => {
      console.log("💳 PAYMENT RESPONSE:", response);
      setPaymentLoading(false);
    });

    socket.on("error", (data) => {
      console.error("❌ Socket Error:", data.message);
      setResponseMessage(data.message || "Error fetching nearby drivers.");
    });

    return () => {
      socket.off("nearbyDrivers");
      socket.off("rideRequested");
      socket.off("availableDriversForRide");
      socket.off("requestDriver");
      socket.off("payForRide");
      socket.off("error");
    };
  }, []);

  /** ✅ Handle Selecting "From" Location */
  const handleFromSelect = async (location) => {
    if (!location?.location?.lat || !location?.location?.lng) {
      Alert.alert("Error", "Invalid location selected");
      return;
    }

    setRideDetails((prev) => ({
      ...prev,
      from: location?.description,
      fromId: location?.place_id,
      fromCoordinates: {
        type: "Point",
        coordinates: [location?.location?.lng, location?.location?.lat], // Set proper coordinates
      },
    }));

    console.log("📍 Selected 'From' Location:", location);
  };

  /** ✅ Handle Selecting "To" Location */
  const handleToSelect = async (index, location) => {
    if (!location?.location?.lat || !location?.location?.lng) {
      Alert.alert("Error", "Invalid destination selected");
      return;
    }

    setRideDetails((prev) => {
      const newTo = [...prev.to];
      newTo[index] = {
        place: location?.description,
        placeId: location?.place_id,
        locationCoordinates: {
          type: "Point",
          coordinates: [location?.location?.lng, location?.location?.lat], // Set proper coordinates
        },
      };
      return { ...prev, to: newTo };
    });

    console.log("📍 Selected 'To' Location:", location);
  };

  const addToPlace = () => setRideDetails((prev) => ({ ...prev, to: [...prev.to, {}] }));
  const removeToPlace = (index) => {
    setRideDetails((prev) => {
      const newTo = [...prev.to];
      newTo.splice(index, 1);
      return { ...prev, to: newTo };
    });
  };

  const handleSubmit = () => {
    if (!rideDetails?.fromCoordinates?.coordinates[0] || !rideDetails?.fromCoordinates?.coordinates[1]) {
      Alert.alert("Error", "Please select a valid 'From' location.");
      return;
    }
    if (!rideDetails?.to.length || !rideDetails?.to[0]?.locationCoordinates?.coordinates[0]) {
      Alert.alert("Error", "Please select a valid 'To' location.");
      return;
    }

    setLoading(true);
    console.log("🚗 Requesting Ride:", JSON.stringify(rideDetails, null, 2));

    socket.emit("requestRide", rideDetails);
  };


  const handleRequestDriver = (rideId, driverId) => {
    const data = { driverId, rideId };
    console.log(`🚕 Requesting Driver ${driverId} for Ride:`, data);
    socket.emit("requestDriver", data);
  };

  const handleSelectDriver = (driver) => {
    // Check if the rideId is available
    if (!newRideId) {
      Alert.alert("Error", "Ride ID is not available");
      return;
    }
    
    // Emit requestDriver event with rideId and selected driverId
    handleRequestDriver(newRideId, driver?.driver?.driverId);
    
    // Set the selected driver and show payment options
    setSelectedDriver(driver);
    setShowPaymentOptions(true);
  };
  
  
  const handleRidePayment = (rideId, type) => {
    setPaymentLoading(true);
  
    const cardDetails = {
      cardHolderName: "Ade One",
      cardNumber: "****************",
      cvv: "456",
      expiryDate: "12/25",
      cardType: "Visa",  
    };
  
    let data;
    if (type === "card") {
      data = {
        rideId,
        paymentType: "card",
        cardId: "67952582bf03f1fa37e65fbd",  
        cardDetails,  
      };
    } else if (type === "direct") {
      data = { rideId, paymentType: "direct", cardDetails };
    } else {
      data = { rideId, paymentType: "wallet"};  
    }
  
    console.log("💳 Initiating Payment Request:", JSON.stringify(data, null, 2));
    socket.emit("payForRide", data);
  };
  
  

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Request a Ride</Text>

      <GooglePlacesAutocompleteComponent placeholder="From" onSelect={handleFromSelect} />
      {rideDetails.to.map((_, index) => (
        <View key={index} style={styles.destinationContainer}>
          <GooglePlacesAutocompleteComponent
            placeholder={`To ${index + 1}`}
            onSelect={(location) => handleToSelect(index, location)}
          />
          {index > 0 && (
            <TouchableOpacity onPress={() => removeToPlace(index)} style={styles.removeButton}>
              <Text>❌</Text>
            </TouchableOpacity>
          )}
        </View>
      ))}

      <TouchableOpacity onPress={addToPlace} style={styles.addButton}>
        <Text>Add Destination</Text>
      </TouchableOpacity>

      <TextInput
        style={styles.input}
        placeholder="Pickup Point"
        value={rideDetails.pickupPoint}
        onChangeText={(text) => setRideDetails((prev) => ({ ...prev, pickupPoint: text }))}
      />

      <TouchableOpacity onPress={handleSubmit} style={styles.submitButton}>
        <Text>Submit Ride Request</Text>
      </TouchableOpacity>

      {availableDrivers?.length > 0 && !showPaymentOptions && (
  <View style={styles.driverContainer}>
    <Text style={styles.driverTitle}>Available Drivers</Text>
    <FlatList
      data={availableDrivers}
      keyExtractor={(item) => item?.driver?.driverId}
      renderItem={({ item }) => (
        <TouchableOpacity
          style={styles.driverCard}
          onPress={() => handleSelectDriver(item)}
          
        >
          <Text style={styles.driverName}>
            {item?.driver?.firstName} {item?.driver?.lastName}
          </Text>
          <Text style={styles.carDetails}>
            {item?.car?.model} | {item?.car?.color} | Seats: {item?.car?.noOfSeats}
          </Text>
          <Text style={styles.price}>Price: ${item?.price}</Text>
          <Text style={styles.estimate}>
            ETA: {((item?.estimatedTimeToPickup) / 60).toFixed(2)} mins
          </Text>
        </TouchableOpacity>
      )}
    />
  </View>
)}

{showPaymentOptions && selectedDriver && (
  <View style={styles.paymentContainer}>
    <Text style={styles.paymentTitle}>Choose Payment Method</Text>
    <Text style={styles.paymentDriver}>
      Driver: {selectedDriver?.driver?.firstName} {selectedDriver?.driver?.lastName}
    </Text>

    <TouchableOpacity
  style={styles.paymentButton}
  onPress={() => {
    console.log("💳 Pay with Card Clicked");
    handleRidePayment(selectedDriver.rideId, "card");
  }}
>
  <Text style={styles.paymentText}>💳 Pay with Card</Text>
</TouchableOpacity>

<TouchableOpacity
  style={styles.paymentButton}
  onPress={() => {
    console.log("🏦 Pay Direct Clicked");
    handleRidePayment(selectedDriver.rideId, "direct");
  }}
>
  <Text style={styles.paymentText}>🏦 Pay Directly</Text>
</TouchableOpacity>

<TouchableOpacity
  style={styles.paymentButton}
  onPress={() => {
    console.log("💰 Pay with Wallet Clicked");
    handleRidePayment(selectedDriver.rideId, "wallet");
  }}
>
  <Text style={styles.paymentText}>💰 Pay with Wallet</Text>
</TouchableOpacity>

  </View>
)}


    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20 },

  title: { fontSize: 22, fontWeight: "bold", marginBottom: 10 },
  input: { borderWidth: 1, padding: 10, marginVertical: 5 },
  submitButton: { backgroundColor: "blue", padding: 10, marginTop: 10 },
  addButton: { backgroundColor: "green", padding: 10, marginTop: 10 },
  removeButton: { backgroundColor: "red", padding: 5 },
  driverContainer: { padding: 15, backgroundColor: "#fff", marginTop: 10 },
  driverTitle: { fontSize: 18, fontWeight: "bold", marginBottom: 5 },
  driverCard: { padding: 10, borderBottomWidth: 1, borderColor: "#ccc" },
  driverName: { fontSize: 16, fontWeight: "bold" },
  carDetails: { fontSize: 14, color: "gray" },
  price: { fontSize: 16, fontWeight: "bold", color: "green" },
  estimate: { fontSize: 14, color: "blue" },
  

  paymentContainer: { padding: 15, backgroundColor: "#fff", marginTop: 10 },
  paymentTitle: { fontSize: 18, fontWeight: "bold", marginBottom: 10 },
  paymentDriver: { fontSize: 16, marginBottom: 10 },
  paymentButton: {
    backgroundColor: "#007bff",
    padding: 10,
    marginVertical: 5,
    alignItems: "center",
    borderRadius: 5,
  },
  paymentText: { color: "#fff", fontSize: 16 },
});

export default ArrangeYourRide;
