import React from 'react';
import { View, Text, TextInput, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, icons, images, SIZES } from '../../../constants';

const DeliveryDetails = () => {
    const navigation = useNavigation();

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Image source={images.goback} style={styles.backIcon} />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>                      Delivery details</Text>
            </View>

            <View style={{ flex: 1, backgroundColor: COLORS.light_blue, margin: SIZES.base * 2, flexDirection: 'row' ,alignItems:'flex-start'}}>
                {/* Pickup Section */}
                <Image source={images.Longroute} style={styles.statusIcon} />
                <View>
                    <View style={styles.section}>

                        <View style={styles.iconRow}>
                            <Text style={styles.sectionTitle}>Pickup</Text>
                        </View>

                        <View style={styles.inputRow}>
                            <Image source={images.call} style={styles.icon} />
                            <Text style={styles.label}>Phone no and name</Text>
                        </View>
                        <TextInput style={styles.input} placeholder="Enter your phone no and name" placeholderTextColor={COLORS.grey} />

                        <View style={styles.inputRow}>
                            <Image source={icons.LocationIcon} style={styles.icon} />
                            <Text style={styles.location}>AMLI 7th Street Station...</Text>
                        </View>

                        <Text style={styles.label}>Product description</Text>
                        <TextInput
                            style={[styles.input, styles.textArea]}
                            placeholder="Enter product description to help driver identify the product easily"
                            placeholderTextColor={COLORS.grey}
                            multiline
                        />
                    </View>

                    {/* Dropoff Section */}
                    <View style={styles.section}>
                        <View style={styles.iconRow}>
                            <Image source={images.routeIndicator} style={styles.statusIcon} />
                            <Text style={styles.sectionTitle}>Dropoff</Text>
                        </View>

                        <View style={styles.inputRow}>
                            <Image source={images.call} style={styles.icon} />
                            <Text style={styles.label}>Phone no and name*</Text>
                        </View>
                        <TextInput style={styles.input} placeholder="Enter your phone no and name" placeholderTextColor={COLORS.grey} />

                        <View style={styles.inputRow}>
                            <Image source={images.locate} style={styles.icon} />
                            <Text style={styles.location}>AMLI 7th Street Station...</Text>
                        </View>
                    </View>

                    {/* Submit Button */}
                    <TouchableOpacity style={styles.button} onPress={() => navigation.navigate('Requestmade')}>
                        <Text style={styles.buttonText}>View delivery request</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>

    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: COLORS.light_blue, },
    header: { flexDirection: 'row', alignItems: 'center', paddingVertical: SIZES.base, backgroundColor: COLORS.white },
    backIcon: { width: 24, height: 24 },
    headerTitle: { ...FONTS.body3, marginLeft: SIZES.base * 2 },

    section: { backgroundColor: COLORS.light_blue, padding: SIZES.padding, borderRadius: SIZES.radius, marginBottom: SIZES.base * 2 },
    iconRow: { flexDirection: 'row', alignItems: 'flex-start', marginBottom: SIZES.base },
    statusIcon: { width: 20, height: '70%', marginRight: SIZES.base, resizeMode: 'contain', justifyContent: 'flex-end' },
    sectionTitle: { ...FONTS.body4, color: COLORS.black },

    inputRow: { flexDirection: 'row', alignItems: 'center', marginVertical: SIZES.base },
    icon: { width: 20, height: 20, marginRight: SIZES.base },
    label: { ...FONTS.body4, color: COLORS.tertiaryText },
    location: { ...FONTS.body4, color: COLORS.tertiaryText, marginVertical: 20 },

    input: { ...FONTS.body4, backgroundColor: COLORS.white, borderRadius: 7, padding: SIZES.base, color: COLORS.black, marginTop: SIZES.base },
    textArea: { height: 100, textAlignVertical: 'top' },

    button: { backgroundColor: COLORS.primary, paddingVertical: SIZES.base * 1.5,marginRight:20, alignItems: 'center', borderRadius: SIZES.radius, marginTop: SIZES.base * 2 },
    buttonText: { ...FONTS.h3, color: COLORS.white, },
});

export default DeliveryDetails;
