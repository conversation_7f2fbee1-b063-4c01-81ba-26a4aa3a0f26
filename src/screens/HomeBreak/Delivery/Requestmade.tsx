import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, ScrollView, Modal } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, images, SIZES } from '../../../constants';
import MapView from 'react-native-maps';

const Requestmade = () => {
  const navigation = useNavigation();
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);

  const openBottomSheet = () => setBottomSheetVisible(true);
  const closeBottomSheet = () => setBottomSheetVisible(false);

  return (
    <ScrollView style={styles.container}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
      {/* Header */}
      <View style={styles.header}>
      
        <Text style={styles.headerTitle}>Arrival date- 13th, July, 2000 10:00 PM</Text>
      <Text style={styles.carDetails}>Toyota Corolla | Black | EPE-546-633</Text>

      </View>


      {/* Delivery Details */}
      <View style={styles.deliveryCard}>
        <View style={styles.rowBetween}>
          <Text style={styles.sectionTitle}>Delivery details</Text>
          <TouchableOpacity>
            <Image source={images.more} style={styles.moreIcon} />
          </TouchableOpacity>
        </View>

        {/* Pickup Section */}
        <Text style={styles.subTitle}>Pickup</Text>
        <View style={styles.infoRow}>
          <Image source={images.call} style={styles.icon} />
          <View>
            <Text style={styles.name}>Samad Opabode</Text>
            <Text style={styles.phone}>08054065745</Text>
          </View>
        </View>
        <View style={styles.infoRow}>
          <Image source={images.locate} style={styles.icon} />
          <Text style={styles.location}>AMLI 7th Street Station...</Text>
        </View>
        <Text style={styles.description}>Product description</Text>
<View style={{backgroundColor:COLORS.border,height:1,marginVertical:10}}></View>
        {/* Dropoff Section */}
        <Text style={styles.subTitle}>Dropoff</Text>
        <View style={styles.infoRow}>
          <Image source={images.call} style={styles.icon} />
          <View>
            <Text style={styles.name}>Receiver phone no</Text>
            <Text style={styles.phone}>08054065745</Text>
          </View>
        </View>
        <View style={styles.infoRow}>
          <Image source={ images.locate} style={styles.icon} />
          <Text style={styles.location}>AMLI 7th Street Station...</Text>
        </View>
      </View>

      {/* Driver Section */}
      <View style={styles.driverCard}>
        <View style={styles.driverRow}>
          <Image source={images.driver1} style={styles.driverImage} />
          <View style={styles.driverInfo}>
            <Text style={styles.driverName}>Olive Rodrigo</Text>
            <View style={styles.ratingRow}>
              <Text style={styles.rating}>4.4</Text>
              {/* <Image source={require('../assets/star.png')} style={styles.starIcon} /> */}
              <Text style={styles.rides}>| 53 rides</Text>
            </View>
          </View>
          <Text style={styles.price}>$12.5</Text>
        </View>

        {/* Message and Call */}
        <View style={styles.contactRow}>
          <TouchableOpacity style={styles.messageButton} onPress={()=> navigation.navigate('ChatScreen')}>
            <Text style={styles.messageText}>Message Driver</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.callButton} onPress={()=> navigation.navigate('CallScreen')}>
            <Image source={images.call} style={styles.callIcon} />
            <Text style={styles.callText}>Call</Text>
          </TouchableOpacity>
        </View>
      </View>

      {
        isBottomSheetVisible && (
            <View style={{ flex: 1 }}>
            {/* Approve Button */}
            <TouchableOpacity style={styles.approveButton} onPress={openBottomSheet}>
              <Text style={styles.approveText}>Approve delivery request</Text>
            </TouchableOpacity>
      
            {/* Bottom Sheet */}
            <Modal
              animationType="slide"
              transparent={true}
              visible={isBottomSheetVisible}
              onRequestClose={closeBottomSheet}
            >
              <View style={styles.modalContainer}>
                <View style={styles.bottomSheet}>
                  {/* Header */}
                  <TouchableOpacity onPress={closeBottomSheet} style={styles.closeIcon}>
                    <Image source={images.goback} style={styles.backIcon} />
                  </TouchableOpacity>
                  <Text style={styles.sheetTitle}>Your delivery request has been made</Text>
      
                  {/* Map Container */}
                  <View style={styles.mapContainer}>
                  <MapView
                    style={styles.mapImage}
                    initialRegion={{
                      latitude: 8,
                      longitude: 4,
                      latitudeDelta: 0.0922,
                      longitudeDelta: 0.0421,
                    }}>
                    </MapView>
                  </View>
      
                  {/* Delivery Details */}
                  <View style={styles.deliveryDetails}>

<View style={{flexDirection:'row',justifyContent:'space-between',width:'100%'}}>
                    <Text style={styles.arrivalText}>Arrival date- 13th, July, 2000</Text>
                    <Text style={styles.price}>$12.5</Text>
                    </View>
                    {/* Pickup & Dropoff */}
                    <View style={styles.locationContainer}>

                    <Image source={images.routeIndicator} style={styles.dotIcon} />
<View>
                      <View style={styles.locationRow}>
                        <Text style={styles.locationText}>AMLI 7th Street Station.</Text>
                      </View>
                      <View style={styles.dashedLine} />
                      <View style={styles.locationRow}>
                        <Text style={styles.locationText}>AMLI 7th Street Station.</Text>
                      </View>
                    </View>
                    </View>
                  </View>
      
                  {/* Add Delivery Request Button */}
                  <TouchableOpacity style={styles.deliveryButton} onPress={()=> {
                    setBottomSheetVisible(false);
                    navigation.navigate('TabStack')}}>
                    <Text style={styles.deliveryButtonText}>Add delivery request</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Modal>
          </View>
        
      
        )
      }

      {/* Approve Button */}
      <TouchableOpacity style={styles.approveButton} onPress={()=> setBottomSheetVisible(true)}>
        <Text style={styles.approveText}>Approve delivery request</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.white, padding: SIZES.padding },
  header: { borderBottomWidth:0.5,borderColor:COLORS.border, alignItems: 'center', marginBottom: SIZES.base },
  backIcon: { width: 24, height: 24,  },
  headerTitle: { textAlign:'center',marginTop:20,fontSize: SIZES.body3, fontFamily: FONTS.h3.fontFamily, marginLeft: SIZES.base, color: COLORS.black },
  carDetails: {  textAlign:'center',fontFamily: FONTS.body5.fontFamily, color: COLORS.tertiaryText, marginBottom: SIZES.padding },

  deliveryCard: {borderWidth:0.5,borderColor:COLORS.border, backgroundColor: COLORS.white, padding: SIZES.padding, borderRadius: SIZES.radius, marginBottom: SIZES.base * 2 },
  rowBetween: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  sectionTitle: { fontSize: SIZES.body4, fontFamily: FONTS.h3.fontFamily, color: COLORS.black },
  moreIcon: { width: 10, height: 10, resizeMode:'contain' },

  subTitle: { fontSize: SIZES.body4, fontFamily: FONTS.h3.fontFamily, color: COLORS.black, marginTop: SIZES.base * 2 },
  infoRow: { flexDirection: 'row', alignItems: 'center', marginVertical: SIZES.base },
  icon: { width: 20, height: 20, marginRight: SIZES.base,resizeMode:"contain" },
  name: { fontSize: SIZES.body4, fontFamily: FONTS.body3.fontFamily, color: COLORS.black ,marginVertical:5},
  phone: { fontSize: SIZES.body4, color: COLORS.tertiaryText ,marginVertical:5},
  location: { fontSize: SIZES.body4, color: COLORS.tertiaryText ,marginVertical:5},
  description: { fontSize: SIZES.body4, color: COLORS.tertiaryText, fontStyle: 'italic' ,marginVertical:5},

  driverCard: { backgroundColor: COLORS.white, padding: SIZES.padding, borderRadius: SIZES.radius, marginBottom: SIZES.base * 2 },
  driverRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  driverImage: { width: 50, height: 50, borderRadius: 25 },
  driverInfo: { flex: 1, marginLeft: SIZES.base },
  driverName: { fontSize: SIZES.body4, fontFamily: FONTS.h3.fontFamily, color: COLORS.black },
  ratingRow: { flexDirection: 'row', alignItems: 'center', marginTop: SIZES.base / 2 },
  rating: { fontSize: SIZES.body3, fontWeight: 'bold' },
  starIcon: { width: 15, height: 15, marginHorizontal: 5 },
  rides: { fontSize: SIZES.body4, color: COLORS.tertiaryText },
  price: { fontSize: SIZES.h3, fontWeight: 'bold', color: COLORS.black },

  contactRow: { flexDirection: 'row', justifyContent: 'space-between', marginTop: SIZES.padding },
  messageButton: { flex: 1, backgroundColor: COLORS.light_blue, paddingVertical: SIZES.base, alignItems: 'center', borderRadius: SIZES.radius },
  messageText: { fontSize: SIZES.body4, color: COLORS.black },
  callButton: { flexDirection: 'row', alignItems: 'center', paddingVertical: SIZES.base, marginLeft: SIZES.base * 2 },
  callIcon: { width: 20, height: 20, marginRight: SIZES.base },
  callText: { fontSize: SIZES.body4, color: COLORS.black },

  approveButton: { backgroundColor: COLORS.primary, paddingVertical: SIZES.base * 1.5, alignItems: 'center', borderRadius: SIZES.radius, marginTop: SIZES.base * 2 },
  approveText: { fontSize: SIZES.h3, color: COLORS.white },
  approveButton: { backgroundColor: COLORS.primary, paddingVertical: SIZES.base * 1.5, alignItems: 'center', borderRadius: SIZES.radius, marginTop: SIZES.base * 2 },
  approveText: { fontSize: SIZES.h3, fontFamily: FONTS.h3.fontFamily, color: COLORS.white, fontWeight: 'bold' },

  modalContainer: { flex: 1, justifyContent: 'flex-end', backgroundColor: 'rgba(0, 0, 0, 0.5)' },
  bottomSheet: { backgroundColor: COLORS.white, borderTopLeftRadius: SIZES.radius * 2, borderTopRightRadius: SIZES.radius * 2, padding: SIZES.padding, alignItems: 'center' },
  
  closeIcon: { alignSelf: 'flex-start', marginBottom: SIZES.base },
  backIcon: { width: 24, height: 24 },

  sheetTitle: { fontSize: SIZES.h3, fontFamily: FONTS.h3.fontFamily, textAlign: 'center', color: COLORS.black, marginVertical: SIZES.base * 2 },

  mapContainer: { width: '100%', borderRadius: SIZES.radius, overflow: 'hidden', marginBottom: SIZES.base * 2 },
  mapImage: { width: '100%', height: 150, resizeMode: 'cover' },

  deliveryDetails: { backgroundColor: COLORS.white, width: '100%', padding: SIZES.base * 2, borderRadius: SIZES.radius, borderWidth: 0.5, borderColor: COLORS.border },
  arrivalText: { fontSize: SIZES.body4, fontFamily: FONTS.h3.fontFamily, color: COLORS.black },

  locationContainer: { marginTop: SIZES.base * 2, flexDirection: 'row' },
  locationRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SIZES.base,borderWidth:0.5,borderRadius:7,padding:12,width:280,borderColor:COLORS.border },
  dotIcon: { width: 16, height: '80%', marginRight: SIZES.base ,resizeMode:'contain'},
  locationText: { fontSize: SIZES.body4, fontFamily: FONTS.body3.fontFamily, color: COLORS.black },

  dashedLine: { height: 20, width: 1, backgroundColor: COLORS.border, alignSelf: 'center' },

  deliveryButton: { backgroundColor: COLORS.primary, paddingVertical: SIZES.base * 1.5, alignItems: 'center', borderRadius: SIZES.radius, marginTop: SIZES.base * 2, width: '100%' },
  deliveryButtonText: { fontSize: SIZES.h3, fontFamily: FONTS.h3.fontFamily, color: COLORS.white, fontWeight: 'bold' },

});

export default Requestmade;
