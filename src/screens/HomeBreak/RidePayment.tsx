import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme'; // Update your theme path
import { images } from '../../constants';
import MapView, { Circle, Marker } from 'react-native-maps';
import { useNavigation, useRoute } from '@react-navigation/native';
import socketService from '../../services/SocketService';
import RideDetails from '../Earnings/RideDetails';
import BackToHome from '../../components/Backbtn';

const RidePayment = () => {
  const route = useRoute();
  const response = route.params || {};
  console.log('isDrivers', JSON.stringify(response, null, 2));

  const [cardHolderName, setCardHolderName] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [cvv, setCvv] = useState('');
  const [expiryDate, setExpiryDate] = useState('');

  const [isBottomSheetVisible, setBottomSheetVisible] = useState(true);
  const [isBottomSheetPaymentVisible, setBottomSheetPaymentVisible] = useState(false);
  const [isAddCardModalVisible, setAddCardModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [newRideId, setNewRideId] = useState(response?.rideId);

  useEffect(() => {
    socketService.onEvent("payForRide", (response) => {
      console.log("✅ PAYMENT RESPONSE RECEIVED:", JSON.stringify(response, null, 2));
      setIsLoading(false);
      if (response.success) {
        Alert.alert("🎉 Payment Successful!", `  ${response.message}`);
        navigation.navigate("RideDetailScreen", { rideId: newRideId });

      } else {
        Alert.alert("❌ Payment Failed", `Try again.\n\nReason: ${response.message}`);
      }
    });

    return () => {
      socketService.socket.off("payForRide");
    };
  }, []);

  const handleRidePayment = (rideId, type) => {
    setIsLoading(true);

    const cardDetails = {
      cardHolderName: cardHolderName,
      cardNumber: cardNumber,
      cvv: cvv,
      expiryDate: expiryDate,
      cardType: "Visa",
    };


    let data;
    if (type === "card" ) {
      if (cardHolderName === "" || cardNumber === "" || cvv === "" || expiryDate === "") {
        Alert.alert("❌ Invalid Card Details", "Please fill in all the fields.");
        setIsLoading(false);
        return;
      }
      data = { rideId, paymentType: "card", cardId: "myCardId", cardDetails };
    } else if (type === "direct") {
      data = { rideId, paymentType: "direct", cardDetails };
    } else {
      data = { rideId, paymentType: "wallet" };
    }

    console.log("💳 Initiating Payment Request:", JSON.stringify(data, null, 2));
    socketService.emitEvent("payForRide", data);
  };





  const navigation = useNavigation();

  // Map region
  const [region, setRegion] = useState({
    latitude: 33.5186, // Birmingham latitude
    longitude: -86.8104, // Birmingham longitude
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  });
  const [radius, setRadius] = useState(30000); // Initial radius in meters (30km)


  return (
    <View style={styles.container}>
   {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />}

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>  Pay for the Ride</Text>
        <BackToHome />
      </View>



      <ScrollView contentContainerStyle={styles.scrollContent}>
        {(isBottomSheetVisible || isBottomSheetPaymentVisible) && (
          <View style={styles.overlay} />
        )}
        {/* Input Fields */}


        <MapView
          style={styles.map}
          region={region}
          onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
        >
          {/* Circle showing the search area */}
          <Circle
            center={{ latitude: region.latitude, longitude: region.longitude }}
            radius={radius} // Circle radius in meters
            strokeColor={COLORS.primary}
            fillColor="rgba(0, 122, 255, 0.2)"
          />
          {/* Marker in the center */}
          <Marker
            coordinate={{ latitude: region.latitude, longitude: region.longitude }}
            draggable
            onDragEnd={(e) => {
              const { latitude, longitude } = e.nativeEvent.coordinate;
              setRegion({ ...region, latitude, longitude });
            }}
          />
        </MapView>


      </ScrollView>

    


      {isBottomSheetVisible && (
        <View style={styles.bottomSheet}>
          <View style={styles.paymentModalHeader}>
            <Text style={styles.addCardModalTitle}>
            
              Set your payment method
            </Text>
            <Text style={styles.paymentModalSubtitle}>  {response?.message}
            </Text>

          </View>

          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.paymentOption}
            // onPress={() => handleRidePayment(newRideId, "card")}>
            onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.paypal} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Pay with Card</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>

          {/* <TouchableOpacity style={styles.paymentOption} onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.Applepay} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Apple Pay</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity> */}

          <TouchableOpacity style={styles.paymentOption}
            onPress={() => handleRidePayment(newRideId, "wallet")}>
            {/* // onPress={() => setAddCardModalVisible(true)}> */}
            <Image source={images.masterCard} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Wallet</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>

          {/* <TouchableOpacity style={styles.paymentOption} onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.creditcard} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Debit or credit card</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity> */}
        </View>
      )}

      {isAddCardModalVisible && (
        <View style={styles.bottomSheet}>
          <View style={styles.addCardModalHeader}>
            <Text style={styles.addCardModalTitle}>Add payment method</Text>
            <Text style={styles.addCardModalSubtitle}>
              Update your card details.
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setAddCardModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>


          <View style={styles.inputContainer1}>
            <Text style={styles.inputLabel}>Name on card</Text>
            <TextInput
              style={[styles.input, { width: '100%' }]}
              placeholder="Olivia Rhye"
              value={cardHolderName}
              placeholderTextColor={COLORS.grey}
              onChangeText={setCardHolderName}
            />
          </View>

          <View style={styles.inputContainer1}>
            <Text style={styles.inputLabel}>Card number</Text>
            <View style={styles.cardNumberInput}>
              <Image source={images.masterCard} style={styles.cardIcon} />
              <TextInput
                style={[styles.input, { width: '90%' }]}
                placeholder="1234 1234 1234 1234"
                keyboardType="number-pad"
                placeholderTextColor={COLORS.grey}
                value={cardNumber}
                onChangeText={setCardNumber}
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.inputContainer1}>
              <Text style={styles.inputLabel}>Expiry</Text>
              <TextInput
                style={[styles.input, { width: 150, marginRight: 50 }]}
                placeholder="06 / 2024"
                value={expiryDate}
                placeholderTextColor={COLORS.grey}
                onChangeText={setExpiryDate}
              />
            </View>
            <View style={styles.inputContainer1}>
              <Text style={styles.inputLabel}>CVV</Text>
              <TextInput
                style={[styles.input, { width: 150 }]}
                placeholder="..."
                secureTextEntry
                placeholderTextColor={COLORS.grey}
                value={cvv}
                onChangeText={setCvv}
              />
            </View>
          </View>




          <TouchableOpacity style={styles.addButton}
            // onPress={() => navigation.navigate('RideDetailScreen')}>

            onPress={() => handleRidePayment(newRideId, "card")}>
            <Text style={styles.addButtonText}>Add</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.cancelButton} onPress={() => setAddCardModalVisible(false)}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          {/* <View style={styles.saveCardContainer}>
            <TouchableOpacity style={styles.checkbox} />
            <Text style={styles.saveCardText}>Save Card</Text>
          </View> */}
        </View>
      )}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },

  addCardModalHeader: {
    marginBottom: 20,
    alignItems: 'center',
  },
  addCardModalTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  addCardModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  inputContainer: {
    marginBottom: 15,
    flexDirection: 'row'
  },
  inputContainer1: {
    marginBottom: 15,
    // flexDirection:'row'
  },
  inputLabel: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  // input: {
  //   backgroundColor: COLORS.light_blue,
  //   borderRadius: SIZES.radius,
  //   padding: 20,
  //   ...FONTS.body3,
  //   fontSize:22,
  //   color: COLORS.black,
  // },
  cardNumberInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    paddingHorizontal: 10,
  },
  cardIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  row: {
    // width: 100,
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  addButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    marginTop: 20,
  },
  addButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  cancelButton: {
    marginTop: 10,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
  },
  cancelButtonText: {
    ...FONTS.body3,
    color: COLORS.grey,
  },
  saveCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  checkbox: {
    width: 20,
    marginTop: 10,

    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginRight: 10,
  },
  saveCardText: {
    ...FONTS.body4,
    color: COLORS.grey,
  },

  paymentModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  paymentModalHeader: {
    marginBottom: 20,
    marginHorizontal: 40,
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    padding: 10,
  },
  paymentModalTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 8,
  },
  paymentModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  // closeButton: {
  //   position: 'absolute',
  //   top: 30,
  //   right: 30,
  // },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  paymentIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  paymentOptionText: {
    flex: 1,
    ...FONTS.body3,
    fontSize: 14,
    marginLeft: 10,
    color: COLORS.black,
  },
  nextIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.grey,
    resizeMode: 'contain',
  },

  selectedDriverCard: {
    borderWidth: 0.5,
    backgroundColor: COLORS.light_blue,
    borderColor: COLORS.primary, // Highlight border for selected driver
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  paymentIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    borderRadius: SIZES.radius,
  },
  paymentButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    marginLeft: 10,
    paddingVertical: 12,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paymentButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.7,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body4,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    // padding: SIZES.padding,
  },

  inputRow: {},
  routeIndicator: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft: -20,
  },
  input1: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: '100%',
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  bottomSheet: {
    height: Dimensions.get('window').height * 0.6,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white, // Overlay color with transparency
    padding: SIZES.padding,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1, // Ensure it appears above other content
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  bottomSheetSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    paddingBottom: 20,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: SIZES.radius,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    resizeMode: 'contain',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  driverDetails: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
  },
  driverRatingPrice: {
    alignItems: 'flex-end',
    fontSize: 12,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default RidePayment;