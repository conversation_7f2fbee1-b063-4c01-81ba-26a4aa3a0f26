import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../../constants/theme'; // Update your theme path
import { images } from '../../../constants';
import { useNavigation } from '@react-navigation/native';
import GooglePlacesAutocompleteComponent from '../../../components/shared/GooglePlaceAutoComplete';
import MapView, { Marker, Polyline } from 'react-native-maps';
import Geolocation from '@react-native-community/geolocation';
const ArrangeReserveRide = () => {
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [isBottomSheetPaymentVisible, setBottomSheetPaymentVisible] = useState(false);
 
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [destination, setDestination] = useState(null);
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false); // Searching for drivers
  const [currentLocation, setCurrentLocation] = useState(null);
  const [selectedView, setSelectedView] = useState('recent'); // 'recent' or 'map'


  const handleContinue = () => {
    setBottomSheetVisible(true);
    setBottomSheetPaymentVisible(true);
  };

  const handleConfirmPickup = () => {
    setBottomSheetVisible(false);
    setBottomSheetPaymentVisible(true);

    // navigation.navigate('RideType');
  };

  useEffect(() => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setCurrentLocation({ latitude, longitude });
      },
      (error) => {
        console.error('Error getting current location:', error);
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  }, []);

  return (
    <View style={styles.container}>
      {/* Header */}


      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>                          Reserve Your Ride</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>

        {(isBottomSheetVisible || isBottomSheetPaymentVisible) && (
          <View style={styles.overlay} />
        )}
        {/* Input Fields */}
        <View style={styles.inputContainer}>
          <Image source={images.routeIndicator} style={styles.routeIndicator} />

          <View style={styles.inputRow}>


            <GooglePlacesAutocompleteComponent
              //  style={styles.input}
              placeholder="Select your pickup location"
              onSelect={(location) => setSelectedLocation(location)}
            />

            <GooglePlacesAutocompleteComponent
              //  style={styles.input}
              placeholder="Where to?"
              onSelect={(location) => setDestination(location)}
            />

          </View>
          <TouchableOpacity>
            <Image source={images.add} style={styles.addIcon} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.locationCard}>
          <Image source={images.locate1} style={styles.locationIcon} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationTitle}>Saved Places</Text>
          </View>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>
       
        <TouchableOpacity
  style={styles.locationCard}
  onPress={() => setSelectedView(selectedView === 'map' ? 'recent' : 'map')}
>
  <Image source={images.locate1} style={styles.locationIcon} />
  <View style={styles.locationTextContainer}>
    <Text style={styles.locationTitle}>Select Location via Map</Text>
  </View>
  <Image source={images.next} style={styles.nextIcon} />
</TouchableOpacity>



{selectedView === 'map' ? (
  <MapView
    style={styles.map}
    region={currentLocation ? { ...currentLocation, latitudeDelta: 0.05, longitudeDelta: 0.05 } : [8.3792, 4.5244]}
  >
    {currentLocation && <Marker coordinate={currentLocation} title="Current Location" />}
    {selectedLocation && <Marker coordinate={selectedLocation} title="Selected Location" />}
    {destination && <Marker coordinate={destination} title="Destination" />}
    {currentLocation && destination && (
      <Polyline coordinates={[currentLocation, destination]} strokeWidth={5} strokeColor={COLORS.primary} />
    )}
  </MapView>
) : (
  <View>
    {/* Close by Pickups */}
    <Text style={styles.sectionTitle}>Close by Pickups</Text>
    {Array(2)
      .fill(null)
      .map((_, index) => (
        <TouchableOpacity key={index} style={styles.locationCard}>
          <Image source={images.locate1} style={styles.locationIcon} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationTitle}>AMLI 7th Street Station.</Text>
            <Text style={styles.locationSubtitle}>
              2601 West 7th St. Fort Worth, Texas
            </Text>
          </View>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>
      ))}

    {/* Recent Search */}
    <Text style={styles.sectionTitle}>Recent Search</Text>
    {Array(2)
      .fill(null)
      .map((_, index) => (
        <TouchableOpacity key={index} style={styles.locationCard}>
          <Image source={images.locate} style={styles.locationIcon} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationTitle}>AMLI 7th Street Station.</Text>
            <Text style={styles.locationSubtitle}>
              2601 West 7th St. Fort Worth, Texas
            </Text>
          </View>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>
      ))}
  </View>
)}


      </ScrollView>

 
      <TouchableOpacity
        style={[styles.continueButtonn]}
          onPress={()=>navigation.navigate('PickDate')}

        disabled={loading}
      >

        <Text style={styles.continueButtonText}>Pick a Date and Time </Text>
      </TouchableOpacity>

     

    </View>
  );
};

const styles = StyleSheet.create({
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  continueButton: {
    position: "static",
    width: 350,
    bottom: 20,
    alignSelf: 'center',
    paddingHorizontal: 0,
    marginTop: 20,
    backgroundColor: COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    // alignItems: 'center',
  },
  continueButtonn: {
    position: "static",
    width: 350,
    bottom: 20,
    alignSelf: 'center',
    paddingHorizontal: 0,
    marginTop: 20,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    // alignItems: 'center',
  },
  continueButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
    textAlign: 'center',
  },
  continueButtonTextt: {
    ...FONTS.h3,
    color: COLORS.primary,
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  
  scrollContent: {
    padding: SIZES.padding,
  },
  rideTypeContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  rideTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.white,
    marginHorizontal: 5,
    width: 100,
  },
  activeButton: {
    backgroundColor: COLORS.primary,
    width: 100,
  },
  buttonText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginLeft: 5,
  },
  activeText: {
    color: COLORS.white,
  },
  icon: {
    width: 20,
    height: 20,
  },
  inputContainer: {
    marginBottom: 20,
    flexDirection: 'row',
  },
  
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  inputRow: {
    width: '70%',
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  sectionTitle: {
    ...FONTS.h4,
    color: COLORS.primary,
    fontSize: 14,
    marginVertical: 10,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    marginVertical: 5,
  },
  locationIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationTitle: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  locationSubtitle: {
    ...FONTS.body4,
    fontSize: 10,
    color: COLORS.grey,
  },
  nextIcon: {
    width: 10,
    height: 10,
  },



  addCardModalHeader: {
    marginBottom: 20,
    alignItems: 'center',
  },
  addCardModalTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  addCardModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  inputContainer: {
    marginBottom: 15,
    flexDirection: 'row'
  },
  inputContainer1: {
    marginBottom: 15,
    // flexDirection:'row'
  },
  inputLabel: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  // input: {
  //   backgroundColor: COLORS.light_blue,
  //   borderRadius: SIZES.radius,
  //   padding: 20,
  //   ...FONTS.body3,
  //   fontSize:22,
  //   color: COLORS.black,
  // },
  cardNumberInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    paddingHorizontal: 10,
  },
  cardIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  row: {
    // width: 100,
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  addButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    marginTop: 20,
  },
  addButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  cancelButton: {
    marginTop: 10,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
  },
  cancelButtonText: {
    ...FONTS.body3,
    color: COLORS.grey,
  },
  saveCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  checkbox: {
    width: 20,
    marginTop: 10,

    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginRight: 10,
  },
  saveCardText: {
    ...FONTS.body4,
    color: COLORS.grey,
  },

  paymentModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  paymentModalHeader: {
    marginBottom: 20,
    marginHorizontal: 40,
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    padding: 10,
  },
  paymentModalTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 8,
  },
  paymentModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  // closeButton: {
  //   position: 'absolute',
  //   top: 30,
  //   right: 30,
  // },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  paymentIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  paymentOptionText: {
    flex: 1,
    ...FONTS.body3,
    fontSize: 14,
    marginLeft: 10,
    color: COLORS.black,
  },
  nextIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.grey,
    resizeMode: 'contain',
  },

  selectedDriverCard: {
    borderWidth: 0.5,
    backgroundColor: COLORS.light_blue,
    borderColor: COLORS.primary, // Highlight border for selected driver
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  paymentIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    borderRadius: SIZES.radius,
  },
  paymentButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    marginLeft: 10,
    paddingVertical: 12,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paymentButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    padding: SIZES.padding,
  },

  inputRow: {},
  routeIndicator: {
    width: 60,
    height: 100,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft: -20,
  },
  input1: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: '100%',
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  bottomSheet: {
    height: Dimensions.get('window').height * 0.7,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white, // Overlay color with transparency
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1, // Ensure it appears above other content
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  bottomSheetSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    paddingBottom: 20,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: SIZES.radius,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    resizeMode: 'contain',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  driverDetails: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
  },
  driverRatingPrice: {
    alignItems: 'flex-end',
    fontSize: 12,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default ArrangeReserveRide;

