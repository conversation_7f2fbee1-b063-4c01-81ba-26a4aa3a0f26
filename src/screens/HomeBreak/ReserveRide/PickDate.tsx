import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, Platform, ScrollView } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Calendar } from 'react-native-calendars';
import DateTimePicker from '@react-native-community/datetimepicker';
import { COLORS, FONTS, SIZES, images } from '../../../constants';

const PickDate = () => {
  const navigation = useNavigation();
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(new Date());
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [isAM, setIsAM] = useState(true);

  const onDateSelect = (day) => {
    setSelectedDate(day.dateString);
  };
  const route = useRoute();
  const { rideType } = route.params || {};
  const onTimeChange = (event, selected) => {
    if (selected) {
      setSelectedTime(selected);
      setShowTimePicker(false);
    }
  };
  console.log(selectedTime, selectedDate, 'selectedTime');
  const handleReserveRide = () => {
    const formattedTime = selectedTime.getHours().toString().padStart(2, '0') + ':' + selectedTime.getMinutes().toString().padStart(2, '0');
    const body = {
      scheduleDate: selectedDate,
      scheduleTime: formattedTime,
      rideType: rideType,
    };
  console.log(body, 'body');
  
    navigation.navigate('ArrangeYourRide', body);
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
         {Platform.OS === 'ios' && <View style={{ marginTop: 40 }} />}     
         {Platform.OS === 'android' && <View style={{ marginTop: 40 }} />}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>                    Reserve ride     </Text>
      </View>

      {/* Calendar */}
      <View style={styles.calendarContainer}>
        <Calendar
          current={new Date().toISOString().split('T')[0]}
          minDate={new Date().toISOString().split('T')[0]}
          maxDate={new Date(Date.now() + 120 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
          onDayPress={onDateSelect}
          markedDates={selectedDate ? { [selectedDate]: { selected: true, selectedColor: COLORS.primary } } : {}}
          theme={{
            textSectionTitleColor: COLORS.black,
            selectedDayBackgroundColor: COLORS.primary,
            selectedDayTextColor: COLORS.white,
            todayTextColor: COLORS.primary,
            arrowColor: COLORS.primary,
            monthTextColor: COLORS.black,
          }}
        />


        {/* Time Picker */}
        <View style={styles.timePickerContainer}>
          <Text style={styles.timeLabel}>Time</Text>
          <TouchableOpacity style={styles.timeBox} onPress={() => setShowTimePicker(true)}>
            <Text style={styles.timeText}>{selectedTime.getHours()}:{selectedTime.getMinutes().toString().padStart(2, '0')}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.ampmBox, isAM && styles.activeAmPm]} onPress={() => setIsAM(true)}>
            <Text style={[styles.ampmText, isAM && styles.activeAmPmText]}>AM</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.ampmBox, !isAM && styles.activeAmPm]} onPress={() => setIsAM(false)}>
            <Text style={[styles.ampmText, !isAM && styles.activeAmPmText]}>PM</Text>
          </TouchableOpacity>
        </View>
      </View>
      {/* Time Picker Modal (For Android & iOS) */}
      {showTimePicker && (
        <DateTimePicker
          value={selectedTime}
          mode="time"
          display="spinner"
          is24Hour={false}
          onChange={onTimeChange}
        />
      )}
      {/* Ride Info */}
      <View style={styles.infoContainer}>
        <View style={styles.infoRow}>
          <Image source={images.carr} style={styles.icon} />
          <Text style={styles.infoText}>
            Choose your exact pickup date up to 120 days in advance
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Image source={images.carr} style={styles.icon} />
          <Text style={styles.infoText}>
            Take your time, a driver will be waiting for you
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Image source={images.carr} style={styles.icon} />
          <Text style={styles.infoText}>
            Cancellation of ride, absent - no charges up to 60 minutes before the ride
          </Text>
        </View>
      </View>

      {/* Reserve Button */}
      <TouchableOpacity style={styles.reserveButton}
        onPress={() => handleReserveRide() }>
        <Text style={styles.buttonText}>Reserve a ride</Text>
      </TouchableOpacity>


    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.light_blue, paddingTop: SIZES.padding },

  header: { flexDirection: 'row', alignItems: 'center', marginBottom: SIZES.base },
  backIcon: { width: 24, height: 24, marginLeft: SIZES.padding },
  headerTitle: { flex: 1, fontSize: SIZES.h3, fontFamily: FONTS.h3.fontFamily, color: COLORS.black },

  calendarContainer: { backgroundColor: COLORS.white, borderRadius: SIZES.radius, padding: SIZES.padding, marginHorizontal: SIZES.padding, marginBottom: SIZES.padding },

  timePickerContainer: { flexDirection: 'row', alignItems: 'center', marginTop: SIZES.padding, justifyContent: 'space-between' },
  timeLabel: { fontSize: SIZES.body3, fontFamily: FONTS.h3.fontFamily, color: COLORS.black },
  timeBox: { backgroundColor: COLORS.white, paddingHorizontal: SIZES.base * 2, paddingVertical: SIZES.base, borderRadius: SIZES.radius, borderWidth: 0.5, borderColor: COLORS.border },
  timeText: { fontSize: SIZES.body3, fontFamily: FONTS.body3.fontFamily, color: COLORS.black },

  ampmBox: { paddingHorizontal: SIZES.base * 2, paddingVertical: SIZES.base, borderRadius: SIZES.radius, borderWidth: 0.5, borderColor: COLORS.border },
  activeAmPm: { backgroundColor: COLORS.primary },
  ampmText: { fontSize: SIZES.body3, fontFamily: FONTS.body3.fontFamily, color: COLORS.black },
  activeAmPmText: { color: COLORS.white },

  infoContainer: { paddingHorizontal: SIZES.padding, paddingVertical: SIZES.base },
  infoRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SIZES.base * 2 },
  icon: { width: 20, height: 20, marginRight: SIZES.base },
  infoText: { fontSize: SIZES.body4, fontFamily: FONTS.body3.fontFamily, color: COLORS.black, flex: 1 },

  reserveButton: { backgroundColor: COLORS.primary, paddingVertical: SIZES.base * 1.5, alignItems: 'center', borderRadius: SIZES.radius, marginHorizontal: SIZES.padding, marginTop: SIZES.base * 3 },
  buttonText: { fontSize: SIZES.h3, fontFamily: FONTS.h3.fontFamily, color: COLORS.white, fontWeight: 'bold' },
});

export default PickDate;
