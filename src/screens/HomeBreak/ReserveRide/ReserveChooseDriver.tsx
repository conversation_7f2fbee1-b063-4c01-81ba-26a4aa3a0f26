import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../../constants/theme'; // Update your theme path
import { images } from '../../../constants';
import MapView, { Circle, Marker } from 'react-native-maps';
import { useNavigation } from '@react-navigation/native';

const ReserveChooseDriver = () => {
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(true);
  const [isBottomSheetPaymentVisible, setBottomSheetPaymentVisible] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState(null); // Add state to track selected driver
  const [isAddCardModalVisible, setAddCardModalVisible] = useState(false);

  const handleContinue = () => {
    setBottomSheetVisible(true);
    setBottomSheetPaymentVisible(true);
  };

  const handleConfirmPickup = () => {
    // setBottomSheetVisible(false);
    setBottomSheetPaymentVisible(true);

    // navigation.navigate('RideType');
  };

  const navigation = useNavigation();

  // Map region
  const [region, setRegion] = useState({
    latitude: 33.5186, // Birmingham latitude
    longitude: -86.8104, // Birmingham longitude
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  });
  const [radius, setRadius] = useState(30000); // Initial radius in meters (30km)

  return (
    <View style={styles.container}>

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>                     Searching For Driver</Text>
      </View>



      <ScrollView contentContainerStyle={styles.scrollContent}>
        {(isBottomSheetVisible || isBottomSheetPaymentVisible) && (
          <View style={styles.overlay} />
        )}
        {/* Input Fields */}
        <View style={styles.inputContainer}>
          <Image source={images.routeIndicator} style={styles.routeIndicator} />

          <View style={styles.inputRow}>
            <TextInput
              placeholder="AMLI 7th Street Station."
              placeholderTextColor={COLORS.black}
              style={styles.input1}
            />

            <TextInput
              placeholder="Where to ?"
              placeholderTextColor={COLORS.black}
              style={styles.input1}
            />
          </View>
          <TouchableOpacity>
            <Image source={images.add} style={styles.addIcon} />
          </TouchableOpacity>
        </View>

        <MapView
          style={styles.map}
          region={region}
          onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
        >
          {/* Circle showing the search area */}
          <Circle
            center={{ latitude: region.latitude, longitude: region.longitude }}
            radius={radius} // Circle radius in meters
            strokeColor={COLORS.primary}
            fillColor="rgba(0, 122, 255, 0.2)"
          />
          {/* Marker in the center */}
          <Marker
            coordinate={{ latitude: region.latitude, longitude: region.longitude }}
            draggable
            onDragEnd={(e) => {
              const { latitude, longitude } = e.nativeEvent.coordinate;
              setRegion({ ...region, latitude, longitude });
            }}
          />
        </MapView>


      </ScrollView>

      {isBottomSheetVisible && (
        <View style={styles.bottomSheet}>
          <Text style={styles.bottomSheetTitle}>Choose a driver</Text>
          <Text style={styles.bottomSheetSubtitle}>Prices are given by the driver</Text>

          <ScrollView showsVerticalScrollIndicator={false}>
            {[1, 2, 3, 2].map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.driverCard,
                  selectedDriver === index && styles.selectedDriverCard, // Apply border style for selected driver
                ]}
                onPress={() => setSelectedDriver(index)} // Update selected driver on press
              >
                <Image source={images[`driver${item}`]} style={styles.driverImage} />
                <View style={styles.driverInfo}>
                  <Text style={styles.driverName}>Olive Rodrigo</Text>
                  <Text style={styles.driverDetails}>10 min  ·  9:45pm</Text>
                </View>
                <View style={styles.driverRatingPrice}>
                  <Text style={styles.driverRating}>4.4 ★ | 53 rides</Text>
                  <Text style={styles.driverPrice}>$12.5</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
          {selectedDriver !== null && (
            <View style={styles.paymentContainer}>
              <TouchableOpacity style={{}}>
                <Image source={images.carRide} style={styles.paymentIcon} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.paymentButton} onPress={handleConfirmPickup}>
                <Text style={styles.paymentButtonText}>Make payment →</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}

      {isBottomSheetPaymentVisible && (
        <View style={styles.bottomSheet}>
          <View style={styles.paymentModalHeader}>
            <Text style={styles.addCardModalTitle}>
              Add payment method before choosing drive
            </Text>
            <Text style={styles.paymentModalSubtitle}>
              Set your payment method
            </Text>

          </View>

          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setBottomSheetPaymentVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.paymentOption}   onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.paypal} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>PayPal</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>

          <TouchableOpacity style={styles.paymentOption}   onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.Applepay} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Apple Pay</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>

          <TouchableOpacity style={styles.paymentOption}   onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.masterCard} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Discover</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>

          <TouchableOpacity style={styles.paymentOption}   onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.creditcard} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Debit or credit card</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>
        </View>
      )}

      {isAddCardModalVisible && (
        <View style={styles.bottomSheet}>
          <View style={styles.addCardModalHeader}>
            <Text style={styles.addCardModalTitle}>Add payment method</Text>
            <Text style={styles.addCardModalSubtitle}>
              Update your card details.
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setAddCardModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer1}>
            <View>
            <Text style={styles.inputLabel}>Name on card</Text>
            </View>
            <View>
            <TextInput style={[styles.input,{width:'100%'}]} placeholder="Olivia Rhye" />
         
         </View>
          </View>

          <View style={styles.inputContainer1}>
            <Text style={styles.inputLabel}>Card number</Text>
            <View style={styles.cardNumberInput}>
              <Image source={images.masterCard} style={styles.cardIcon} />
              <TextInput
                style={[styles.input, { width:'90%' }]}
                placeholder="1234 1234 1234 1234"
                keyboardType="number-pad"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.inputContainer1}>
              <Text style={styles.inputLabel}>Expiry</Text>
              <TextInput style={[styles.input,{width:150, marginRight:50}]} placeholder="06 / 2024" />
            </View>
            <View style={styles.inputContainer1}>
              <Text style={styles.inputLabel}>CVV</Text>
              <TextInput style={[styles.input,{width:150}]} placeholder="..." secureTextEntry />
            </View>
          </View>

          <TouchableOpacity style={styles.addButton} onPress={()=> navigation.navigate('ApproveReserve')}>
            <Text style={styles.addButtonText}>Add</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <View style={styles.saveCardContainer}>
            <TouchableOpacity style={styles.checkbox} />
            <Text style={styles.saveCardText}>Save Card</Text>
          </View>
        </View>
      )}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  
  addCardModalHeader: {
    marginBottom: 20,
    alignItems: 'center',
  },
  addCardModalTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  addCardModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  inputContainer: {
    marginBottom: 15,
    flexDirection:'row'
  },
  inputContainer1: {
    marginBottom: 15,
    // flexDirection:'row'
  },
  inputLabel: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  // input: {
  //   backgroundColor: COLORS.light_blue,
  //   borderRadius: SIZES.radius,
  //   padding: 20,
  //   ...FONTS.body3,
  //   fontSize:22,
  //   color: COLORS.black,
  // },
  cardNumberInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    paddingHorizontal: 10,
  },
  cardIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  row: {
    // width: 100,
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  addButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    marginTop: 20,
  },
  addButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  cancelButton: {
    marginTop: 10,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
  },
  cancelButtonText: {
    ...FONTS.body3,
    color: COLORS.grey,
  },
  saveCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  checkbox: {
    width: 20,
    marginTop: 10,

    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginRight: 10,
  },
  saveCardText: {
    ...FONTS.body4,
    color: COLORS.grey,
  },

  paymentModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  paymentModalHeader: {
    marginBottom: 20,
    marginHorizontal: 40,
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    padding: 10,
  },
  paymentModalTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 8,
  },
  paymentModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  // closeButton: {
  //   position: 'absolute',
  //   top: 30,
  //   right: 30,
  // },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  paymentIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  paymentOptionText: {
    flex: 1,
    ...FONTS.body3,
    fontSize: 14,
    marginLeft: 10,
    color: COLORS.black,
  },
  nextIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.grey,
    resizeMode: 'contain',
  },

  selectedDriverCard: {
    borderWidth: 0.5,
    backgroundColor: COLORS.light_blue,
    borderColor: COLORS.primary, // Highlight border for selected driver
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  paymentIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    borderRadius: SIZES.radius,
  },
  paymentButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    marginLeft: 10,
    paddingVertical: 12,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paymentButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    padding: SIZES.padding,
  },
  
  inputRow: {},
  routeIndicator: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft: -20,
  },
  input1: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
   },  
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: '100%',
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  bottomSheet: {
    height: Dimensions.get('window').height * 0.7,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white, // Overlay color with transparency
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1, // Ensure it appears above other content
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  bottomSheetSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    paddingBottom: 20,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: SIZES.radius,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    resizeMode: 'contain',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  driverDetails: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
  },
  driverRatingPrice: {
    alignItems: 'flex-end',
    fontSize: 12,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default ReserveChooseDriver;