import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, SIZES, images } from '../../../constants';

const ReserveRide = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Reserve ride</Text>
      </View>

      {/* Image Section */}
      {/* <View style={styles.imageContainer}> */}
        <Image source={images.reserveimage} style={styles.illustration} />
      {/* </View> */}

      {/* Information Section */}
      <View style={styles.infoContainer}>
        <View style={styles.infoRow}>
          <Image source={images.carr} style={styles.icon} />
          <Text style={styles.infoText}>
            Choose your exact pickup date up to 120 days in advance
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Image source={images.Search} style={styles.icon} />
          <Text style={styles.infoText}>
            Take your time, a driver will be waiting for you
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Image source={images.carr} style={styles.icon} />
          <Text style={styles.infoText}>
            Cancellation of ride, absent - no charges up to 60 minutes before the ride
          </Text>
        </View>
      </View>

      {/* Button */}
      <TouchableOpacity 
      style={styles.reserveButton}
      onPress={() => navigation.navigate('ArrangeReserveRide')}
      >
        <Text style={styles.buttonText}>Reserve a ride</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.light_blue, paddingTop: SIZES.padding },
  header: { flexDirection: 'row', alignItems: 'center', marginBottom: SIZES.base },
  backIcon: { width: 24, height: 24, marginLeft: SIZES.padding },
  headerTitle: { flex: 1, textAlign: 'center',   ...FONTS.body3, color: COLORS.black },
  imageContainer: { alignItems: 'center', width:SIZES.width ,borderRadius: SIZES.radius },
  illustration: { width: SIZES.width, height: SIZES.height * 0.3, resizeMode: 'cover', borderRadius: 10 },
  infoContainer: { paddingHorizontal: SIZES.padding, paddingVertical: SIZES.base,marginTop: SIZES.base * 2 },
  infoRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SIZES.base * 2 },
  icon: { width: 20, height: 20, marginRight: SIZES.base },
  infoText: { fontSize: SIZES.body4, fontFamily: FONTS.body3.fontFamily, color: COLORS.black, flex: 1 },
  reserveButton: { backgroundColor: COLORS.primary, paddingVertical: SIZES.base * 1.5, alignItems: 'center', borderRadius: SIZES.radius, marginHorizontal: SIZES.padding, marginTop: SIZES.base * 3 },
  buttonText: { fontSize: SIZES.h3, fontFamily: FONTS.h3.fontFamily, color: COLORS.white, fontWeight: 'bold' },
});

export default ReserveRide;
