import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, Platform, Alert } from 'react-native';
import { StreamVideo, StreamCall } from '@stream-io/video-react-native-sdk';
import { useNavigation, useRoute } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useCall, CALL_STATES } from '../../contexts/CallContext';
import CallControlsComponent from '../../components/CallControls';

export default function CallScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { callState, startCall: initiateCall, endCall } = useCall();

  // Get ride details, driver details, and call data from navigation params
  const { rideDetails, driverDetails, callData } = route.params || {};

  console.log('📱 CallScreen params:', { rideDetails, driverDetails, callData });

  const {
    callStatus,
    currentCall,
    streamClient,
    callType,
    isCallActive,
    error
  } = callState;

  console.log('📱 CallScreen state:', {
    callStatus,
    hasCurrentCall: !!currentCall,
    hasStreamClient: !!streamClient,
    callType,
    isCallActive
  });

  // Handle error display
  useEffect(() => {
    if (error) {
      Alert.alert('Call Error', error);
    }
  }, [error]);

  // Handle navigation back when call ends
  useEffect(() => {
    if (callStatus === CALL_STATES.ENDED || callStatus === CALL_STATES.REJECTED) {
      const timer = setTimeout(() => {
        navigation.goBack();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [callStatus, navigation]);

  // Start call function
  const handleStartCall = async (selectedCallType = 'voice') => {
    try {
      const rideId = rideDetails?.rideId;
      if (!rideId) {
        Alert.alert('Error', 'No ride information available');
        return;
      }

      await initiateCall(rideId, selectedCallType);
    } catch (error) {
      console.error('Error starting call:', error);
      Alert.alert('Error', 'Failed to start call. Please try again.');
    }
  };

  // End call function
  const handleEndCall = async () => {
    try {
      await endCall();
    } catch (error) {
      console.error('Error ending call:', error);
      navigation.goBack();
    }
  };

  // Render different UI based on call state
  const renderCallInterface = () => {

    console.log('Driver Details:', driverDetails);
    console.log('Ride Details Driver:', rideDetails?.driver);

    // Use driverDetails if available, otherwise fallback to rideDetails
    const driverInfo = {
      name: driverDetails?.firstName && driverDetails?.lastName
        ? `${driverDetails.firstName} ${driverDetails.lastName}`
        : driverDetails?.name
        || rideDetails?.driver?.name
        || 'Driver',
      profileImg: driverDetails?.profileImg
        || rideDetails?.driver?.profileImg
        || 'https://i.pravatar.cc/300',
      phone: driverDetails?.phoneNumber
        || driverDetails?.phone
        || rideDetails?.driver?.phone
        || 'Unknown'
    };

    switch (callStatus) {
      case CALL_STATES.RINGING:
        return (
          <View style={styles.callingContainer}>
            <View style={styles.profileContainer}>
              <Text style={styles.callerName}>Calling...</Text>
              <Image
                source={{ uri: driverInfo.profileImg }}
                style={styles.profileImg}
              />
              <Text style={styles.callStatus}>{driverInfo.name}</Text>
              <Text style={styles.phoneNumber}>{driverInfo.phone}</Text>
            </View>
            <TouchableOpacity
              style={[styles.actionButton, styles.rejectButton]}
              onPress={handleEndCall}
            >
              <Ionicons name="call" size={30} color="white" />
            </TouchableOpacity>
          </View>
        );

      case CALL_STATES.CONNECTED:
        return (
          <View style={styles.callingContainer}>
            {streamClient && currentCall && (
              <StreamVideo client={streamClient}>
                <StreamCall call={currentCall}>
                  <View style={styles.videoContainer}>
                    {/* Video will be rendered here by Stream SDK */}
                  </View>
                </StreamCall>
              </StreamVideo>
            )}

            <View style={styles.profileContainer}>
              <Image
                source={{ uri: driverInfo.profileImg }}
                style={styles.profileImg}
              />
              <Text style={styles.callerName}>{driverInfo.name}</Text>
              <Text style={styles.callStatus}>Call in progress</Text>
            </View>

            {/* Call Controls */}
            <CallControlsComponent
              call={currentCall}
              callType={callType}
            />
          </View>
        );

      case CALL_STATES.ENDED:
        return (
          <View style={styles.callingContainer}>
            <View style={styles.profileContainer}>
              <Text style={styles.callStatus}>Call Ended</Text>
            </View>
          </View>
        );

      case CALL_STATES.REJECTED:
        return (
          <View style={styles.callingContainer}>
            <View style={styles.profileContainer}>
              <Text style={styles.callStatus}>Call Rejected</Text>
            </View>
          </View>
        );

      default:
        return (
          <View style={styles.callButtonContainer}>
            <View style={styles.profileContainer}>
              <Text style={styles.callerName}>{driverInfo.name}</Text>
              <Image
                source={{ uri: driverInfo.profileImg }}
                style={styles.profileImg}
              />
            </View>

            <View style={styles.callTypeButtons}>
              <TouchableOpacity
                style={[styles.actionButton, styles.voiceButton]}
                onPress={() => handleStartCall('voice')}
              >
                <Ionicons name="call" size={30} color="white" />
                {/* <Text style={styles.buttonLabel}>Voice</Text> */}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.videoButton]}
                onPress={() => handleStartCall('video')}
              >
                <Ionicons name="videocam" size={30} color="white" />
                {/* <Text style={styles.buttonLabel}>Video</Text> */}
              </TouchableOpacity>
            </View>
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerSpace} />
      {renderCallInterface()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    padding: 20,
    justifyContent: 'center',
  },
  headerSpace: {
    height: Platform.OS === 'ios' ? 60 : 40,
  },
  callingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  profileImg: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: '#fff',
    marginBottom: 20,
  },
  callerName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 30,
  },
  callStatus: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 20,
    marginBottom: 10,
  },
  phoneNumber: {
    color: 'rgba(255,255,255,0.6)',
    fontSize: 16,
    marginBottom: 10,
  },
  callButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  videoContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
  },
  callTypeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '80%',
    marginTop: 40,
  },
  actionButton: {
    width: 75,
    height: 75,
    borderRadius: 37.5,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  voiceButton: {
    backgroundColor: '#4CAF50',
  },
  videoButton: {
    backgroundColor: '#2196F3',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#FF4444',
  },
  buttonLabel: {
    color: '#fff',
    fontSize: 12,
    marginTop: 5,
    textAlign: 'center',
  },
});

