import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import { COLORS, FONTS, icons } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import GradientBackground from '../../components/shared/GradientBackground';

const RideType = () => {
  const navigation = useNavigation();
  const [selectedOptions, setSelectedOptions] = useState({
    normal: false,
    group: false,
    delivery: false,
    reservation: false,
    all: false,
  });

  const handleOptionChange = (key) => {
    if (key === 'all') {
      const newState = !selectedOptions.all;
      setSelectedOptions({
        normal: newState,
        group: newState,
        delivery: newState,
        reservation: newState,
        all: newState,
      });
    } else {
      setSelectedOptions((prevState) => ({
        ...prevState,
        [key]: !prevState[key],
        all: false, // If one of the individual options is toggled, disable "All"
      }));
    }
  };

  return (
    <GradientBackground style={styles.container}>
      {/* Header */}
   <View style={{paddingTop:60}}/>
   
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}>Queued ride</Text>
      </View>
      
      {/* Ride Options */}
      <View style={styles.options}>
        {Object.keys(selectedOptions).map((key, index) => (
          <TouchableOpacity
            key={index}
            style={styles.optionContainer}
            onPress={() => handleOptionChange(key)}
          >
            <Text style={styles.optionTitle}>
              {key.charAt(0).toUpperCase() + key.slice(1).replace('-', ' ')}
            </Text>
            <View
              style={[
                styles.checkbox,
                selectedOptions[key] && styles.checked,
              ]}
            >
              {selectedOptions[key] && <View style={styles.checkboxInner} />}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
     
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 70,
  },
  backText: {
    fontSize: 18,
    color: COLORS.black,
    marginLeft:70,

  },
 
  options: {
    // backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 20,
  },
  optionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  optionTitle: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  checkbox: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checked: {
    backgroundColor: COLORS.primary,
  },
  checkboxInner: {
    width: 10,
    height: 10,
    backgroundColor: COLORS.white,
    borderRadius: 2,
  },
});

export default RideType;
