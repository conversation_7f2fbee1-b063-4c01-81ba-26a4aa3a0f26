import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  FlatList,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const ShareRideWithFriendsScreen = () => {
  const [search, setSearch] = useState('');
  const [selectedFriends, setSelectedFriends] = useState([]);
  const [isShareModalVisible, setShareModalVisible] = useState(false);

  const friends = [
    { id: 1, name: '<PERSON>', phone: '(*************', image: images.driver2 },
    { id: 2, name: '<PERSON><PERSON>', phone: '(*************', image:images.driver1   },
    { id: 3, name: 'Idowu Opabode', phone: '(*************', image: images.driver3 },
    { id: 4, name: '<PERSON><PERSON><PERSON>', phone: '(*************', image: images.driver2 },
  ];

  const toggleFriendSelection = (id) => {
    if (selectedFriends.includes(id)) {
      setSelectedFriends(selectedFriends.filter((friendId) => friendId !== id));
    } else {
      setSelectedFriends([...selectedFriends, id]);
    }
  };

  const renderFriend = ({ item }) => (
    <View style={styles.friendItem}>
      <Image source={item.image} style={styles.friendImage} />
      <View style={styles.friendDetails}>
        <Text style={styles.friendName}>{item.name}</Text>
        <Text style={styles.friendPhone}>{item.phone}</Text>
      </View>
      <TouchableOpacity onPress={() => toggleFriendSelection(item.id)}>
        <View
          style={[
            styles.checkbox,
            selectedFriends.includes(item.id) && styles.checkboxSelected,
          ]}
        />
      </TouchableOpacity>
    </View>
  );
const navigation = useNavigation();
  return (
    <View style={styles.container}>
      {/* Header */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}
      
     <View style={styles.headerContainer}>
             <TouchableOpacity onPress={() => navigation.goBack()}>
             <Image source={images.goback} style={styles.backButton}/>
             </TouchableOpacity>
             <Text style={styles.title}>Split ride</Text>
             <Text></Text>
           </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search for a friend"
          placeholderTextColor={COLORS.grey}
          value={search}
          onChangeText={setSearch}
        />
      </View>

      {/* Friend List */}
      <FlatList
        data={friends.filter((friend) =>
          friend.name.toLowerCase().includes(search.toLowerCase())
        )}
        renderItem={renderFriend}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.friendList}
      />

      {/* Share Button */}
      <TouchableOpacity style={styles.shareButton} onPress={() => setShareModalVisible(true)}>
        <Text style={styles.shareButtonText}>Share ride</Text>
      </TouchableOpacity>

      {isShareModalVisible && (
  <>
    {/* Overlay */}
    <View style={styles.overlay} />

    {/* Modal */}
    <View style={styles.shareModal}>
      <TouchableOpacity
        style={styles.closeButton}
        onPress={() => setShareModalVisible(false)}
      >
        <Text style={styles.closeButtonText}>×</Text>
      </TouchableOpacity>

      <Text style={styles.shareModalTitle}>Your ride has been shared</Text>
      <Text style={styles.shareModalSubtitle}>
        Your friends and family now know where you are. Enjoy your ride
      </Text>

      <TouchableOpacity
        style={styles.okayButton}
        onPress={() => setShareModalVisible(false)}
      >
        <Text style={styles.okayButtonText}>Okay, thanks</Text>
      </TouchableOpacity>
    </View>
  </>
)}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  shareModal: {
    position: 'absolute',
    bottom: 250,
    top: 250,
    left: 20,
    right: 20,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20, borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    padding: SIZES.padding,
    alignItems: 'center',
    zIndex: 2,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  shareModalTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    textAlign: 'center',
    marginHorizontal: 50,
    marginTop: 20,
  },
  shareModalSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginHorizontal: 50,

    marginVertical: 30,
  },
  okayButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    width: '80%',
    alignItems: 'center',
  },
  okayButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  
  backButton: {
    width:40,
    height: 40,
    resizeMode: 'contain',
    ...FONTS.body3,
    color: COLORS.primary,
    marginRight: 10,
  },
  title: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  searchContainer: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    ...FONTS.body3,
    color: COLORS.black,
  },
  friendList: {
    marginBottom: 20,
  },
  friendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  friendImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
  },
  friendDetails: {
    flex: 1,
  },
  friendName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  friendPhone: {
    ...FONTS.body4,
    fontSize: 12,

    color: COLORS.grey,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 4,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
  },
  shareButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  shareButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
});

export default ShareRideWithFriendsScreen;
