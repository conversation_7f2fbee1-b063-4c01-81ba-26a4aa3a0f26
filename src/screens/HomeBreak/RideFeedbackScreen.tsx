import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  TextInput,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import { BASE_URL } from '../../../Baseurl';

const RideFeedbackScreen = () => {
  const route = useRoute();
  const rideId = route?.params; ;
  console.log('Ride ID------:', rideId);
  
  const region = {
    latitude: 33.5186, // Birmingham latitude
    longitude: -86.8104, // Birmingham longitude
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
const navigation = useNavigation();
  const handleRatingPress = (value) => {
    setRating(value);
  };

  const submitFeedback = async () => {
    if (rating === 0 || comment.trim() === '') {
      alert('Please provide a rating and a comment before submitting.');
      return;
    }
  
    setIsSubmitting(true); // Show loader
  
    const feedbackData = {
      rideId: rideId,
      comment: comment,
      rating: rating,
    };
  
    console.log('Submitting feedback:', feedbackData);
    
    try {
      const response = await fetch(`${BASE_URL}/api/rides/afterRideFeedBack`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedbackData),
      });
  
      const result = await response.json();
  console.log('Feedback result:', result);
  
      if (result.success) {
        Alert.alert('Feedback submitted successfully!', result?.data);
        navigation.navigate('TabStack'); // Navigate after success
      } else {
        Alert.alert('Failed to submit feedback. Please try again.', result?.data);
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setIsSubmitting(false); // Hide loader
    }
  };
  

  return (
    <View style={styles.container}>
      {/* Map Section */}
   {Platform.OS === 'ios' && <View style={{ marginTop: 0 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 0 }} />}
      
      <MapView style={styles.map} region={region}>
        <Marker
          coordinate={{ latitude: region.latitude, longitude: region.longitude }}
          title="Pickup Location"
          description="2601 West St."
        />
      </MapView>

      {/* Feedback Section */}
      <View style={styles.feedbackContainer}>
        <View style={styles.headerContainer}>
           <Text></Text>
          <Text style={styles.title}>Your feedback is anonymous</Text>
          <TouchableOpacity onPress={()=> navigation.navigate('TabStack')}>
            <Text style={styles.closeButton}>×</Text>
          </TouchableOpacity>
        </View>

        {/* Rating Stars */}
        <View style={styles.starsContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <TouchableOpacity
              key={star}
              onPress={() => handleRatingPress(star)}
            >
              <Text style={rating >= star ? styles.filledStar : styles.emptyStar}>★</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.anonymousText}>Your feedback is anonymous</Text>

        {/* Comment Input */}
        <TextInput
          style={styles.commentInput}
          placeholder="Leave a comment"
          placeholderTextColor={COLORS.grey}
          value={comment}
          onChangeText={setComment}
          multiline
        />

<TouchableOpacity
  style={[styles.submitButton, isSubmitting && styles.disabledButton]}
  onPress={submitFeedback}
  disabled={isSubmitting}
>
  {isSubmitting ? (
    <ActivityIndicator size="small" color="#fff" />
  ) : (
    <Text style={styles.submitButtonText}>Done</Text>
  )}
</TouchableOpacity>

      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 15,
  },
  disabledButton: {
    backgroundColor: COLORS.grey, // Grey out button when submitting
  },
  submitButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  feedbackContainer: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    marginTop: -20,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
borderBottomWidth:0.5,
borderBottomColor:COLORS.border
  },
  title: {
    textAlign: 'center',
    ...FONTS.body3,
    color: COLORS.black,

  
},
  closeButton: {
    fontSize: 24,
    color: COLORS.black,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
  },
  filledStar: {
    fontSize: 30,
    color: COLORS.primary,
    marginHorizontal: 5,
  },
  emptyStar: {
    fontSize: 30,
    color: COLORS.grey,
    marginHorizontal: 5,
  },
  anonymousText: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 10,
  },
  commentInput: {
    height: 150,
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 10,
    textAlignVertical: 'top',
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 20,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
});

export default RideFeedbackScreen;
