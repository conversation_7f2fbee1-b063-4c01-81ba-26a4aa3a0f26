import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-maps';
import { useNavigation } from '@react-navigation/native';
import Geolocation from '@react-native-community/geolocation';
import io from 'socket.io-client';
import GooglePlacesAutocompleteComponent from '../../components/shared/GooglePlaceAutoComplete';
import { icons, images } from '../../constants';
import SocketService from '../../services/SocketService';



const LocationViaMap = () => {
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [currentLocation, setCurrentLocation] = useState([3.3792, 6.5244]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [destination, setDestination] = useState(null);
  const [rideType, setRideType] = useState('personal');
  const [pickupPoint, setPickupPoint] = useState('');
  const [personalRide, setPersonalRide] = useState(true);
  // const [socket, setSocket] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [waitingForDriver, setWaitingForDriver] = useState(false);
const [driverAccepted, setDriverAccepted] = useState(false);
const [rideTimeout, setRideTimeout] = useState(null);
const [selectedDriver, setSelectedDriver] = useState(null);
const [availableDrivers, setAvailableDrivers] = useState([]);


   

const handleRideRequest = async () => {
  if (!selectedLocation || !destination || !pickupPoint) {
    Alert.alert("Error", "Please select pickup location, destination, and enter pickup point.");
    return;
  }

  setLoading(true);
  setWaitingForDriver(true);

  const bookingData = {
    from: selectedLocation?.description || "Unknown Location",
    fromId: selectedLocation?.id || "N/A",
    fromCoordinates: {
      type: "Point",
      coordinates: selectedLocation?.locationCoordinates
        ? [selectedLocation.locationCoordinates.lng, selectedLocation.locationCoordinates.lat]
        : [-122.406417, 37.785834], // Default coordinates
    },
    to: [
      {
        place: destination?.description || "Unknown Destination",
        placeId: destination?.id || "N/A",
        locationCoordinates: {
          type: "Point",
          coordinates: destination?.locationCoordinates
            ? [destination.locationCoordinates.lng, destination.locationCoordinates.lat]
            : [0, 0],
        },
      },
    ],
    rideType: rideType || "personal",
    pickupPoint: pickupPoint || "Unknown Pickup",
    personalRide: personalRide,
  };

  console.log("🚀 Requesting Ride with Data:", JSON.stringify(bookingData, null, 2));

  if (!SocketService.isConnected()) {
    console.warn("⚠️ Socket not connected. Attempting to reconnect...");
    SocketService.reconnectSocket(() => {
      console.log("🔄 Retrying ride request after reconnection...");
      SocketService.requestRide(bookingData, (serverResponse) => {
        console.log("📡 Server Response:", JSON.stringify(serverResponse, null, 2));
        processRideResponse(serverResponse);
      });
    });
    return;
  }

  // Send ride request via socket
  SocketService.requestRide(bookingData, (serverResponse) => {
    console.log("📡 Server Response:", JSON.stringify(serverResponse, null, 2));
    processRideResponse(serverResponse);
  });
};


  
// Navigation
const navigation = useNavigation();

// Function to get the current location
const getCurrentLocation = async () => {
  const hasPermission = await requestLocationPermission();
  if (!hasPermission) return;

  Geolocation.getCurrentPosition(
    (position) => {
      setCurrentLocation({
        latitude: 8.15884250261019,
        longitude: 4.263593292739184
        // latitude: position.coords.latitude,
        // longitude: position.coords.longitude,
      });
    },
    (error) => {
      console.error("Error fetching location: ", error);
    },
    {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
    }
  );
};

// Function to request location permissions
const requestLocationPermission = async () => {
  if (Platform.OS === "android") {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: "Location Permission Required",
        message:
          "This app requires access to your location to provide location-based features.",
      }
    );
    return granted === PermissionsAndroid.RESULTS.GRANTED;
  }
  return true;
};

// Effect to fetch current location on mount
useEffect(() => {
  getCurrentLocation();
}, []);
const processRideResponse = (serverResponse) => {
  if (!serverResponse || !serverResponse.success) {
    Alert.alert("Error", serverResponse?.message || "Failed to request ride.");
    setWaitingForDriver(false);
    setLoading(false);
    return;
  }

  // Fetch available drivers
  SocketService.getAvailableDrivers((driverData) => {
    console.log("🚖 Nearby Drivers:", JSON.stringify(driverData, null, 2));

    if (!driverData || driverData.length === 0) {
      Alert.alert("No Available Drivers", "No drivers are currently available.");
      setWaitingForDriver(false);
      setLoading(false);
      return;
    }

    // Allow passenger to select a driver
    setAvailableDrivers(driverData);
  });

  // Listen for driver acceptance
  SocketService.listenEvent("rideAccepted", (driverDetails) => {
    console.log("✅ Ride Accepted by Driver:", JSON.stringify(driverDetails, null, 2));
    setWaitingForDriver(false);
    setDriverAccepted(true);
    setSelectedDriver(driverDetails);
  });

  // Listen for driver cancellation
  SocketService.listenEvent("rideCancelled", () => {
    console.log("❌ Ride Cancelled by Driver");
    Alert.alert("Ride Cancelled", "The driver has cancelled the ride.");
    setWaitingForDriver(false);
    setDriverAccepted(false);
  });

  // Set timeout for driver response
  const timeout = setTimeout(() => {
    if (!driverAccepted) {
      setWaitingForDriver(false);
      Alert.alert(
        "No Drivers Found",
        "No drivers accepted your request. Would you like to search again?",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Search Again", onPress: handleRideRequest },
        ]
      );
    }
  }, 15000);

  setRideTimeout(timeout);
};


  return (
    <View style={styles.container}>
    {waitingForDriver && (
  <View style={styles.overlay}>
    {/* BottomSheet */}
    <View style={styles.bottomSheet}>
      <Text style={styles.bottomSheetTitle}>Looking for a driver...</Text>
      <ActivityIndicator size="large" color={COLORS.primary} />
      <Text style={styles.waitingText}>Please wait while we find a driver for you.</Text>
    </View>
  </View>
)}


      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Select Location Via Map</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.inputContainer}>
          <Image source={images.routeIndicator} style={styles.routeIndicator} />
          <View>
            <GooglePlacesAutocompleteComponent
              //  style={styles.input}
              placeholder="Select your pickup location"
              onSelect={(location) => setSelectedLocation(location)}
            />

            <GooglePlacesAutocompleteComponent
              //  style={styles.input}
              placeholder="Where to?"
              onSelect={(location) => setDestination(location)}
            />
          </View>


        </View>
        <Text style={styles.label}>Pick up Point:</Text>

        <TextInput
          placeholder="Enter pickup point"
          placeholderTextColor={COLORS.black}
          value={pickupPoint}
          onChangeText={setPickupPoint}
          style={styles.input}
        />
{/* 
        <View style={styles.dropdownContainer}>
          <Text style={styles.label}>Select Ride Type:</Text>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() => setDropdownOpen(!dropdownOpen)}
          >
            <Text style={styles.dropdownText}>{rideType}</Text>
            <Image source={icons.ArrowIcon} style={styles.dropdownIcon} />
          </TouchableOpacity>

          {dropdownOpen && (
            <View style={styles.dropdownList}>
              {["personal", "group", "split", "delivery", "reservation"].map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[styles.dropdownItem, rideType === type && styles.activeDropdownItem]}
                  onPress={() => {
                    setRideType(type);
                    setDropdownOpen(false);
                  }}
                >
                  <Text style={[styles.dropdownItemText, rideType === type && styles.activeDropdownItemText]}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View> */}


        {/* <View style={styles.switchContainer}>
          <Text style={styles.label}>Personal Ride:</Text>
          <Switch
            value={personalRide}
            onValueChange={setPersonalRide}
            trackColor={{ false: COLORS.grey, true: COLORS.primary }}
            thumbColor={personalRide ? COLORS.white : COLORS.grey}
          />
        </View> */}


        <MapView
          style={styles.map}
          region={currentLocation ? { ...currentLocation, latitudeDelta: 0.05, longitudeDelta: 0.05 } : null}
        >
          {currentLocation && <Marker coordinate={currentLocation} title="Current Location" />}
          {selectedLocation && <Marker coordinate={selectedLocation} title="Selected Location" />}
          {destination && <Marker coordinate={destination} title="Destination" />}
          {currentLocation && destination && (
            <Polyline coordinates={[currentLocation, destination]} strokeWidth={5} strokeColor={COLORS.primary} />
          )}
        </MapView>

      </ScrollView>



      <TouchableOpacity
        style={[styles.continueButton]}
        onPress={()=>navigation.navigate('ChooseADriver')}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color={COLORS.white} />
        ) : (
          <Text style={styles.continueButtonText}>Request Ride</Text>
        )}
      </TouchableOpacity>


    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  bottomSheet: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: SIZES.height * 0.5,
    backgroundColor: COLORS.white,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 1,
  },
  bottomSheetTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: COLORS.black,
    marginBottom: 10,
  },
  waitingText: {
    fontSize: 14,
    color: COLORS.gray,
    marginTop: 10,
  },
  
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.h3,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    padding: SIZES.padding,
  },
  inputContainer: {
    marginBottom: 20,
    flexDirection: 'row',
  },
  inputRow: {},
  routeIndicator: {
    width: 50,
    height: 90,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft: 0,
  },
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 15,
    width: '100%',
    borderWidth: 0.5,
    borderColor: COLORS.white,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
    marginBottom: 20,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  continueButton: {
    position: "static",
    width:350,
    bottom: 20,
    alignSelf: 'center',
    paddingHorizontal: 0,
    marginTop: 20,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    // alignItems: 'center',
  },
  continueButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
    textAlign: 'center',
  },
  // bottomSheet: {
  //   height: Dimensions.get('window').height * 0.5,
  //   position: 'absolute',
  //   bottom: 0,
  //   left: 0,
  //   right: 0,
  //   backgroundColor: COLORS.white,
  //   padding: SIZES.padding,
  //   borderTopLeftRadius: 40,
  //   borderTopRightRadius: 40,
  //   shadowColor: COLORS.black,
  //   shadowOffset: { width: 0, height: -2 },
  //   shadowOpacity: 0.1,
  //   shadowRadius: 10,
  //   elevation: 5,
  // },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    lineHeight: 20,
    paddingBottom: 20,
  },
  bottomSheetRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  bottomSheetLocation: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  searchIcon: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginHorizontal: 5,
  },
  confirmButton: {
    position: 'absolute',
    bottom: 20,
    alignSelf: 'center',
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: SIZES.radius,
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',  
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999, 
  },
  switchContainer: {
    marginVertical: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 15,
  },
  label: {
    ...FONTS.h4,
    fontSize: 13,
    color: COLORS.black,
  },
  dropdownContainer: {
    marginTop: 20,
    // paddingHorizontal: 15,
  },

  label: {
    fontSize: 16,
    color: COLORS.black,
    ...FONTS.body4,
  },
  disabledButton: {
    backgroundColor: COLORS.grey,
  },

  dropdownButton: {
    width: '100%',
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: COLORS.white,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.white,
    marginTop: 15,
  },

  dropdownText: {
    fontSize: 14,
    color: COLORS.black,
    ...FONTS.body4,
  },

  dropdownIcon: {
    width: 14,
    height: 14,
    tintColor: COLORS.gray,
  },

  dropdownList: {
    marginTop: 5,
    backgroundColor: COLORS.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    paddingVertical: 5,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 2,
  },

  dropdownItem: {
    padding: 12,
  },

  activeDropdownItem: {
    backgroundColor: COLORS.primary,
  },

  dropdownItemText: {
    fontSize: 14,
    color: COLORS.black,
    ...FONTS.body4,
  },

  activeDropdownItemText: {
    color: COLORS.white,
  },

});



export default LocationViaMap;
