import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Image, FlatList, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import MapView, { Marker } from 'react-native-maps';
import { COLORS, FONTS, SIZES, images } from '../../../constants';
import Geolocation from '@react-native-community/geolocation';

const SearchResult = () => {
    const navigation = useNavigation();
    const [selectedCategory, setSelectedCategory] = useState('buses');
    const [currentLocation, setCurrentLocation] = useState(null);
    const categories = [
        { id: 'light_rail', name: 'Light rail', icon: images.caroutline, type: 'light_rail' },
        { id: 'buses', name: 'Buses', icon: images.bus, type: 'buses' },
    ];

    useEffect(() => {
        Geolocation.getCurrentPosition(
            (position) => {
                const { latitude, longitude } = position.coords;
                setCurrentLocation({ latitude, longitude });
            },
            (error) => {
                console.error('Error getting current location:', error);
            },
            { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
    }, []);

    const rides = [
        {
            id: '1',
            title: 'Fisherman’s Wharf',
            duration: '22 min',
            price: '$54',
            pickup: 'AMLI 7th Street Station.',
            dropoff: 'AMLI 7th Street Station.',
        },
        {
            id: '2',
            title: 'Fisherman’s Wharf',
            duration: '22 min',
            price: '$54',
            pickup: 'AMLI 7th Street Station.',
            dropoff: 'AMLI 7th Street Station.',
        },
    ];

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Image source={images.goback} style={styles.backIcon} />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Taxi van</Text>
            </View>

            {/* Categories */}
            <View style={styles.categoryContainer}>
                {categories.map((category) => (
                    <TouchableOpacity
                        key={category.id}
                        style={[
                            styles.categoryButton,
                            selectedCategory === category.type && styles.selectedCategory,
                        ]}
                        onPress={() => setSelectedCategory(category.type)}
                    >
                        <Image source={category.icon} style={styles.categoryIcon} />
                        <Text
                            style={[
                                styles.categoryText,
                                selectedCategory === category.type && styles.selectedCategoryText,
                            ]}
                        >
                            {category.name}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Rides List */}
            <FlatList
                data={rides}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                    <View style={styles.rideCard}>
                        {/* Map View */}
                        <View style={styles.mapContainer}>
                            <MapView
                                style={styles.map}
                                region={currentLocation ?
                                    { latitude: currentLocation.latitude, longitude: currentLocation.longitude, latitudeDelta: 0.05, longitudeDelta: 0.05 }
                                    : { latitude: 8.3792, longitude: 4.5244, latitudeDelta: 0.05, longitudeDelta: 0.05 }
                                }
                            >
                                {currentLocation && <Marker coordinate={currentLocation} title="Your Location" />}
                            </MapView>
                        </View>

                        {/* Ride Details */}
                        <View>
                            <Text style={styles.rideTitle}>{item.title} - {item.duration}</Text>
                            <Text style={styles.ridePrice}>{item.price}</Text>
                        </View>
                        {/* Pickup & Dropoff */}
                        <View style={styles.locationContainer}>
                            <Image source={images.routeIndicator} style={styles.dotIcon} />
                            <View>
                                <View style={styles.locationRow}>
                                    <Text style={styles.locationText}>{item.pickup}</Text>
                                </View>
                                <View style={styles.dashedLine} />
                                <View style={styles.locationRow}>
                                    <Text style={styles.locationText}>{item.dropoff}</Text>
                                </View>
                            </View>
                        </View>
                    </View>
                )}
                contentContainerStyle={{ paddingBottom: 20 }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: COLORS.white, padding: SIZES.padding },

    header: { flexDirection: 'row', alignItems: 'center', marginBottom: SIZES.base },
    backIcon: { width: 24, height: 24 },
    headerTitle: { ...FONTS.body3, textAlign: 'center', flex: 1, color: COLORS.black },

    categoryContainer: { flexDirection: 'row', justifyContent: 'center', marginVertical: SIZES.base * 2 },
    categoryButton: { flexDirection: 'row', alignItems: 'center', paddingVertical: SIZES.base, paddingHorizontal: SIZES.base * 2, borderRadius: SIZES.radius, backgroundColor: COLORS.light_blue, marginHorizontal: SIZES.base },
    selectedCategory: { backgroundColor: COLORS.primary },
    categoryIcon: { width: 18, height: 18, marginRight: SIZES.base },
    categoryText: { ...FONTS.body4, color: COLORS.black },
    selectedCategoryText: { color: COLORS.white },

    rideCard: { backgroundColor: COLORS.white, borderRadius: SIZES.radius, padding: SIZES.base * 2, borderWidth: 0.5, borderColor: COLORS.border, marginBottom: SIZES.base * 2 },

    mapContainer: { width: '100%', borderRadius: SIZES.radius, overflow: 'hidden', marginBottom: SIZES.base * 2 },
    map: { width: '100%', height: 150, resizeMode: 'cover' },

    rideTitle: { ...FONTS.body3, color: COLORS.black },
    ridePrice: { ...FONTS.body4, color: COLORS.black, position: 'absolute', right: SIZES.base * 2 },

    locationContainer: { flexDirection: 'row', alignItems: 'center', marginTop: SIZES.base * 2 },
    dotIcon: { width: 16, height: '80%', marginRight: SIZES.base, resizeMode: 'contain' },
    locationRow: { flexDirection: 'row', alignItems: 'center', marginBottom: SIZES.base, borderWidth: 0.5, borderRadius: 7, padding: 12, width: 280, borderColor: COLORS.border },
    locationText: { ...FONTS.body4, color: COLORS.black },

    dashedLine: { height: 20, width: 1, backgroundColor: COLORS.border, alignSelf: 'center' },
});

export default SearchResult;
