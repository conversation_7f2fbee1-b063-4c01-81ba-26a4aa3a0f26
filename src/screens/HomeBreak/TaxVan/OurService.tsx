import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, FlatList, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, SIZES, images } from '../../../constants';



const OurService = () => {
  const navigation = useNavigation();
  const rideOptions = [
    { id: '1', title: 'Ride', image: images.gride, screen: ()=>navigation.navigate('ArrangeYourRide', { rideType: "personal" }) },
    { id: '2', title: 'Reserve', image: images.reserve, screen: ()=>navigation.navigate('PickDate', { rideType: "schedule" })},
    { id: '3', title: 'Delivery', image: images.object, screen: ()=>navigation.navigate('ArrangeYourRide', { rideType: "delivery" })  },
    { id: '4', title: 'Group ride', image: images.ride, screen: ()=>navigation.navigate('ArrangeYourRide', { rideType: "group" })  },
    // { id: '5', title: 'Taxi Van', image: images.bus, screen: 'ViewFare' },
  ];
  return (
    <View style={styles.container}>
      {/* Title */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}     
           {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />} 
      <Text style={styles.headerTitle}>Our Services</Text>

      <FlatList
        data={rideOptions}
        keyExtractor={(item) => item.id}
        numColumns={3}
        contentContainerStyle={styles.grid}
        renderItem={({ item }) => (
          <TouchableOpacity style={styles.optionCard} onPress={item.screen}>
            <Image source={item.image} style={styles.optionImage} />
            <Text style={styles.optionTitle}>{item.title}</Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.light_blue,  width: '100%' },
  
  headerTitle: { 
    ...FONTS.h3, 
    textAlign: 'center', 
    color: COLORS.black, 
    marginBottom: SIZES.padding ,
    backgroundColor: COLORS.white,
    paddingVertical: SIZES.padding,
  },

  grid: { justifyContent: 'center',  paddingHorizontal: SIZES.padding },

  optionCard: { 
    width: SIZES.width * 0.25, 
    height: SIZES.width * 0.25, 
    backgroundColor: COLORS.white, 
    borderRadius: SIZES.radius, 
    alignItems: 'center', 
    justifyContent: 'center', 
    paddingVertical: SIZES.base * 2, 
    margin: SIZES.base * 1.5, 
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },

  optionImage: { width: 60, height: 60, resizeMode: 'contain' },

  optionTitle: { 
    marginTop: SIZES.base, 
    fontSize: SIZES.body4, 
    fontFamily: FONTS.h3.fontFamily, 
    color: COLORS.black, 
    textAlign: 'center' 
  },
});

export default OurService;
