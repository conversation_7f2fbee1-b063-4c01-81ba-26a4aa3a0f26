import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import MapView, { <PERSON>er, Polyline } from 'react-native-maps';
import { useNavigation, useRoute } from '@react-navigation/native';
import socketService from '../../services/SocketService';
import BackToHome from '../../components/Backbtn';

const ConfirmPickUp = () => {
  const route = useRoute();
  const rideDetails = route.params || {};
  console.log('rideDetails', JSON.stringify(rideDetails, null, 2));

  const [rideDetail, setRideDetail] = useState(rideDetails);
  const [pickupPoint, setPickupPoint] = useState('');
  const [noOffPassengers, setNoOffPassengers] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  
  const navigation = useNavigation();

  // Map region and coordinates
  const [region, setRegion] = useState({
    latitude: 33.5186, // Birmingham latitude
    longitude: -86.8104, // Birmingham longitude
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  });
  const [routeCoordinates, setRouteCoordinates] = useState([]);

  useEffect(() => {
    const geocodeLocations = async () => {
      try {
        setIsLoading(true);
        
        if (!rideDetail?.from || !rideDetail?.to?.[0]?.place) {
          setIsLoading(false);
          return;
        }
        
        // Always use geocoding from OpenStreetMap
        const pickupResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(rideDetail.from)}&format=json&limit=1`);
        const pickupData = await pickupResponse.json();
        
        const dropoffResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(rideDetail.to[0].place)}&format=json&limit=1`);
        const dropoffData = await dropoffResponse.json();

        if (pickupData.length > 0 && dropoffData.length > 0) {
          const pickupCoords = {
            latitude: parseFloat(pickupData[0].lat),
            longitude: parseFloat(pickupData[0].lon)
          };
          
          const dropoffCoords = {
            latitude: parseFloat(dropoffData[0].lat),
            longitude: parseFloat(dropoffData[0].lon)
          };

          // Create midpoint between pickup and dropoff
          const midpointCoords = {
            latitude: (pickupCoords.latitude + dropoffCoords.latitude) / 2,
            longitude: (pickupCoords.longitude + dropoffCoords.longitude) / 2
          };
          
          // Set route coordinates
          setRouteCoordinates([pickupCoords, midpointCoords, dropoffCoords]);
          
          // Calculate proper delta for the region to ensure both points are visible
          const latDelta = Math.abs(pickupCoords.latitude - dropoffCoords.latitude) * 1.5;
          const lngDelta = Math.abs(pickupCoords.longitude - dropoffCoords.longitude) * 1.5;
          
          // Set map region to center on the route
          setRegion({
            latitude: midpointCoords.latitude,
            longitude: midpointCoords.longitude,
            latitudeDelta: Math.max(latDelta, 0.05), // Ensure minimum zoom level
            longitudeDelta: Math.max(lngDelta, 0.05), // Ensure minimum zoom level
          });
        } else {
          throw new Error('Could not find coordinates for one or both locations');
        }
      } catch (error) {
        console.error("Error setting up map:", error);
        Alert.alert("Map Error", error.message || "Failed to load map coordinates");
      } finally {
        setIsLoading(false);
      }
    };

    geocodeLocations();
  }, [rideDetail]);

  const handleConfirmPickup = () => {
    if (!pickupPoint) {
      Alert.alert('Please enter a pickup point');
      return;
    }
    
    if (rideDetail?.rideType === 'personal' || rideDetail?.rideType === 'group') {
      if (!noOffPassengers) {
        Alert.alert('Please enter the number of passengers');
        return;
      }
    }
      
    const updatedRideDetails = { ...rideDetail, pickupPoint, noOffPassengers };
    setRideDetail(updatedRideDetails);
    navigation.navigate('ChooseADriver', updatedRideDetails);
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />}

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Confirm PickUp Spot</Text>
        <BackToHome />
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Map Section */}
        {isLoading ? (
          <View style={[styles.map, styles.loadingContainer]}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading map...</Text>
          </View>
        ) : (
          <MapView
            style={styles.map}
            region={region}
            showsUserLocation={true}
          >
            {/* Route Line */}
            {routeCoordinates.length > 0 && (
              <Polyline
                coordinates={routeCoordinates}
                strokeColor={COLORS.primary}
                strokeWidth={3}
              />
            )}
            
            {/* Origin Marker */}
            {routeCoordinates.length > 0 && (
              <Marker coordinate={routeCoordinates[0]}>
                <View style={styles.marker}>
                  <Text style={styles.markerText}>{rideDetail?.from || 'Origin'}</Text>
                </View>
              </Marker>
            )}
            
            {/* Destination Marker */}
            {routeCoordinates.length > 0 && (
              <Marker coordinate={routeCoordinates[2]}>
                <View style={styles.marker}>
                  <Text style={styles.markerText}>{rideDetail?.to?.[0].place || 'Destination'}</Text>
                </View>
              </Marker>
            )}
          </MapView>
        )}
      </ScrollView>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
      >
        <ScrollView style={styles.bottomSheet} keyboardShouldPersistTaps="handled" scrollEnabled={true}>
          <Text style={styles.bottomSheetTitle}>Confirm PickUp Spot</Text>

          {(rideDetail?.rideType === 'personal' || rideDetail?.rideType === 'group') && (
            <TextInput
              style={styles.input1}
              placeholder="Number of Passengers"
              placeholderTextColor={COLORS.grey}
              value={noOffPassengers}
              keyboardType="numeric"
              onChangeText={(text) => setNoOffPassengers(text)}
            />
          )}

          <TextInput
            style={styles.input1}
            placeholder="Enter PickUp Address"
            value={pickupPoint}
            multiline={true}
            numberOfLines={4}
            onChangeText={(text) => setPickupPoint(text)}
          />

          <View style={styles.paymentContainer}>
            <TouchableOpacity style={styles.paymentButton} onPress={handleConfirmPickup}>
              <Text style={styles.paymentButtonText}>Search for Driver</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body4,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    flex: 1,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.5,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  loadingText: {
    ...FONTS.body3,
    color: COLORS.grey,
    marginTop: 10,
  },
  marker: {
    backgroundColor: COLORS.primary,
    padding: 5,
    borderRadius: 5,
    maxWidth: 150,
  },
  markerText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 10,
  },
  bottomSheet: {
    height: Dimensions.get('window').height * 0.4,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 15,
  },
  bottomSheetSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    paddingBottom: 20,
  },
  input1: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 15,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 10,
    color: COLORS.black,
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    marginTop: 5,
  },
  paymentButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paymentButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
});

export default ConfirmPickUp;