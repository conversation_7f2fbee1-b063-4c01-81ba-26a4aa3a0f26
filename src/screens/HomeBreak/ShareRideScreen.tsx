import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Share,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const ShareRideScreen = () => {
  const shareRide = async () => {
    try {
      const result = await Share.share({
        message: 'I am sharing my ride details with you! 🚗\nPickup: AMLI 7th Street Station.\nDropoff: Fisherman’s Wharf.\nArrival: 13th July, 10:00 PM.\nPrice: $54',
      });
  
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Shared successfully');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error) {
      console.log('Error sharing:', error.message);
    }
  };
    const navigation = useNavigation();
  return (
    <View style={styles.container}>
         {Platform.OS === 'ios' && <View style={{ marginTop: 20 }} />}     
         {Platform.OS === 'android' && <View style={{ marginTop: 20 }} />}
      {/* Header */}
      <View style={{paddingTop:0}}/>
      
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
              <Image source={images.goback} style={styles.backButton}/>
              </TouchableOpacity>
        <Text style={styles.title}>Share ride</Text>
      </View>

      {/* Illustration */}
      <Image source={images.shareRide} style={styles.illustration} />

      {/* Description */}
      <Text style={styles.mainText}>Lets your friends know you are on your way</Text>
      <Text style={styles.subText}>
        Connect contact and easily send your friends and foes a live map of your ride
      </Text>

      <TouchableOpacity style={styles.shareButton} 
      // onPress={()=>navigation.navigate('ShareRideWithFriendsScreen')}
      onPress={shareRide}
      >
  <Text style={styles.shareButtonText}>Share ride</Text>
</TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // alignItems: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    // marginBottom: 20,
    paddingHorizontal: 20,
    backgroundColor: COLORS.white,
    paddingVertical: 30,
  },
  backButton: {
    width:30,
    height: 30,
    resizeMode: 'contain',
    ...FONTS.body3,
    color: COLORS.primary,
    marginRight: 10,
  },
  title: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    flex: 1,
  },
  illustration: {
    width: '100%',
    height: 250,
    resizeMode: 'contain',
    marginBottom: 20,
  },
  mainText: {
    ...FONTS.body2,
    color: COLORS.black,
    paddingHorizontal: 30,
    textAlign:'left', 
    marginVertical: 20,
  },
  subText: {
    ...FONTS.body3,
    color: COLORS.grey,
    textAlign:'left', 
    paddingHorizontal: 30,
    marginBottom: 30,
  },
  shareButton: {
    position: 'absolute',bottom:30,
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    alignSelf: 'center',
    width: '80%',
  },
  shareButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
});

export default ShareRideScreen;
