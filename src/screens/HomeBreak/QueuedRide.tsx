 

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import { COLORS, FONTS, icons } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import GradientBackground from '../../components/shared/GradientBackground';

const QueuedRide = () => {
  const navigation = useNavigation();
  const [selectedOption, setSelectedOption] = useState('manual');

  const handleOptionChange = (option) => {
    setSelectedOption(option);
  };

  return (
    <GradientBackground style={styles.container}>
      {/* Header */}
      <View style={{paddingTop:60}}/>
      

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}>Queued ride</Text>
      </View>
      

      {/* Options */}
      <View style={styles.options}>
        {/* Auto-Accept Option */}
        <TouchableOpacity
          style={styles.optionContainer}
          onPress={() => handleOptionChange('auto')}
        >
          <View style={styles.row}>
            <Text style={styles.optionTitle}>Auto-accept</Text>
            <View
              style={[
                styles.checkbox,
                selectedOption === 'auto' && styles.checked,
              ]}
            >
              {selectedOption === 'auto' && <View style={styles.checkboxInner} />}
            </View>
          </View>
          <Text style={styles.optionDescription}>
            Rides are automatically accepted and added to your queue after 30
            seconds. You can still decline manually.
          </Text>
        </TouchableOpacity>

        {/* Manual-Accept Option */}
        <TouchableOpacity
          style={styles.optionContainer}
          onPress={() => handleOptionChange('manual')}
        >
          <View style={styles.row}>
            <Text style={styles.optionTitle}>Manual-accept</Text>
            <View
              style={[
                styles.checkbox,
                selectedOption === 'manual' && styles.checked,
              ]}
            >
              {selectedOption === 'manual' && <View style={styles.checkboxInner} />}
            </View>
          </View>
          <Text style={styles.optionDescription}>
            Rides can be accepted and added to your queue by yourself after 30
            seconds. Rides are automatically declined otherwise.
          </Text>
        </TouchableOpacity>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: COLORS.lightGray,
    // padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 70,
  },
  backText: {
    fontSize: 18,
    color: COLORS.black,
    marginLeft:70,

  },
 
  options: {
    borderRadius: 10,
    padding: 20,
  },
  optionContainer: {
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.light_grey,
    paddingBottom: 10,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionTitle: {
    ...FONTS.body3,
    fontSize:14,
    color: COLORS.black,
  },
  checkbox: {
    width: 16,
    height: 16,
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor:COLORS.white,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checked: {
    backgroundColor: COLORS.primary,
    borderWidth: 4,

  },
  checkboxInner: {
    width: 10,
    height: 10,
    backgroundColor: COLORS.white,
    borderRadius: 9,
  },
  optionDescription: {
    ...FONTS.body4,
    color: COLORS.black,
    marginTop: 10,
    fontSize:12,
    lineHeight:18
  },
});

export default QueuedRide;
