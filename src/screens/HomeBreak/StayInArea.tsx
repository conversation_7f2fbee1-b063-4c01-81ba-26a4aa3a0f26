 
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import MapView, { Circle, Marker } from 'react-native-maps';
import { COLORS, FONTS } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import Slider from '@react-native-community/slider';

const StayInArea = () => {
  const navigation = useNavigation();
  const [radius, setRadius] = useState(30000); // Initial radius in meters (30km)

  // Map region
  const [region, setRegion] = useState({
    latitude: 33.5186, // Birmingham latitude
    longitude: -86.8104, // Birmingham longitude
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  });

  const handleContinue = () => {
    navigation.navigate('RideType', { radius, location: region });
  };

  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={{paddingTop:60}}/>
      
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backText}>{'<'}</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Stay in area</Text>
      </View>

      {/* Map Section */}
      <MapView
        style={styles.map}
        region={region}
        onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
      >
        {/* Circle showing the search area */}
        <Circle
          center={{ latitude: region.latitude, longitude: region.longitude }}
          radius={radius} // Circle radius in meters
          strokeColor={COLORS.primary}
          fillColor="rgba(0, 122, 255, 0.2)"
        />
        {/* Marker in the center */}
        <Marker
          coordinate={{ latitude: region.latitude, longitude: region.longitude }}
          draggable
          onDragEnd={(e) => {
            const { latitude, longitude } = e.nativeEvent.coordinate;
            setRegion({ ...region, latitude, longitude });
          }}
        />
      </MapView>

      {/* Details Section */}
      <View style={styles.details}>
        <Text style={styles.subtitle}>
          We’ll search for rides within this location, you can widen the area to
          get more potential rides.
        </Text>

        {/* Slider for Radius Selection */}
        <View style={styles.sliderContainer}>
          <Text style={styles.sliderText}>{Math.round(radius / 1000)}miles</Text>
          <Slider
            style={styles.slider}
            minimumValue={10000} // 10km
            maximumValue={100000} // 100km
            step={1000} // 1km
            value={radius}
            onValueChange={(value) => setRadius(value)}
            minimumTrackTintColor={COLORS.primary}
            maximumTrackTintColor={COLORS.lightGray}
          />
          <View style={styles.sliderLabels}>
            <Text style={styles.label}>10miles</Text>
            <Text style={styles.label}>100miles</Text>
          </View>
        </View>

        {/* Continue Button */}
        <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
          <Text style={styles.continueButtonText}>Continue →</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  backText: {
    fontSize: 18,
    color: COLORS.primary,
  },
  title: {
    ...FONTS.h3,
    marginLeft: 20,
    color: COLORS.dark,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  details: {
    padding: 20,
    backgroundColor: COLORS.lightGray,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    flex: 1,
  },
  subtitle: {
    ...FONTS.body3,
    color: COLORS.darkGray,
    marginBottom: 20,
    textAlign: 'center',
  },
  sliderContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  label: {
    ...FONTS.body4,
    color: COLORS.darkGray,
  },
  sliderText: {
    ...FONTS.h3,
    color: COLORS.primary,
    marginBottom: 10,
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  continueButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default StayInArea;
