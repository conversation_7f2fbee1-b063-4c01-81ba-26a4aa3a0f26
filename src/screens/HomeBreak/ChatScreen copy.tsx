import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
} from 'react-native';
// import Icon from 'react-native-vector-icons/Ionicons';  // Changed from Expo icons
import { useNavigation, useRoute } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, images } from '../../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socketService from '../../services/SocketService';


export default function ChatScreen() {
  const navigation = useNavigation();
  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');

  const route = useRoute();
  const { rideId } = route.params;
  console.log('Ride ID:', rideId);
  const [isOptionsVisible, setIsOptionsVisible] = useState(false);


  useEffect(() => {
    const loadMessages = async () => {
      try {
        const savedMessages = await AsyncStorage.getItem(`chat_${rideId}`);
        if (savedMessages) {
          setMessages(JSON.parse(savedMessages)); // Load stored messages immediately
        }
      } catch (error) {
        console.error("Error loading messages:", error);
      }
    };
  
    loadMessages();
  
    return () => {
      socketService.socket.off("chatWithDriver");
      socketService.socket.off("chatWithPassenger");
    };
  }, [rideId]);
  
  const sendMessage = async () => {
    if (!messageText.trim()) return;

    const messageData = {
      rideId: rideId,
      message: messageText,
    };
    console.log('messageData', messageData);

    // Emit the message to the server
    socketService.emitEvent('chatWithDriver', messageData);

    const newMessage = {
      id: Date.now().toString(),
      text: messageText,
      isSent: true,
      seen: false,
    };

    setMessages((prevMessages) => {
      const updatedMessages = [...prevMessages, newMessage];

      // Save updated messages locally
      AsyncStorage.setItem(`chat_${rideId}`, JSON.stringify(updatedMessages));

      return updatedMessages;
    });

    setMessageText('');
  };


  useEffect(() => {
    const handleNewMessageFromDriver = (data) => {
      console.log("New message from driver:", data);
  
      if (data?.message && Array.isArray(data.message)) {
        setMessages((prevMessages) => {
          const existingMessageIds = new Set(prevMessages.map((msg) => msg.id));
          const newMessages = data.message
            .filter((msg) => !existingMessageIds.has(msg._id)) // Prevent duplicates
            .map((msg) => ({
              id: msg._id,
              text: msg.message,
              mediaLink: msg.mediaLink || null,
              isSent: false, // Driver messages should be marked as received
            }));
  
          const updatedMessages = [...prevMessages, ...newMessages];
  
          AsyncStorage.setItem(`chat_${rideId}`, JSON.stringify(updatedMessages));
          return updatedMessages;
        });
      }
    };
  
    socketService.onEvent("chatWithPassenger", handleNewMessageFromDriver);
  
    return () => {
      socketService.socket.off("chatWithPassenger", handleNewMessageFromDriver);
    };
  }, [rideId]);
  


  const renderMessage = ({ item }) => {
    const isImage = item.mediaLink && typeof item.mediaLink === "string" && item.mediaLink.trim() !== "" && !item.mediaLink.endsWith(".mp3");
    const isAudio = item.mediaLink && typeof item.mediaLink === "string" && item.mediaLink.endsWith(".mp3");
    const isText = item.text && typeof item.text === "string" && item.text.trim() !== "";
  
    if (!isText && !isImage && !isAudio) return null;
  
    return (
      <View
        style={[
          styles.messageContainer,
          item.isSent ? styles.sentMessage : styles.receivedMessage,
        ]}
      >
        {isImage && (
          <Image
            source={{ uri: item.mediaLink }}
            style={{ width: 120, height: 120, borderRadius: 10 }}
            resizeMode="cover"
          />
        )}
  
        {isText && (
          <Text style={item.isSent ? styles.messageText : styles.messageTextt}>
            {item.text}
          </Text>
        )}
  
        {isAudio && (
          <TouchableOpacity onPress={() => playAudio(item.mediaLink)} style={styles.audioButton}>
            <Text style={{ color: "white" }}>▶ Play Audio</Text>
          </TouchableOpacity>
        )}
  
        {item.isSent && (
          <Text style={{ fontSize: 10, color: "#D1D5DB", alignSelf: "flex-end" }}>
            {item.isSent ? "✓✓" : "✓"}
          </Text>
        )}
      </View>
    );
  };
  


  return (
    <LinearGradient colors={[COLORS.light_blue, '#FFFFFF']} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          {/* <Icon name="chevron-back" size={24} color="#000" /> */}
          <Image source={images.goback} style={{ width: 30, height: 30, resizeMode: 'contain' }} />

        </TouchableOpacity>
        <View style={styles.profileContainer}>
          <Image source={images.driver1} style={styles.profileImage} />
          <Text style={styles.profileName}>Olive Rodrigo</Text>
        </View>
        <View style={styles.headerIcons}>

          <Image source={images.Videoicon} style={{ width: 30, height: 30, resizeMode: 'contain', marginHorizontal: 5 }} />
          <Image source={images.call} style={{ width: 15, height: 15, resizeMode: 'contain' }} />

        </View>
      </View>

      {/* Info */}
      <Text style={styles.infoText}>
        Keep your account safe—don’t share personal or account information here
      </Text>

      {/* Messages */}
      <FlatList
  data={messages}
  keyExtractor={(item) => item.id.toString()}
  renderItem={renderMessage}
  inverted // 👈 Makes chat scroll from bottom up
  ListEmptyComponent={<Text style={styles.emptyMessage}>No messages yet</Text>}
/>
      {/* Options */}
      {isOptionsVisible && (
        <View style={styles.optionsContainer}>
          {[
            { id: '1', icon: images.camera, text: 'Camera' },
            { id: '2', icon: images.Micicon, text: 'Record' },
            { id: '3', icon: images.Contacticon, text: 'Contact' },
            { id: '4', icon: images.Imageicon, text: 'Gallery' },
            { id: '5', icon: images.Locicon, text: 'Location' },
            { id: '6', icon: images.Fileicon, text: 'Document' },
          ].map((option) => (
            <TouchableOpacity key={option.id} style={styles.option}>
              <Image source={option.icon} style={[styles.driverImage, { width: 30, height: 30, marginRight: 0 }]} />
              <Text style={styles.optionText}>{option.text}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Input */}
      <View style={styles.inputContainer}>
        <TouchableOpacity onPress={() => setIsOptionsVisible(!isOptionsVisible)}>
          {/* <Icon name="add-outline" size={24} color={COLORS.primary} /> */}
          <Image source={images.addd} style={{ width: 50, resizeMode: 'contain', height: 50 }} />

        </TouchableOpacity>
        <TextInput
          style={styles.textInput}
          placeholder="Type a message..."
          value={messageText}
          onChangeText={setMessageText}
          onSubmitEditing={sendMessage} // Send message when user presses Enter
        />

        <TouchableOpacity onPress={sendMessage}>
          {/* <Icon name="send-outline" size={24} color="#FFF" style={styles.sendButton} /> */}
          <Image source={images.send} style={{ width: 50, resizeMode: 'contain', height: 50 }} />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.light_blue },
  header: { flexDirection: 'row', alignItems: 'center', padding: 16, backgroundColor: '#FFFFFF', borderBottomWidth: 1, borderBottomColor: '#E5E7EB' },
  profileContainer: { flexDirection: 'row', alignItems: 'center', flex: 1, marginLeft: 16 },
  profileImage: { width: 40, height: 40, borderRadius: 20, marginRight: 8 },
  profileName: { fontSize: 16, fontWeight: 'bold' },
  headerIcons: { flexDirection: 'row', alignItems: 'center' },
  icon: { marginRight: 16 },
  infoText: { textAlign: 'center', fontSize: 12, color: '#6B7280', padding: 8, backgroundColor: '#E5E7EB' },
  messagesList: { flex: 1, padding: 16 },
  messageContainer: { padding: 12, borderRadius: 8, marginBottom: 8, maxWidth: '70%' },
  sentMessage: { alignSelf: 'flex-end', backgroundColor: COLORS.primary },
  receivedMessage: { alignSelf: 'flex-start', backgroundColor: '#FFFFFF', borderColor: '#E5E7EB', borderWidth: 1 },
  messageText: { color: '#FFF', fontSize: 14 },
  messageTextt: { color: '#000', fontSize: 14 },
  messageTime: { marginTop: 4, fontSize: 10, color: '#D1D5DB', alignSelf: 'flex-end' },
  optionsContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-around', backgroundColor: '#FFFFFF', padding: 16, elevation: 3, borderRadius: 5 },
  driverImage: { width: 50, height: 50, borderRadius: 25, marginRight: 16 },
  option: { alignItems: 'center', marginBottom: 16 },
  optionText: { marginTop: 8, fontSize: 12, color: '#000' },
  inputContainer: { flexDirection: 'row', alignItems: 'center', padding: 16, backgroundColor: '#FFFFFF', borderTopWidth: 1, borderTopColor: '#E5E7EB' },
  textInput: { flex: 1, backgroundColor: '#F3F4F6', borderRadius: 8, padding: 12, marginHorizontal: 8 },
  sendButton: { backgroundColor: COLORS.primary, padding: 8, borderRadius: 8, resizeMode: 'contain', height: 40, width: 40 },
});
