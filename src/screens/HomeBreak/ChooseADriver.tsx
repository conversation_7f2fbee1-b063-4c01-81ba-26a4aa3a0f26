import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme'; // Update your theme path
import { images } from '../../constants';
import MapView, { Circle, Marker, Polyline } from 'react-native-maps';
import { useNavigation, useRoute } from '@react-navigation/native';
import socketService from '../../services/SocketService';
import RideDetails from '../Earnings/RideDetails';
import { PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import BackToHome from '../../components/Backbtn';

const ChooseADriver = () => {
  const route = useRoute();
  const updatedRideDetails = route.params || {};
  console.log('updatedRideDetails', JSON.stringify(updatedRideDetails, null, 2));
  
  const [userLocation, setUserLocation] = useState({ type: "Point", coordinates: [0, 0] });
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(true);
  const [isBottomSheetPaymentVisible, setBottomSheetPaymentVisible] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [isAddCardModalVisible, setAddCardModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [drivers, setDrivers] = useState([]);
  const [isDrivers, setIsDrivers] = useState(null);
  const [mapLoading, setMapLoading] = useState(true);
  const [mapReady, setMapReady] = useState(false);
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  
  const navigation = useNavigation();

  // Map region
  const [region, setRegion] = useState({
    latitude: 33.5186, // Default latitude
    longitude: -86.8104, // Default longitude
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  });

  // State for storing coordinates
  const [pickupLatLng, setPickupLatLng] = useState({
    latitude: 0,
    longitude: 0
  });
  
  const [destinationLatLng, setDestinationLatLng] = useState({
    latitude: 0,
    longitude: 0
  });

  useEffect(() => {
    const geocodeLocations = async () => {
      try {
        setMapLoading(true);
        
        if (!updatedRideDetails?.from || !updatedRideDetails?.to?.[0]?.place) {
          setMapLoading(false);
          return;
        }
        
        // Use geocoding from OpenStreetMap like in ConfirmPickUp
        const pickupResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(updatedRideDetails.from)}&format=json&limit=1`);
        const pickupData = await pickupResponse.json();
        
        const dropoffResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(updatedRideDetails.to[0].place)}&format=json&limit=1`);
        const dropoffData = await dropoffResponse.json();

        if (pickupData.length > 0 && dropoffData.length > 0) {
          const pickup = {
            latitude: parseFloat(pickupData[0].lat),
            longitude: parseFloat(pickupData[0].lon)
          };
          
          const destination = {
            latitude: parseFloat(dropoffData[0].lat),
            longitude: parseFloat(dropoffData[0].lon)
          };

          setPickupLatLng(pickup);
          setDestinationLatLng(destination);

          // Create midpoint for map centering
          const midLatitude = (pickup.latitude + destination.latitude) / 2;
          const midLongitude = (pickup.longitude + destination.longitude) / 2;
          
          // Calculate appropriate zoom level based on distance
          const latDelta = Math.abs(pickup.latitude - destination.latitude) * 1.5;
          const lngDelta = Math.abs(pickup.longitude - destination.longitude) * 1.5;
          
          // Set route coordinates for the polyline
          setRouteCoordinates([pickup, destination]);
          
          // Update region
          setRegion({
            latitude: midLatitude,
            longitude: midLongitude,
            latitudeDelta: Math.max(0.05, latDelta),
            longitudeDelta: Math.max(0.05, lngDelta),
          });
          
          // Update the user location for socket service
          setUserLocation({ 
            type: "Point", 
            coordinates: [pickup.longitude, pickup.latitude] 
          });
          
        } else {
          throw new Error('Could not find coordinates for one or both locations');
        }
      } catch (error) {
        console.error("Error setting up map:", error);
        Alert.alert("Map Error", error.message || "Failed to load map coordinates");
      } finally {
        setMapLoading(false);
      }
    };

    geocodeLocations();
  }, [updatedRideDetails]);

  const handleContinue = () => {
    setBottomSheetVisible(true);
    setBottomSheetPaymentVisible(true);
  };

  // Check if coordinates are valid
  const areCoordinatesValid = () => {
    return (
      pickupLatLng.latitude !== 0 &&
      pickupLatLng.longitude !== 0 &&
      destinationLatLng.latitude !== 0 &&
      destinationLatLng.longitude !== 0
    );
  };

  // Update socket connection for ride requests
  useEffect(() => {
    socketService.connect();
    const handleRideRequested = (response) => {
      if (response.success) {
        console.log('Ride request successful:', response?.message);
        Alert.alert("Ride request Successful", response?.message);
      } else {
        console.log('Ride request failed:', response?.message);
        Alert.alert('No driver Available', response?.message || 'An error occurred during the ride request.');
        navigation.goBack();
      }
    };

    socketService.onEvent('rideRequested', handleRideRequested);

    // Only emit the event when we have the ride details
    if (updatedRideDetails.from && updatedRideDetails.to) {
      socketService.emitEvent('requestRide', updatedRideDetails);
    }

    return () => {
      socketService.socket.off('rideRequested', handleRideRequested);
      socketService.disconnect();
    };
  }, [updatedRideDetails]);

  useEffect(() => {
    setIsLoading(true);

    // Setup a listener for the driver data
    const handleAvailableDrivers = (response) => {
      setIsLoading(false);  
      console.log('Available drivers:', response.finalResult);
      
      if (response.success) {
        setDrivers(response?.finalResult); 
      } else {
        Alert.alert("Error", response.message || "Failed to get available drivers");
      }
    };

    // Register the event listener
    socketService.onEvent('availableDriversForRide', handleAvailableDrivers);

    return () => {
      // Clean up the event listener
      socketService.socket.off('availableDriversForRide', handleAvailableDrivers);
    };
  }, []);

  const handleConfirmPickup = () => {
    const requestData = {
      driverId: isDrivers?.driver?.driverId,   
      rideId: isDrivers?.rideId,   
    };
    
    console.log('Requesting driver with data:', JSON.stringify(requestData, null, 2));
    
    socketService.emitEvent('requestDriver', requestData, (serverResponse) => {
      if (serverResponse.success) {
        console.log("Driver request successful:", serverResponse.message);
      } else {
        console.log("Driver request failed:", serverResponse.message);
        Alert.alert("Request Failed", serverResponse.message || "Failed to request the driver.");
        navigation.navigate("ArrangYourRide");
      }
    });
  };

  useEffect(() => {
    socketService.onEvent('requestDriver', (response) => {
      if (response.success) {
        console.log("Confirmation of driver request:", response.message);
        navigation.navigate('RidePayment', response);
      } else {
        console.log("Error in confirming driver request:", response.message);
        Alert.alert("Driver Confirmation Failed", response.message);
      }
    });
  
    return () => {
      socketService.socket.off('requestDriver');
    };
  }, []);

  useEffect(() => { 
    socketService.onEvent('nearbyDrivers', (response) => {
      console.log('Nearby drivers:', JSON.stringify(response.finalResult, null, 2));
    });
  }, []);

  useEffect(() => {
    if (areCoordinatesValid()) {
      console.log('Emitting getNearbyDrivers event');
      socketService.emitEvent("getNearbyDrivers", { 
        location: [pickupLatLng.longitude, pickupLatLng.latitude], 
        type: "Point" 
      });
    }
  }, [pickupLatLng.latitude, pickupLatLng.longitude]);

  const handleMapReady = () => {
    setMapReady(true);
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />}

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Searching For Driver</Text>
        <BackToHome />
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {(isBottomSheetVisible || isBottomSheetPaymentVisible) && (
          <View style={styles.overlay} />
        )}

        {mapLoading ? (
          <View style={[styles.map, styles.loadingContainer]}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading map...</Text>
          </View>
        ) : (
          <MapView
            style={styles.map}
            region={region}
            onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
            onMapReady={handleMapReady}
          >
            {/* Marker for pickup location */}
            <Marker
              coordinate={pickupLatLng}
              title={updatedRideDetails.from || "Pickup"}
              description={updatedRideDetails.pickupPoint || "Pickup location"}
              pinColor="green"
            >
              <View style={styles.markerContainer}>
                <View style={styles.markerPickup}>
                  <View style={styles.markerInner} />
                </View>
              </View>
            </Marker>

            {/* Marker for destination */}
            <Marker
              coordinate={destinationLatLng}
              title={updatedRideDetails.to?.[0]?.place || "Destination"}
              description="Destination location"
              pinColor="red"
            >
              <View style={styles.markerContainer}>
                <View style={styles.markerDestination}>
                  <View style={styles.markerInner} />
                </View>
              </View>
            </Marker>

            {/* Draw a line between pickup and destination */}
            <Polyline
              coordinates={routeCoordinates}
              strokeColor={COLORS.primary}
              strokeWidth={3}
              lineDashPattern={[1, 3]}
            />

            {/* Show available drivers on the map */}
            {drivers.map((item, index) => {
              if (item?.driver?.currentLocation?.coordinates) {
                const driverPos = {
                  latitude: item.driver.currentLocation.coordinates[1],
                  longitude: item.driver.currentLocation.coordinates[0],
                };
                
                return (
                  <Marker
                    key={`driver-${item.driver.driverId}`}
                    coordinate={driverPos}
                    title={`${item.driver.firstName} ${item.driver.lastName}`}
                    description={`Rating: ${item.driver.ratings || "New"}`}
                  >
                    <View style={styles.driverMarker}>
                      <Image 
                        source={images.carIcon} 
                        style={styles.carIcon} 
                        resizeMode="contain"
                      />
                    </View>
                  </Marker>
                );
              }
              return null;
            })}
          </MapView>
        )}
      </ScrollView>

      {isBottomSheetVisible && (
        <View style={styles.bottomSheet}>
          <Text style={styles.bottomSheetTitle}>Choose a driver</Text>
          <Text style={styles.bottomSheetSubtitle}>Prices are given by the driver</Text>

          <ScrollView showsVerticalScrollIndicator={false}>
            {isLoading ? (
              <ActivityIndicator size="large" color={COLORS.primary} />
            ) : (
              drivers?.map((item, index) => (
                <TouchableOpacity
                  key={item?.driver?.driverId}
                  style={[
                    styles.driverCard,
                    selectedDriver === index && styles.selectedDriverCard,
                  ]}
                  onPress={() => {
                    setSelectedDriver(index);
                    setIsDrivers(item);
                  }}
                >
                  <Image
                    source={
                      item?.car?.carImgUrl 
                        ? { uri: item.car.carImgUrl } 
                        : require('../../assets/images/driver1.png')
                    }
                    style={styles.driverImage}
                  />
                  <View style={styles.driverInfo}>
                    <Text style={styles.driverName}>{`${item?.driver?.firstName} ${item?.driver?.lastName}`}</Text>
                    <Text style={styles.driverDetails}>{`Mobile: ${item?.driver?.mobileNumber}`}</Text>
                  </View>
                  <View style={styles.driverRatingPrice}>
                    <Text style={styles.driverRating}>{item?.driver?.ratings || "New"} ★</Text>
                    <Text style={styles.driverPrice}>${item?.price?.toFixed(2)}</Text>
                  </View>
                </TouchableOpacity>
              ))
            )}
          </ScrollView>

          {selectedDriver !== null && (
            <View style={styles.paymentContainer}>
              <TouchableOpacity style={{}}>
                <Image source={images.carRide} style={styles.paymentIcon} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.paymentButton} onPress={handleConfirmPickup}>
                <Text style={styles.paymentButtonText}>Make payment →</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}

      {isBottomSheetPaymentVisible && (
        <View style={styles.bottomSheet}>
          <View style={styles.paymentModalHeader}>
            <Text style={styles.addCardModalTitle}>
              Add payment method before choosing drive
            </Text>
            <Text style={styles.paymentModalSubtitle}>
              Set your payment method
            </Text>
          </View>

          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setBottomSheetPaymentVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.paymentOption} onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.paypal} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>PayPal</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>

          <TouchableOpacity style={styles.paymentOption} onPress={() => setAddCardModalVisible(true)}>
            <Image source={images.masterCard} style={styles.paymentIcon} />
            <Text style={styles.paymentOptionText}>Wallet</Text>
            <TouchableOpacity>
              <Image source={images.next} style={styles.nextIcon} />
            </TouchableOpacity>
          </TouchableOpacity>
        </View>
      )}

      {isAddCardModalVisible && (
        <View style={styles.bottomSheet}>
          <View style={styles.addCardModalHeader}>
            <Text style={styles.addCardModalTitle}>Add payment method</Text>
            <Text style={styles.addCardModalSubtitle}>
              Update your card details.
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setAddCardModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer1}>
            <View>
              <Text style={styles.inputLabel}>Name on card</Text>
            </View>
            <View>
              <TextInput
              placeholderTextColor={COLORS.grey}
              style={[styles.input, { width: '100%' }]} placeholder="Olivia Rhye" />
            </View>
          </View>

          <View style={styles.inputContainer1}>
            <Text style={styles.inputLabel}>Card number</Text>
            <View style={styles.cardNumberInput}>
              <Image source={images.masterCard} style={styles.cardIcon} />
              <TextInput
                style={[styles.input, { width: '90%' }]}
                placeholder="1234 1234 1234 1234"
                placeholderTextColor={COLORS.grey}
                keyboardType="number-pad"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.inputContainer1}>
              <Text style={styles.inputLabel}>Expiry</Text>
              <TextInput style={[styles.input, { width: 150, marginRight: 50 }]} 
              placeholderTextColor={COLORS.grey}
              placeholder="06 / 2024" />
            </View>
            <View style={styles.inputContainer1}>
              <Text style={styles.inputLabel}>CVV</Text>
              <TextInput 
              placeholderTextColor={COLORS.grey}
              style={[styles.input, { width: 150 }]} placeholder="..." secureTextEntry />
            </View>
          </View>

          <TouchableOpacity style={styles.addButton} onPress={() => navigation.navigate('RideDetailScreen')}>
            <Text style={styles.addButtonText}>Add</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <View style={styles.saveCardContainer}>
            <TouchableOpacity style={styles.checkbox} />
            <Text style={styles.saveCardText}>Save Card</Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  loadingText: {
    ...FONTS.body3,
    color: COLORS.grey,
    marginTop: 10,
  },
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerPickup: {
    height: 20,
    width: 20,
    backgroundColor: 'green',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  markerDestination: {
    height: 20,
    width: 20,
    backgroundColor: 'red',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  markerInner: {
    height: 8,
    width: 8,
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  driverMarker: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: 5,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  carIcon: {
    width: 20,
    height: 20,
    tintColor: COLORS.primary,
  },
  addCardModalHeader: {
    marginBottom: 20,
    alignItems: 'center',
  },
  addCardModalTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  addCardModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  inputContainer: {
    marginBottom: 15,
    flexDirection: 'row'
  },
  inputContainer1: {
    marginBottom: 15,
    // flexDirection:'row'
  },
  inputLabel: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
 
  cardNumberInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    paddingHorizontal: 10,
  },
  cardIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  row: {
    // width: 100,
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  addButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    marginTop: 20,
  },
  addButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  cancelButton: {
    marginTop: 10,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
  },
  cancelButtonText: {
    ...FONTS.body3,
    color: COLORS.grey,
  },
  saveCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  checkbox: {
    width: 20,
    marginTop: 10,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginRight: 10,
  },
  saveCardText: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  paymentModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  paymentModalHeader: {
    marginBottom: 20,
    marginHorizontal: 40,
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    padding: 10,
  },
  paymentModalTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 8,
  },
  paymentModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  paymentIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  paymentOptionText: {
    flex: 1,
    ...FONTS.body3,
    fontSize: 14,
    marginLeft: 10,
    color: COLORS.black,
  },
  nextIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.grey,
    resizeMode: 'contain',
  },
  selectedDriverCard: {
    borderWidth: 0.5,
    backgroundColor: COLORS.light_blue,
    borderColor: COLORS.primary, // Highlight border for selected driver
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  paymentIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    borderRadius: SIZES.radius,
  },
  paymentButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    marginLeft: 10,
    paddingVertical: 12,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  paymentButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.7,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body4,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    // padding: SIZES.padding,
  },
  inputRow: {},
  routeIndicator: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft: -20,
  },
  input1: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: '100%',
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  bottomSheet: {
    height: Dimensions.get('window').height * 0.6,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  bottomSheetSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    paddingBottom: 20,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: SIZES.radius,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    resizeMode: 'contain',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  driverDetails: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
  },
  driverRatingPrice: {
    alignItems: 'flex-end',
    fontSize: 12,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default ChooseADriver;