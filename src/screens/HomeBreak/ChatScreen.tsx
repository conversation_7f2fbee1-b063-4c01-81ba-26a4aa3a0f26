import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  Alert,
  Platform,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, images } from '../../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { launchCamera, launchImageLibrary } from "react-native-image-picker";
import socketService from '../../services/SocketService';

export default function ChatScreen() {
  const navigation = useNavigation();
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const route = useRoute();
  const { rideId } = route.params;
  const [isOptionsVisible, setIsOptionsVisible] = useState(false);
  useEffect(() => {
    const handleNewMessageFromDriver = (data) => {
      console.log("New message from driver:", data);

      if (data?.message && Array.isArray(data.message)) {
        const formattedMessages = data.message.map((msg) => ({
          id: msg._id,
          text: msg.message,
          mediaLink: msg.mediaLink || null,
          isSent: msg.from !== "Passenger", // Mark driver messages as sent
        }));

        // Ensure new messages are appended to the state immediately
        setMessages((prevMessages) => {
          const newMessages = [...prevMessages, ...formattedMessages];
          return newMessages;
        });

        // Optionally save to AsyncStorage if needed
        AsyncStorage.setItem(`chat_${rideId}`, JSON.stringify(formattedMessages));
      }
    };

    socketService.onEvent("chatWithDriver", handleNewMessageFromDriver);

    return () => {
      socketService.socket.off("chatWithDriver", handleNewMessageFromDriver);
    };
  }, [rideId]);  // Dependency on rideId to reload messages for each ride


  useEffect(() => {
    const loadMessages = async () => {
      try {
        const savedMessages = await AsyncStorage.getItem(`chat_${rideId}`);
        if (savedMessages) {
          setMessages(JSON.parse(savedMessages)); // Load stored messages immediately
        }
      } catch (error) {
        console.error("Error loading messages:", error);
      }
    };

    loadMessages(); // Load messages from storage when screen mounts

    return () => {
      socketService.socket.off("chatWithDriver");
    };
  }, [rideId]);

  const handleMessageChange = (text) => {
    setMessage(text);
    setSelectedFile(null);
  };

  const handleMediaSelection = (type) => {
    const options = { mediaType: "photo", includeBase64: true };

    const callback = (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
        return;
      }
      if (response.errorMessage) {
        Alert.alert("Error", response.errorMessage);
        return;
      }
      if (!response.assets || response.assets.length === 0) {
        console.log("No file selected");
        return;
      }

      const file = response.assets[0];

      setSelectedFile({
        uri: file.uri,
        base64: file.base64,
        type: file.type,
        name: file.fileName,
      });

      setMessage(""); // Prevent message input when file is selected
    };

    if (type === "camera") {
      launchCamera(options, callback);
    } else {
      launchImageLibrary(options, callback);
    }
  };

  const sendMessage = async () => {
    if (!message.trim() && !selectedFile) {
      Alert.alert("Error", "Enter a message or select a file.");
      return;
    }





    let chatData = {
      id: Date.now().toString(),
      rideId: rideId,
      message: selectedFile ? "" : message.trim(),
      file: selectedFile,
      from: "Passenger", // Ensure the 'from' field is set to 'Passenger'
      isSent: true,
      status: "pending",
    };

    // Update UI immediately
    setMessages((prevMessages) => {
      const updatedMessages = [...prevMessages, chatData];
      AsyncStorage.setItem(`chat_${rideId}`, JSON.stringify(updatedMessages)); // Save messages immediately
      return updatedMessages;
    });

    try {
      socketService.emitEvent("chatWithDriver", chatData, (response) => {
        if (response?.success) {
          setMessages((prevMessages) =>
            prevMessages.map((msg) =>
              msg.id === chatData.id ? { ...msg, status: "sent" } : msg
            )
          );
        } else {
          throw new Error("Message failed");
        }
      });
    } catch (error) {
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.id === chatData.id ? { ...msg, status: "failed" } : msg
        )
      );
      Alert.alert("Message Failed", "Your message could not be sent.");
    }

    setMessage("");
    setSelectedFile(null);
  };



  const renderMessage = ({ item }) => {
    const isImage = item.mediaLink && typeof item.mediaLink === "string" && item.mediaLink.trim() !== "" && !item.mediaLink.endsWith(".mp3");
    const isText = item.text && typeof item.text === "string" && item.text.trim() !== "";

    if (!isText && !isImage) return null;

    return (
      <View
        style={[
          styles.messageContainer,
          item.isSent ? styles.sentMessage : styles.receivedMessage, // Left for Driver, Right for Passenger
        ]}
      >
        {isImage && (
          <Image
            source={{ uri: item.mediaLink }}
            style={{ width: 120, height: 120, borderRadius: 10 }}
            resizeMode="cover"
          />
        )}

        {isText && (
          <Text style={item.isSent ? styles.messageText : styles.messageTextt}>
            {item.text}
          </Text>
        )}

        {item.isSent && (
          <Text style={{ fontSize: 10, color: "#D1D5DB", alignSelf: "flex-end" }}>
            {item.isSent ? "✓✓" : "✓"}
          </Text>
        )}
      </View>
    );
  };



  return (
    <LinearGradient colors={[COLORS.light_blue, '#fff']} style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={{ width: 30, height: 30, resizeMode: 'contain' }} />
        </TouchableOpacity>
        <View style={styles.profileContainer}>
          <Image source={images.driver1} style={styles.profileImage} />
          <Text style={styles.profileName}>Olive Rodrigo</Text>
        </View>
        <View style={styles.headerIcons}>
          <Image source={images.Videoicon} style={{ width: 30, height: 30, resizeMode: 'contain', marginHorizontal: 5 }} />
          <Image source={images.call} style={{ width: 15, height: 15, resizeMode: 'contain' }} />
        </View>
      </View>

      <Text style={styles.infoText}>
        Keep your account safe—don’t share personal or account information here
      </Text>

      <FlatList
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item?._id?.toString()}
      />

      {isOptionsVisible && (
        <View style={styles.optionsContainer}>
          {[
            { id: '1', icon: images.camera, text: 'Camera' },
            { id: '2', icon: images.Micicon, text: 'Record' },
            { id: '3', icon: images.Contacticon, text: 'Contact' },
            { id: '4', icon: images.Imageicon, text: 'Gallery', },
          ].map((option) => (
            <TouchableOpacity
              key={option.id}
              style={styles.option}
              onPress={() => {
                if (option.text === "Camera") handleMediaSelection("camera");
                if (option.text === "Gallery") handleMediaSelection("gallery");
              }}
            >
              <Image source={option.icon} style={[styles.driverImage, { width: 30, height: 30, marginRight: 0 }]} />
              <Text style={styles.optionText}>{option.text}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {selectedFile && (
        <Image
          source={{ uri: selectedFile.uri }}
          style={{ width: 120, height: 120, borderRadius: 10, marginTop: 5 }}
          resizeMode="cover"
        />
      )}

      <View style={styles.inputContainer}>
        <TouchableOpacity onPress={() => setIsOptionsVisible(!isOptionsVisible)}>
          <Image source={images.addd} style={{ width: 50, resizeMode: 'contain', height: 50 }} />
        </TouchableOpacity>

        <TextInput
          style={styles.textInput}
          placeholder="Type a message..."
          placeholderTextColor={COLORS.grey}
          value={message}
          onChangeText={handleMessageChange}
          onSubmitEditing={sendMessage}
        />

        <TouchableOpacity onPress={sendMessage}>
          <Image source={images.send} style={{ width: 50, resizeMode: 'contain', height: 50 }} />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}



const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.light_blue },
  header: { flexDirection: 'row', alignItems: 'center', padding: 16, backgroundColor: '#FFFFFF', borderBottomWidth: 1, borderBottomColor: '#E5E7EB',
    paddingTop: 20,
    

   },
  profileContainer: { flexDirection: 'row', alignItems: 'center', flex: 1, marginLeft: 16 },
  profileImage: { width: 40, height: 40, borderRadius: 20, marginRight: 8 },
  profileName: { fontSize: 16, fontWeight: 'bold' },
  headerIcons: { flexDirection: 'row', alignItems: 'center' },
  icon: { marginRight: 16 },
  infoText: { textAlign: 'center', fontSize: 12, color: '#6B7280', padding: 8, backgroundColor: '#fff' },
  messagesList: { flex: 1, padding: 16 },
  messageContainer: { padding: 12, borderRadius: 8, marginBottom: 8, maxWidth: '70%', marginHorizontal: 10 },
  sentMessage: { alignSelf: 'flex-start', backgroundColor: '#fff' },
  receivedMessage: { alignSelf: 'flex-end', backgroundColor: COLORS.primary, borderColor: '#E5E7EB', borderWidth: 1 },
  messageText: { color: '#000', fontSize: 14 },
  messageTextt: { color: '#fff', fontSize: 14 },
  messageTime: { marginTop: 4, fontSize: 10, color: '#D1D5DB', alignSelf: 'flex-end' },
  optionsContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-around', backgroundColor: '#FFFFFF', padding: 16, elevation: 3, borderRadius: 5 },
  driverImage: { width: 50, height: 50, borderRadius: 25, marginRight: 16 },
  option: { alignItems: 'center', marginBottom: 16 },
  optionText: { marginTop: 8, fontSize: 12, color: '#000' },
  inputContainer: { flexDirection: 'row', alignItems: 'center', padding: 16, backgroundColor: '#FFFFFF', borderTopWidth: 1, borderTopColor: '#E5E7EB' },
  textInput: { flex: 1, backgroundColor: '#F3F4F6', borderRadius: 8, padding: 12, marginHorizontal: 8 },
  sendButton: { backgroundColor: COLORS.primary, padding: 8, borderRadius: 8, resizeMode: 'contain', height: 40, width: 40 },
});

