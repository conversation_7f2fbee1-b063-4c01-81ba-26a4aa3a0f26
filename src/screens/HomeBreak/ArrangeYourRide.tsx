

import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  Image,
  ScrollView,
  Dimensions,
} from "react-native";
import io from "socket.io-client";
import Geolocation from "react-native-geolocation-service";
import GooglePlacesAutocompleteComponent from "../../components/shared/GooglePlaceAutoComplete";
import { COLORS, FONTS, images, SIZES } from "../../constants";
import { useNavigation, useRoute } from "@react-navigation/native";
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON> } from "react-native-maps";
import { Platform } from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import AsyncStorage from "@react-native-async-storage/async-storage";
import { BASE_URL } from "../../../Baseurl";

const SOCKET_URL = `${BASE_URL}/passenger`;
const socket = io(SOCKET_URL, {
  transports: ["websocket"],
  withCredentials: true,
});

const ArrangeYourRide = () => {
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [showPaymentOptions, setShowPaymentOptions] = useState(false);
  const route = useRoute();
  const { rideType, scheduleDate, scheduleTime } = route.params || {};
  const body = route.params || {};
  const [activeInput, setActiveInput] = useState('from');
  const setActiveInputField = (field) => {
    console.log("Active input set to:", field);
    setActiveInput(field);
  };


  const [rideDetails, setRideDetails] = useState({
    from: "",
    fromId: "",
    fromCoordinates: { type: "Point", coordinates: [null, null] },
    to: [],
    personnalRide: true,
    noOffPassengers: 1,
    pickupPoint: "",
    rideType: rideType || "personal",
    scheduleDate: scheduleDate || "",
    scheduleTime: scheduleTime || "",
  });

  // console.log(JSON.stringify(rideDetails,null,2), 'rideDetails');

  const [responseMessage, setResponseMessage] = useState("");
  const [availableDrivers, setAvailableDrivers] = useState([]);
  const [newRideId, setNewRideId] = useState("");
  const [selectedView, setSelectedView] = useState('recent');
  const [currentLocation, setCurrentLocation] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [destination, setDestination] = useState(null);
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [loadingDrivers, setLoadingDrivers] = useState(false);
  const [userLocation, setUserLocation] = useState({ type: "Point", coordinates: [null, null] });
  const [loading, setLoading] = useState(false);
  const [driverRequested, setDriverRequested] = useState(null);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [priceRange, setPriceRange] = useState("");
  const [recentSearches, setRecentSearches] = useState([]);

  useEffect(() => {
    const loadRecentSearches = async () => {
      const storedLocations = await AsyncStorage.getItem('recentLocations');
      if (storedLocations) {
        setRecentSearches(JSON.parse(storedLocations));
      }
    };

    loadRecentSearches();
  }, []);

  const handleSelectLocation = (location) => {
    console.log("Handling select location for:", activeInput, location.description); // Debug output

    if (activeInput === 'from') {
      setRideDetails(prev => ({
        ...prev,
        from: location.description,
        fromId: location.place_id,
        fromCoordinates: {
          type: "Point",
          coordinates: [location.location.lng, location.location.lat]
        },
      }));
    } else {
      const index = activeInput;
      if (typeof index === 'number') {
        setRideDetails(prev => {
          const newTo = [...prev.to];
          newTo[index] = {
            place: location.description,
            placeId: location.place_id,
            locationCoordinates: {
              type: "Point",
              coordinates: [location.location.lng, location.location.lat]
            }
          };
          return { ...prev, to: newTo };
        });
      } else {
        console.error("Active input index is not a number:", index);
      }
    }
  };


  const navigation = useNavigation();




  const requestLocationPermission = async () => {
    let permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });

    try {
      const result = await request(permission);
      if (result === RESULTS.GRANTED) {
        // console.log("✅ Location permission granted");
        return true;
      } else {
        console.log("❌ Location permission denied");
        Alert.alert("Permission Denied", "Please enable location permissions in Settings.");
        return false;
      }
    } catch (error) {
      console.error("⚠️ Permission Error:", error);
      return false;
    }
  };


  const checkLocationServices = async () => {
    return new Promise((resolve) => {
      Geolocation.getCurrentPosition(
        () => resolve(true), // ✅ Location services enabled
        (error) => {
          if (error.code === 2) { // 🚨 Location provider disabled
            Alert.alert(
              "Location Services Disabled",
              "Please enable location services in Settings.",
              [{ text: "Go to Settings", onPress: () => Linking.openSettings() }, { text: "Cancel", style: "cancel" }]
            );
          }
          resolve(false);
        },
        { enableHighAccuracy: false, timeout: 5000 }
      );
    });
  };


  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.log("⛔ Location permission denied. Exiting function.");
      return null;
    }

    const isLocationEnabled = await checkLocationServices();
    if (!isLocationEnabled) {
      console.log("⛔ Location services disabled. Exiting function.");
      return null;
    }


    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;

          // const latitude = 4.2649;
          // const longitude = 8.1577;
          const newLocation = { type: 'Point', coordinates: [longitude, latitude] };

          console.log("✅ Location Retrieved:", newLocation);
          setUserLocation(newLocation);
          // resolve(newLocation); // ✅ Return the location
        },
        (error) => {
          // console.error("❌ Location Error:", error);
          // Alert.alert("Location Error", error.message);
          reject(null); // ❌ Return `null` if failed
        },
        {
          enableHighAccuracy: false,
          timeout: 30000,
          maximumAge: 5000,
        }
      );
    });
  };

  /** ✅ Handle Selecting "From" Location */
  const handleFromSelect = async (location) => {
    if (!location?.location?.lat || !location?.location?.lng) {
      Alert.alert("Error", "Invalid location selected");
      return;
    }

    setRideDetails((prev) => ({
      ...prev,
      from: location?.description,
      fromId: location?.place_id,
      fromCoordinates: {
        type: "Point",
        coordinates: [location?.location?.lng, location?.location?.lat], // Set proper coordinates
      },
    }));

    console.log("📍 Selected 'From' Location:", location);
  };

  const handleToSelect = (index, location) => {
    setRideDetails(prev => {
      const newTo = [...prev.to];
      newTo[index] = {
        place: location.description,
        placeId: location.place_id,
        locationCoordinates: {
          type: "Point",
          coordinates: [location.location.lng, location.location.lat]
        }
      };
      return { ...prev, to: newTo };
    });
  };

  // Function to add a new destination input field
  const addToPlace = () => {
    setRideDetails(prev => ({
      ...prev,
      to: [...prev.to, { place: "", placeId: "", locationCoordinates: { type: "Point", coordinates: [null, null] } }]
    }));
  };

  // Function to remove a destination input field
  const removeToPlace = (index) => {
    setRideDetails(prev => {
      const newTo = [...prev.to];
      newTo.splice(index, 1);
      return { ...prev, to: newTo };
    });
  };
  const handleSubmit = () => {
    console.log("🚗 Ride Details:", JSON.stringify(rideDetails, null, 2));

    // Validate 'From' location
    if (!rideDetails.fromCoordinates.coordinates[0] || !rideDetails.fromCoordinates.coordinates[1]) {
      Alert.alert("Error", "Please select a valid 'From' location.");
      return;
    }

    // Validate 'To' locations
    for (let dest of rideDetails.to) {
      if (!dest.locationCoordinates.coordinates[0] || !dest.locationCoordinates.coordinates[1]) {
        Alert.alert("Error", "Please select a valid 'To' location.");
        return;
      }
    }

    // Navigate to the confirmation screen with ride details
    navigation.navigate('ConfirmPickUp', rideDetails);
  };

  const getRideTypeTitle = (rideType) => {
    const rideTypeMapping = {
      personal: "Book a Personal Ride",
      group: "Book a Group Ride",
      split: "Book a Split Fare Ride",
      delivery: "Book a Delivery Package",
      reservation: "Book a Reserved Ride",
    };

    return rideTypeMapping[rideType] || "Book a Ride"; // Default if not found
  };

  const handleFromChange = (text) => {
    setRideDetails(prev => ({ ...prev, from: text }));
  };

  // Function to handle changes in each 'To' input
  const handleToChange = (index, text) => {
    setRideDetails(prev => {
      const newTo = [...prev.to];
      newTo[index] = { ...newTo[index], place: text };
      return { ...prev, to: newTo };
    });
  };

  // useEffect(() => {
  //   getCurrentLocation();
  // }, []);

  return (
    <View style={styles.container}>
      {/* Header */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}


      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>                     {getRideTypeTitle(rideDetails.rideType)}</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* 
     
        {/* Input Fields */}


        <View style={styles.inputContainer}>
          <Image source={images.routeIndicator} style={styles.routeIndicator} />

          <View>
            <GooglePlacesAutocompleteComponent
              placeholder="From"
              onSelect={handleFromSelect}
              onChangeText={handleFromChange}
              value={rideDetails.from}
              onFocus={() => {
                console.log('From input focused');  // Debugging statement
                setActiveInputField('from');
              }}
            />

            {rideDetails.to.map((destination, index) => (
              <View key={destination.placeId || index} style={styles.destinationContainer}>
                <GooglePlacesAutocompleteComponent
                  placeholder={`To where ${index + 1}`}
                  onSelect={(location) => handleToSelect(index, location)}
                  onChangeText={(text) => handleToChange(index, text)}
                  value={destination.place}
                  onFocus={() => {
                    console.log(`To input ${index} focused`);  // Debugging statement
                    setActiveInputField(index);
                  }}

                />
                {index > 0 && (
                  <TouchableOpacity
                    onPress={() => removeToPlace(index)}
                    style={styles.removeButton}
                  >
                    <Text>❌</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}

          </View>


          <TouchableOpacity onPress={addToPlace}>
            <Image source={images.add} style={styles.addIcon} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.locationCard}>
          <Image source={images.locate1} style={styles.locationIcon} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationTitle}>Saved Places</Text>
          </View>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.locationCard}
          onPress={() => setSelectedView(selectedView === 'map' ? 'recent' : 'map')}
        >
          <Image source={images.locate1} style={styles.locationIcon} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationTitle}>Select Location via Map</Text>
          </View>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>



        {selectedView === 'map' ? (
          <MapView
            style={styles.map}
            region={currentLocation ? { ...currentLocation, latitudeDelta: 0.05, longitudeDelta: 0.05 } : [8.3792, 4.5244]}
          >
            {currentLocation && <Marker coordinate={currentLocation} title="Current Location" />}
            {selectedLocation && <Marker coordinate={selectedLocation} title="Selected Location" />}
            {destination && <Marker coordinate={destination} title="Destination" />}
            {currentLocation && destination && (
              <Polyline coordinates={[currentLocation, destination]} strokeWidth={5} strokeColor={COLORS.primary} />
            )}
          </MapView>
        ) : (
          <View>

            <Text style={styles.sectionTitle}>Recent Search</Text>

          { recentSearches.length > 0 &&
           <ScrollView style={styles.locationCardContainer}>
              {recentSearches.map((location, index) => (
                <TouchableOpacity key={index} style={styles.locationCard} onPress={() => handleSelectLocation(location)}>
                  <Image source={images.transcat} style={styles.locationIcon} />
                  <View style={styles.locationTextContainer}>
                    <Text style={styles.locationTitle}>{location.description}</Text>
                    <Text style={styles.locationSubtitle}>
                      Latitude: {location.location.lat}, Longitude: {location.location.lng}
                    </Text>
                  </View>
                  <Image source={images.next} style={styles.nextIcon} />
                </TouchableOpacity>
              ))}
            </ScrollView>}

            {/* <Text style={styles.sectionTitle}>Recent Search</Text> */}
            {/* {Array(2)
              .fill(null)
              .map((_, index) => (
                <TouchableOpacity key={index} style={styles.locationCard}>
                  <Image source={images.locate} style={styles.locationIcon} />
                  <View style={styles.locationTextContainer}>
                    <Text style={styles.locationTitle}>AMLI 7th Street Station.</Text>
                    <Text style={styles.locationSubtitle}>
                      2601 West 7th St. Fort Worth, Texas
                    </Text>
                  </View>
                  <Image source={images.next} style={styles.nextIcon} />
                </TouchableOpacity>
              ))} */}
          </View>
        )}


      </ScrollView>




      <TouchableOpacity
        style={styles.continueButtonn}
        onPress={handleSubmit}
      >
        <Text style={styles.continueButtonText}>Search for Driver</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  destinationContainer: {
    flexDirection: 'column'
  },

  paymentOptions: {
    marginTop: 20,
    zIndex: 999,
  },
  paymentOptionsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
  },
  paymentOptionButton: {
    backgroundColor: "#f0f0f0", // light gray button
    padding: 20,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: "center", // center text/loader
    justifyContent: "center",
  },
  paymentOptionText: {
    fontSize: 14,
    color: COLORS.black, // ensures text is visible on light gray
    textAlign: "center",
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  locationCardContainer: {
    height: 300,
    marginVertical: 10,
  },
  continueButton: {
    position: "static",
    width: 350,
    bottom: 20,
    alignSelf: 'center',
    paddingHorizontal: 0,
    marginTop: 20,
    backgroundColor: COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    // alignItems: 'center',
  },
  continueButtonn: {
    position: "absolute",
    width: 350,
    bottom: 50,
    alignSelf: 'center',
    paddingHorizontal: 0,
    marginTop: 20,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    // alignItems: 'center',
  },
  continueButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
    textAlign: 'center',
  },
  continueButtonTextt: {
    ...FONTS.h3,
    color: COLORS.primary,
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },

  scrollContent: {
    padding: SIZES.padding,
  },
  rideTypeContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  rideTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.white,
    marginHorizontal: 5,
    width: 100,
  },
  activeButton: {
    backgroundColor: COLORS.primary,
    width: 100,
  },
  buttonText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginLeft: 5,
  },
  activeText: {
    color: COLORS.white,
  },
  icon: {
    width: 20,
    height: 20,
  },
  // inputContainer: {
  //   marginBottom: 20,
  //   wldth: '50%',
  //   flexDirection: 'row',
  // },

  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  inputRow: {
    width: '70%',
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  sectionTitle: {
    ...FONTS.h4,
    color: COLORS.primary,
    fontSize: 14,
    marginVertical: 10,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    marginVertical: 5,
  },
  locationIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationTitle: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  locationSubtitle: {
    ...FONTS.body4,
    fontSize: 10,
    color: COLORS.grey,
  },
  nextIcon: {
    width: 10,
    height: 10,
  },



  addCardModalHeader: {
    marginBottom: 20,
    alignItems: 'center',
  },
  addCardModalTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  addCardModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  inputContainer: {
    marginBottom: 15,
    width: '94%',
    flexDirection: 'row'
  },
  inputContainer1: {
    marginBottom: 15,
    // flexDirection:'row'
  },
  inputLabel: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  // input: {
  //   backgroundColor: COLORS.light_blue,
  //   borderRadius: SIZES.radius,
  //   padding: 20,
  //   ...FONTS.body3,
  //   fontSize:22,
  //   color: COLORS.black,
  // },
  cardNumberInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    paddingHorizontal: 10,
  },
  cardIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  row: {
    // width: 100,
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  addButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    marginTop: 20,
  },
  addButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  cancelButton: {
    marginTop: 10,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
  },
  cancelButtonText: {
    ...FONTS.body3,
    color: COLORS.grey,
  },
  saveCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  checkbox: {
    width: 20,
    marginTop: 10,

    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginRight: 10,
  },
  saveCardText: {
    ...FONTS.body4,
    color: COLORS.grey,
  },

  paymentModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  paymentModalHeader: {
    marginBottom: 20,
    marginHorizontal: 40,
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    padding: 10,
  },
  paymentModalTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 8,
  },
  paymentModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  // closeButton: {
  //   position: 'absolute',
  //   top: 30,
  //   right: 30,
  // },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  paymentIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  paymentOptionText: {
    flex: 1,
    ...FONTS.body3,
    fontSize: 14,
    marginLeft: 10,
    color: COLORS.black,
  },
  nextIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.grey,
    resizeMode: 'contain',
  },

  selectedDriverCard: {
    borderWidth: 0.5,
    backgroundColor: COLORS.light_blue,
    borderColor: COLORS.primary, // Highlight border for selected driver
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  paymentIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    borderRadius: SIZES.radius,
  },
  paymentButton: {
    flex: 1,
    backgroundColor: COLORS.white,
    marginLeft: 10,
    paddingVertical: 20,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    borderWidth: 0.5,
    borderColor: COLORS.primary,
  },
  paymentButtonText: {

    color: COLORS.primary,
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body4,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    padding: SIZES.padding,
  },

  inputRow: {},
  routeIndicator: {
    width: 60,
    height: 90,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft: -20,
  },
  input1: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: '100%',
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  bottomSheet: {
    height: Dimensions.get('window').height * 0.7,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white, // Overlay color with transparency
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1, // Ensure it appears above other content
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  bottomSheetSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    paddingBottom: 20,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: SIZES.radius,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    resizeMode: 'contain',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  driverDetails: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
  },
  driverRatingPrice: {
    alignItems: 'flex-end',
    fontSize: 12,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default ArrangeYourRide;