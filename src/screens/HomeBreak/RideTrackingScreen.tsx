import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Share,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Circle } from 'react-native-maps';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import Geolocation from '@react-native-community/geolocation';
import socketService from '../../services/SocketService';
import { BASE_URL } from '../../../Baseurl';

const RideTrackingScreen = () => {
  const [isInviteModalVisible, setInviteModalVisible] = useState(false);
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [rideDetails, setRideDetails] = useState(null);
  const [geocodingLoading, setGeocodingLoading] = useState(false);
  const route = useRoute();
  const { rideId } = route.params || {};
  
  // Set default coordinates for Nigeria
  const [currentLocation, setCurrentLocation] = useState({
    latitude: 6.5244,
    longitude: 3.3792,
    latitudeDelta: 0.015,
    longitudeDelta: 0.015
  });

  // Track both driver location and destination
  const [driverLocation, setDriverLocation] = useState(null);
  const [destinationLocation, setDestinationLocation] = useState(null);

  // Geocode addresses to coordinates
  const geocodeAddress = async (address) => {
    try {
      setGeocodingLoading(true);
      console.log(`Geocoding address: ${address}`);
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(address)}&format=json&limit=1`,
        {
          headers: {
            'User-Agent': 'RideFuzeApp/1.0'  // It's good practice to identify your app to the API
          }
        }
      );
      
      const data = await response.json();
      console.log('Geocoding response:', data);
      
      if (data && data.length > 0) {
        return {
          latitude: parseFloat(data[0].lat),
          longitude: parseFloat(data[0].lon)
        };
      } else {
        console.warn(`No geocoding results found for address: ${address}`);
        return null;
      }
    } catch (error) {
      console.error('Error geocoding address:', error);
      return null;
    } finally {
      setGeocodingLoading(false);
    }
  };

  useEffect(() => {
    if (!rideId) {
      console.error('No rideId provided');
      Alert.alert('Error', 'No ride information available');
      return;
    }

    console.log('RideTrackingScreen mounted with rideId:', rideId);
    
    const updateDriverLocation = () => {
      try {
        socketService.emitEvent("trackRide", { rideId }); // Request latest location

        socketService.onEvent("trackRide", async (data) => {
          console.log("Driver location update received:", data);

          if (data?.getDriverLocation?.location?.coordinates) {
            const [longitude, latitude] = data.getDriverLocation.location.coordinates;
            
            // Ensure we have valid coordinates
            if (typeof latitude === 'number' && typeof longitude === 'number' && 
                !isNaN(latitude) && !isNaN(longitude)) {
              
              // Only update if we have non-zero coordinates
              if (latitude !== 0 && longitude !== 0) {
                // Set driver's current location
                setDriverLocation({
                  latitude,
                  longitude
                });
                
                // Update the map's view region
                setCurrentLocation({
                  latitude,
                  longitude,
                  latitudeDelta: 0.015,
                  longitudeDelta: 0.015
                });
                
                console.log("Updated driver location:", { latitude, longitude });
              } else {
                console.warn("Zero coordinates received, attempting to geocode from ride details");
                // If we have ride details but zero coordinates, try to geocode the pickup location
                if (rideDetails?.from) {
                  const geocoded = await geocodeAddress(rideDetails.from);
                  if (geocoded) {
                    setDriverLocation(geocoded);
                    setCurrentLocation({
                      ...geocoded,
                      latitudeDelta: 0.015,
                      longitudeDelta: 0.015
                    });
                    console.log("Using geocoded pickup location:", geocoded);
                  }
                }
              }
            } else {
              console.warn("Invalid coordinates received:", { latitude, longitude });
            }
          } else {
            console.warn("Missing location coordinates in data:", data);
          }
        });
      } catch (error) {
        console.error("Error in tracking update:", error);
      }
    };

    updateDriverLocation();
    fetchRideDetails();
    
    // Get user's current location as fallback
    Geolocation.getCurrentPosition(
      position => {
        const { latitude, longitude } = position.coords;
        setCurrentLocation(prev => ({
          ...prev,
          latitude,
          longitude
        }));
        console.log("Got user's current position:", { latitude, longitude });
      },
      error => console.error("Geolocation error:", error.message),
      { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
    );

    const interval = setInterval(updateDriverLocation, 10000);

    return () => {
      clearInterval(interval);
      socketService.socket.off("trackRide");
    };
  }, [rideId]);

  // Use effect to geocode addresses when ride details change
  useEffect(() => {
    if (rideDetails) {
      // Geocode destination if we have an address but no coordinates
      if (rideDetails.to && rideDetails.to[0]?.place && !destinationLocation) {
        (async () => {
          const destCoords = await geocodeAddress(rideDetails.to[0].place);
          if (destCoords) {
            setDestinationLocation(destCoords);
            console.log("Geocoded destination location:", destCoords);
          }
        })();
      }
      
      // Geocode pickup location if we have no driver location yet
      if (rideDetails.from && !driverLocation) {
        (async () => {
          const pickupCoords = await geocodeAddress(rideDetails.from);
          if (pickupCoords) {
            setDriverLocation(pickupCoords);
            setCurrentLocation({
              ...pickupCoords,
              latitudeDelta: 0.015,
              longitudeDelta: 0.015  
            });
            console.log("Geocoded pickup location:", pickupCoords);
          }
        })();  
      }
    }
  }, [rideDetails]);

  const fetchRideDetails = async () => {
    if (!rideId) return;
    
    try {
      console.log('Fetching ride details...', rideId);
      setLoading(true);

      const response = await fetch(
        `${BASE_URL}/api/rides/getPassengerRide/${rideId}`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
      const data = await response.json();
      console.log('Ride Details:', JSON.stringify(data, null, 2));
      
      if (response.ok && data?.data) {
        setRideDetails(data.data);
        
        // Extract and set destination coordinates
        if (data.data.to && data.data.to.length > 0 && 
            data.data.to[0].locationCoordinates && 
            data.data.to[0].locationCoordinates.coordinates) {
          
          const [destLongitude, destLatitude] = data.data.to[0].locationCoordinates.coordinates;
          
          if (typeof destLatitude === 'number' && typeof destLongitude === 'number' &&
              destLatitude !== 0 && destLongitude !== 0) {
            setDestinationLocation({
              latitude: destLatitude,
              longitude: destLongitude
            });
            console.log("Set destination location:", { latitude: destLatitude, longitude: destLongitude });
          } else {
            // If we have invalid destination coordinates but have an address, try to geocode
            if (data.data.to[0].place) {
              const destCoords = await geocodeAddress(data.data.to[0].place);
              if (destCoords) {
                setDestinationLocation(destCoords);
                console.log("Geocoded destination location:", destCoords);
              }
            }
          }
        }
        
        // Set initial driver location from source coordinates if available
        if (data.data.fromCoordinates && data.data.fromCoordinates.coordinates) {
          const [srcLongitude, srcLatitude] = data.data.fromCoordinates.coordinates;
          
          if (typeof srcLatitude === 'number' && typeof srcLongitude === 'number' &&
              srcLatitude !== 0 && srcLongitude !== 0) {
            setDriverLocation({
              latitude: srcLatitude,
              longitude: srcLongitude
            });
            
            // Also update the current map view
            setCurrentLocation({
              latitude: srcLatitude,
              longitude: srcLongitude,
              latitudeDelta: 0.015,
              longitudeDelta: 0.015
            });
            console.log("Set initial driver location:", { latitude: srcLatitude, longitude: srcLongitude });
          } else {
            // If we have invalid source coordinates but have a pickup address, try to geocode
            if (data.data.from) {
              const pickupCoords = await geocodeAddress(data.data.from);
              if (pickupCoords) {
                setDriverLocation(pickupCoords);
                setCurrentLocation({
                  ...pickupCoords,
                  latitudeDelta: 0.015,
                  longitudeDelta: 0.015  
                });
                console.log("Geocoded pickup location:", pickupCoords);
              }
            }
          }
        }
      } else {
        console.error('Failed to fetch ride details:', data?.message || 'Unknown error');
        Alert.alert('Error', 'Failed to fetch ride details');
      }
    } catch (error) {
      console.error('Error fetching ride details:', error);
      Alert.alert('Error', 'There was a problem retrieving ride information');
    } finally {
      setLoading(false);
    }
  };

  const shareRide = async () => {
    if (!rideDetails) {
      Alert.alert('Error', 'No ride details available to share');
      return;
    }
    
    try {
      // Extract key ride details
      const pickup = rideDetails?.from || "Unknown Pickup";
      const dropoff = rideDetails?.to?.[0]?.place || "Unknown Destination";
      const distance = rideDetails?.kmDistance || "Unknown Distance";
      const price = rideDetails?.charge ? `$${rideDetails.charge}` : "N/A";
      const carModel = rideDetails?.carDetails?.model || "Unknown Car";
      const carColor = rideDetails?.carDetails?.color || "Unknown Color";
      const rideStatus = rideDetails?.status || "Unknown Status";
      const paymentMethod = rideDetails?.paymentMethod || "Not Specified";
  
      // Format the message for sharing
      const message = `🚗 *Ride Details* 🚗\n\n` +
        `📍 *Pickup:* ${pickup}\n` +
        `📍 *Dropoff:* ${dropoff}\n` +
        `🛣️ *Distance:* ${distance} km\n` +
        `💰 *Price:* ${price}\n` +
        `🚘 *Car:* ${carColor} ${carModel}\n` +
        `🛑 *Status:* ${rideStatus}\n` +
        `💳 *Payment Method:* ${paymentMethod}\n\n` +
        `🔗 Stay safe and track my ride!`;
  
      // Share the message
      const result = await Share.share({ message });
  
      if (result.action === Share.sharedAction) {
        console.log('Ride details shared successfully');
      } else if (result.action === Share.dismissedAction) {
        console.log('Ride sharing dismissed');
      }
    } catch (error) {
      console.error('Error sharing ride:', error.message);
      Alert.alert('Error', 'Could not share ride details');
    }
  };

  const startTrackingRide = () => {
    if (!rideId) {
      Alert.alert("Error", "No active ride found.");
      return;
    }

    try {
      socketService.emitEvent("trackRide", { rideId });
      console.log("Started tracking ride:", rideId);
    } catch (error) {
      console.error("Error starting tracking:", error);
      Alert.alert("Error", "Could not start tracking ride");
    }
  };
  
  // Calculate estimated time of arrival based on distance and average speed
  const calculateETA = () => {
    if (!driverLocation || !destinationLocation) {
      return 'Calculating...';
    }
    
    try {
      // Calculate distance in kilometers using the Haversine formula
      const calculateDistance = (lat1, lon1, lat2, lon2) => {
        const R = 6371; // Radius of the Earth in km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        
        const a = 
          Math.sin(dLat/2) * Math.sin(dLat/2) +
          Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
          Math.sin(dLon/2) * Math.sin(dLon/2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c; // Distance in km
        
        return distance;
      };
      
      // Calculate distance between driver and destination
      const distanceInKm = calculateDistance(
        driverLocation.latitude, 
        driverLocation.longitude, 
        destinationLocation.latitude, 
        destinationLocation.longitude
      );
      
      // Assume average speed based on region and traffic conditions
      // Use the ride details if available, otherwise use default values
      let averageSpeed = 30; // Default 30 km/h in urban areas
      
      // Adjust speed based on time of day (rush hour, etc)
      const currentHour = new Date().getHours();
      if (currentHour >= 7 && currentHour <= 9) {
        // Morning rush hour
        averageSpeed = 20;
      } else if (currentHour >= 16 && currentHour <= 19) {
        // Evening rush hour
        averageSpeed = 15;
      } else if (currentHour >= 22 || currentHour <= 5) {
        // Late night / early morning - less traffic
        averageSpeed = 40;
      }
      
      // Calculate time in minutes
      const timeInHours = distanceInKm / averageSpeed;
      const timeInMinutes = Math.ceil(timeInHours * 60);
      
      // Add a small buffer for stops, traffic lights, etc.
      const buffer = Math.ceil(timeInMinutes * 0.1); // 10% buffer
      const totalTime = timeInMinutes + buffer;
      
      // Format the output based on the time
      if (totalTime < 1) {
        return 'Less than a minute';
      } else if (totalTime === 1) {
        return '1 minute';
      } else if (totalTime < 60) {
        return `${totalTime} mins`;
      } else {
        const hours = Math.floor(totalTime / 60);
        const mins = totalTime % 60;
        if (mins === 0) {
          return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
        } else {
          return `${hours} ${hours === 1 ? 'hour' : 'hours'} ${mins} mins`;
        }
      }
    } catch (error) {
      console.error('Error calculating ETA:', error);
      return 'Calculating...';
    }
  };

  // Render loading indicator
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Loading ride details...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 0 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 0 }} />}

      {geocodingLoading && (
        <View style={styles.geocodingLoader}>
          <ActivityIndicator size="small" color={COLORS.primary} />
          <Text style={styles.geocodingText}>Getting location coordinates...</Text>
        </View>
      )}

      <MapView
        style={styles.map}
        region={currentLocation}
      >
        {/* Driver location marker */}
        {driverLocation && (
          <Marker 
            coordinate={driverLocation}
            title="Driver"
            description="Driver's current location"
          >
            <Image source={images.CarSingle} style={{ width: 40, height: 40 }} />
          </Marker>
        )}
        
        {/* Destination marker */}
        {destinationLocation && (
          <Marker 
            coordinate={destinationLocation}
            title="Destination"
            description="Your destination"
            pinColor="#00AA00"
          />
        )}
        
        {/* Circle around driver location */}
        {driverLocation && (
          <Circle
            center={driverLocation}
            radius={300}
            fillColor="rgba(0, 150, 255, 0.2)"
            strokeColor="rgba(0, 150, 255, 0.5)"
          />
        )}
        
        {/* Route line between driver and destination */}
        {driverLocation && destinationLocation && (
          <Polyline
            coordinates={[
              driverLocation,
              destinationLocation
            ]}
            strokeWidth={3}
            strokeColor={COLORS.primary}
            lineDashPattern={[1]}
          />
        )}
      </MapView>

      {/* Ride Information */}
      <View style={styles.infoContainer}>
        <Text style={styles.arrivalTime}>Arriving in... {calculateETA()}</Text>
        <Text style={styles.carInfo}>
          {rideDetails?.carDetails?.model || 'Car'} | {rideDetails?.carDetails?.color || 'N/A'} |{' '}
          {rideDetails?.carDetails?.registrationNumber || 'N/A'}
        </Text>

        <View style={styles.rideDetailsContainer}>
          <View>
            <Text style={styles.rideDetailsTitle}>Tracking Ride</Text>

            {rideDetails ? (
              <>
                <Text style={styles.rideDetailsTitle}>
                  {`Driver is en route from ${rideDetails?.from || 'pickup'} to ${rideDetails?.to?.[0]?.place || 'destination'}`}
                </Text>
                <Text style={styles.rideDetailsTitle}>
                  {`Current Distance: ${rideDetails?.kmDistance || '0'} km`}
                </Text>
                <Text style={styles.rideDetailsSubtitle}>
                  {`Status: ${rideDetails?.status || 'Pending'}`}
                </Text>
              </>
            ) : (
              <Text style={styles.rideDetailsSubtitle}>{loading ? 'Loading ride details...' : 'Waiting for ride updates...'}</Text>
            )}

            <TouchableOpacity onPress={startTrackingRide} style={styles.trackButton}>
              <Text style={styles.trackButtonText}>Refresh Tracking</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <TouchableOpacity style={styles.shareButton} onPress={() => setInviteModalVisible(true)}>
          <Text style={styles.shareButtonText}>Rate the RIDE</Text>
        </TouchableOpacity> 
      </View>

      {isInviteModalVisible && (
        <ScrollView style={styles.inviteModal}>
          <View style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setInviteModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>

            <Image source={images.notify} style={styles.illustration} />

            <Text style={styles.inviteTitle}>Ride with friends and foe</Text>
            <Text style={styles.inviteSubtitle}>
              <Text style={styles.inviteBrand}>RideFuze</Text> is a transparent way to move around the city
            </Text>

            <TouchableOpacity style={styles.inviteButton} onPress={() => shareRide()}>
              <Text style={styles.inviteButtonText}>Share ride</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.FeedBackButton} onPress={() => {
              if (rideId) {
                navigation.navigate('RideFeedbackScreen', {rideId});
              } else {
                Alert.alert('Error', 'No ride information available');
              }
            }}>
              <Text style={styles.FeedBackButtonText}>Give Us Feedback</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              onPress={() => {
                if (rideId) {
                  navigation.navigate('RideForgotSomethingScreen', {rideId});
                } else {
                  Alert.alert('Error', 'No ride information available');
                }
              }}
              style={[styles.FeedBackButton, { borderColor: COLORS.primary, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }]}
            >
              <Image source={images.forgotbag} style={styles.forgotbag} />
              <Text style={styles.FeedBackButtonText}>Forgot Something</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    padding: 20,
  },
  loadingText: {
    ...FONTS.body3,
    marginTop: 10,
    color: COLORS.black,
  },
  geocodingLoader: {
    position: 'absolute',
    top: 10,
    alignSelf: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 10,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1000,
  },
  geocodingText: {
    ...FONTS.body4,
    marginLeft: 8,
    color: COLORS.black,
  },
  rideDetails: {
    padding: 20,
    backgroundColor: "#fff",
    borderRadius: 10,
    margin: 10,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 5,
    elevation: 3,
  },
  rideDetailsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  rideDetailsSubtitle: {
    fontSize: 16,
    color: "#666",
    marginBottom: 10,
  },
  trackButton: {
    marginTop: 10,
    backgroundColor: "#007bff",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  trackButtonText: {
    fontSize: 16,
    color: "#fff",
    fontWeight: "bold",
  },
  mapContainer: {
    height: 200,
    width: "100%",
    backgroundColor: "#f0f0f0",
    borderRadius: 10,
    marginTop: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  distanceText: {
    fontSize: 14,
    color: "#888",
    marginTop: 5,
  },
  arrivalTime: {
    ...FONTS.body3,
    fontSize: 15,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 5,
  },
  carInfo: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    paddingBottom: 15,
  },
  rideDetailsContainer: {
    marginBottom: 20,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 15,
    borderRadius: SIZES.radius,
  },
  shareButton: {
    marginTop: 30,
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },
  shareButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.5,
  },
  inviteModal: {
    height: Dimensions.get('window').height * 0.6,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  illustration: {
    width: 320,
    height: 150,
    marginVertical: 20,
    resizeMode: 'contain',
  },
  forgotbag: {
    width: 30,
    height: 30,
    marginHorizontal: 20,
    resizeMode: 'contain',
  },
  inviteTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  inviteSubtitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 20,
    marginHorizontal: 20,
  },
  inviteBrand: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  inviteButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    marginBottom: 10,
    alignItems: 'center',
    width: '100%',
  },
  FeedBackButton: {
    borderColor: COLORS.primary,
    borderWidth: 1,
    borderRadius: SIZES.radius,
    padding: 15,
    marginBottom: 10,
    alignItems: 'center',
    width: '100%',
  },
  inviteButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  FeedBackButtonText: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
  infoContainer: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    marginTop: -20,
  },
  heading: {
    ...FONTS.h3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 5,
  },
  subHeading: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 20,
  },
});

export default RideTrackingScreen;