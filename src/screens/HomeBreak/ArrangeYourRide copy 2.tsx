

import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  FlatList,
  ActivityIndicator,
  StyleSheet,
  Image,
  ScrollView,
  Dimensions,
} from "react-native";
import io from "socket.io-client";
import Geolocation from "react-native-geolocation-service";
import GooglePlacesAutocompleteComponent from "../../components/shared/GooglePlaceAutoComplete";
import { COLORS, FONTS, images, SIZES } from "../../constants";
import { useNavigation, useRoute } from "@react-navigation/native";
import MapView, { <PERSON><PERSON>, <PERSON>yline } from "react-native-maps";
import { BASE_URL } from "../../../Baseurl";


const SOCKET_URL = `${BASE_URL}/passenger`;
const socket = io(SOCKET_URL, {
  transports: ["websocket"],
  withCredentials: true,
});

const ArrangeYourRide = () => {
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [showPaymentOptions, setShowPaymentOptions] = useState(false);
  const route = useRoute();
  const { rideType } = route.params || {};


  const [rideDetails, setRideDetails] = useState({
    from: "",
    fromId: "",
    fromCoordinates: { type: "Point", coordinates: [null, null] },
    to: [],
    personnalRide: true,
    noOffPassengers: 1,
    pickupPoint: "",
    rideType: rideType || "personal",
  });


  const [responseMessage, setResponseMessage] = useState("");
  const [availableDrivers, setAvailableDrivers] = useState([]);
  const [newRideId, setNewRideId] = useState("");
  const [selectedView, setSelectedView] = useState('recent');
  const [currentLocation, setCurrentLocation] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [destination, setDestination] = useState(null);
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [loadingDrivers, setLoadingDrivers] = useState(false);
  const [userLocation, setUserLocation] = useState({ type: "Point", coordinates: [null, null] });
  const [loading, setLoading] = useState(false);
  const [driverRequested, setDriverRequested] = useState(null);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [priceRange, setPriceRange] = useState("");

  const navigation = useNavigation();


  useEffect(() => {
    // Get user's current location
    Geolocation.getCurrentPosition(
      (position) => {
        // const { latitude, longitude } = position.coords;
        const longitude = 7.818791099999999;
        const latitude = 3.908815999999999;
        setUserLocation({ type: "Point", coordinates: [longitude, latitude] });
      },
      (error) => console.error("Error getting location:", error),
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  }, []);

  useEffect(() => {
    if (userLocation.coordinates[0] !== null && userLocation.coordinates[1] !== null) {
      socket.emit("getNearbyDrivers", { location: userLocation.coordinates, radius: 5 });
    }
  }, [userLocation]);

  useEffect(() => {
    socket.on("payForRide", (response) => {
      console.log("✅ PAYMENT RESPONSE RECEIVED:", JSON.stringify(response, null, 2));
      setPaymentLoading(false);

      if (response.success) {
        Alert.alert("🎉 Payment Successful!", `Ride is now active.\n\nMessage: ${response.message}`);
      } else {
        Alert.alert("❌ Payment Failed", `Try again.\n\nReason: ${response.message}`);
      }
    });

    return () => {
      socket.off("payForRide");
    };
  }, []);

  useEffect(() => {
    socket.on("nearbyDrivers", (data) => {
      // console.log("🚖 NEARBY DRIVERS:", JSON.stringify(data,null,2));
      setResponseMessage(data.success ? `Drivers found: ${data.drivers.length}` : "No drivers found");
    });

    socket.on("rideRequested", (data) => {
      console.log("✅ Ride Requested:", data);
      if (!data.success) {
        setLoadingDrivers(false);
        setBottomSheetVisible(false);
        Alert.alert("No driver Available", data.message || '');
      }
      setResponseMessage(data.success ? "Ride request successful!" : "Error requesting ride");
      setAvailableDrivers(data?.drivers || []);
      setNewRideId(data.rideId);
    });



    socket.on("availableDriversForRide", (data) => {
      console.log("🚗 AVAILABLE DRIVERS FOR RIDE:", JSON.stringify(data, null, 2));
      setLoadingDrivers(false);

      if (data?.success && data?.finalResult?.length > 0) {
        setAvailableDrivers(data.finalResult);
      } else {
        setAvailableDrivers([]);
        Alert.alert("No Available Drivers", "No drivers are currently available. Please try again later.");
      }
    });


    socket.on("requestDriver", (response) => {
      setAvailableDrivers([]);
      setDriverRequested(response);
      console.log("✅ Driver Requested Response:", response);
    });

    socket.on("payForRide", (response) => {
      console.log("💳 PAYMENT RESPONSE:", response);
      setPaymentLoading(false);
    });

    socket.on("error", (data) => {
      console.error("❌ Socket Error:", data.message);
      setResponseMessage(data.message || "Error fetching nearby drivers.");
    });

    return () => {
      socket.off("nearbyDrivers");
      socket.off("rideRequested");
      socket.off("availableDriversForRide");
      socket.off("requestDriver");
      socket.off("payForRide");
      socket.off("error");
    };
  }, []);

  /** ✅ Handle Selecting "From" Location */
  const handleFromSelect = async (location) => {
    if (!location?.location?.lat || !location?.location?.lng) {
      Alert.alert("Error", "Invalid location selected");
      return;
    }

    setRideDetails((prev) => ({
      ...prev,
      from: location?.description,
      fromId: location?.place_id,
      fromCoordinates: {
        type: "Point",
        coordinates: [location?.location?.lng, location?.location?.lat], // Set proper coordinates
      },
    }));

    console.log("📍 Selected 'From' Location:", location);
  };

  /** ✅ Handle Selecting "To" Location */
  const handleToSelect = async (index, location) => {
    if (!location?.location?.lat || !location?.location?.lng) {
      Alert.alert("Error", "Invalid destination selected");
      return;
    }

    setRideDetails((prev) => {
      const newTo = [...prev.to];
      newTo[index] = {
        place: location?.description,
        placeId: location?.place_id,
        locationCoordinates: {
          type: "Point",
          coordinates: [location?.location?.lng, location?.location?.lat], // Set proper coordinates
        },
      };
      return { ...prev, to: newTo };
    });

    console.log("📍 Selected 'To' Location:", location);
  };

  const addToPlace = () => setRideDetails((prev) => ({ ...prev, to: [...prev.to, {}] }));
  const removeToPlace = (index) => {
    setRideDetails((prev) => {
      const newTo = [...prev.to];
      newTo.splice(index, 1);
      return { ...prev, to: newTo };
    });
  };




  const handleSubmit = () => {
    if (
      !rideDetails?.fromCoordinates?.coordinates[0] ||
      !rideDetails?.fromCoordinates?.coordinates[1]
    ) {
      Alert.alert("Error", "Please select a valid 'From' location.");
      return;
    }
    if (
      !rideDetails?.to.length ||
      !rideDetails?.to[0]?.locationCoordinates?.coordinates[0]
    ) {
      Alert.alert("Error", "Please select a valid 'To' location.");
      return;
    }

    // Reset driver selection and show bottom sheet with loader
    // setSelectedDriver(null);
    setShowPaymentOptions(false);
    setLoadingDrivers(true);
    setBottomSheetVisible(true);

    console.log("🚗 Requesting Ride:", JSON.stringify(rideDetails, null, 2));
    socket.emit("requestRide", rideDetails);
  };



  const handleRequestDriver = (rideId, driverId) => {
    const data = { driverId, rideId };
    console.log(`🚕 Requesting Driver ${driverId} for Ride:`, data);
    socket.emit("requestDriver", data);
  };


  const handleSelectDriver = (driverData) => {
    setSelectedDriver(driverData);
  };

  const handleRidePayment = (rideId, type) => {
    setPaymentLoading(true);

    // Example card details
    const cardDetails = {
      cardHolderName: "Ade One",
      cardNumber: "****************",
      cvv: "456",
      expiryDate: "12/25",
      cardType: "Visa",
    };

    let data;
    if (type === "card") {
      data = { rideId, paymentType: "card", cardId: "myCardId", cardDetails };
    } else if (type === "direct") {
      data = { rideId, paymentType: "direct", cardDetails };
    } else {
      data = { rideId, paymentType: "wallet" };
    }

    console.log("💳 Initiating Payment Request:", JSON.stringify(data, null, 2));
    socket.emit("payForRide", data);
  };
 
  socket.on("payForRide", (response) => {
    setPaymentLoading(false); // Stop loader when response arrives
    if (response.success) {
      Alert.alert("Payment Successful", response.message);
      navigation.navigate("RideDetail", { rideId: newRideId });
    } else {
      Alert.alert("Payment Failed", response.message);
    }
  });


  const getRideTypeTitle = (rideType) => {
    const rideTypeMapping = {
      personal: "Book a Personal Ride",
      group: "Book a Group Ride",
      split: "Book a Split Fare Ride",
      delivery: "Book a Delivery Package",
      reservation: "Book a Reserved Ride",
    };

    return rideTypeMapping[rideType] || "Book a Ride"; // Default if not found
  };

  return (
    <View style={styles.container}>
      {/* Header */}


      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>                     {getRideTypeTitle(rideDetails.rideType)}</Text>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* 
     
        {/* Input Fields */}

        {(isBottomSheetVisible) && (
          <View style={styles.overlay} />
        )}
        <View style={styles.inputContainer}>
          <Image source={images.routeIndicator} style={styles.routeIndicator} />

          <View>
            <GooglePlacesAutocompleteComponent placeholder="From" onSelect={handleFromSelect} />
            {rideDetails.to.map((_, index) => (
              <View key={index} style={styles.destinationContainer}>
                <GooglePlacesAutocompleteComponent
                  placeholder={`To where ${index + 1}`}
                  onSelect={(location) => handleToSelect(index, location)}
                />

                {index > 0 && (
                  <TouchableOpacity onPress={() => removeToPlace(index)} style={styles.removeButton}>
                    <Text>❌</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>


          <TouchableOpacity onPress={addToPlace}>
            <Image source={images.add} style={styles.addIcon} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.locationCard}>
          <Image source={images.locate1} style={styles.locationIcon} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationTitle}>Saved Places</Text>
          </View>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.locationCard}
          onPress={() => setSelectedView(selectedView === 'map' ? 'recent' : 'map')}
        >
          <Image source={images.locate1} style={styles.locationIcon} />
          <View style={styles.locationTextContainer}>
            <Text style={styles.locationTitle}>Select Location via Map</Text>
          </View>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>



        {selectedView === 'map' ? (
          <MapView
            style={styles.map}
            region={currentLocation ? { ...currentLocation, latitudeDelta: 0.05, longitudeDelta: 0.05 } : [8.3792, 4.5244]}
          >
            {currentLocation && <Marker coordinate={currentLocation} title="Current Location" />}
            {selectedLocation && <Marker coordinate={selectedLocation} title="Selected Location" />}
            {destination && <Marker coordinate={destination} title="Destination" />}
            {currentLocation && destination && (
              <Polyline coordinates={[currentLocation, destination]} strokeWidth={5} strokeColor={COLORS.primary} />
            )}
          </MapView>
        ) : (
          <View>

            <Text style={styles.sectionTitle}>Close by Pickups</Text>
            {Array(2)
              .fill(null)
              .map((_, index) => (
                <TouchableOpacity key={index} style={styles.locationCard}>
                  <Image source={images.locate1} style={styles.locationIcon} />
                  <View style={styles.locationTextContainer}>
                    <Text style={styles.locationTitle}>AMLI 7th Street Station.</Text>
                    <Text style={styles.locationSubtitle}>
                      2601 West 7th St. Fort Worth, Texas
                    </Text>
                  </View>
                  <Image source={images.next} style={styles.nextIcon} />
                </TouchableOpacity>
              ))}

            <Text style={styles.sectionTitle}>Recent Search</Text>
            {Array(2)
              .fill(null)
              .map((_, index) => (
                <TouchableOpacity key={index} style={styles.locationCard}>
                  <Image source={images.locate} style={styles.locationIcon} />
                  <View style={styles.locationTextContainer}>
                    <Text style={styles.locationTitle}>AMLI 7th Street Station.</Text>
                    <Text style={styles.locationSubtitle}>
                      2601 West 7th St. Fort Worth, Texas
                    </Text>
                  </View>
                  <Image source={images.next} style={styles.nextIcon} />
                </TouchableOpacity>
              ))}
          </View>
        )}


      </ScrollView>




      <TouchableOpacity
        style={styles.continueButtonn}
        onPress={handleSubmit}
      >
        <Text style={styles.continueButtonText}>Search for Driver</Text>
      </TouchableOpacity>

      {/* {isBottomSheetVisible && (
  <View style={styles.bottomSheet}>
    <TouchableOpacity onPress={() => setBottomSheetVisible(false)}>
      <Image source={images.cancel} style={{ width: 10, height: 10 }} />
    </TouchableOpacity>

    <Text style={styles.bottomSheetTitle}>
      {loadingDrivers ? "Searching for Drivers..." : "Choose a Driver"}
    </Text>
    <Text style={styles.bottomSheetSubtitle}>Prices are given by the driver</Text>

    {loadingDrivers ? (
      <ActivityIndicator size="large" color="black" style={{ marginTop: 60 }} />
    ) : (
      <ScrollView showsVerticalScrollIndicator={false}>
        {availableDrivers.map((driverData, index) => (
          <TouchableOpacity
            key={index}
            style={styles.driverCard}
            onPress={() => handleSelectDriver(driverData)}
          >
            <View style={styles.driverInfo}>
              <Text style={styles.driverName}>
                {driverData?.driver?.firstName} {driverData?.driver?.lastName}
              </Text>
              <Text style={styles.driverDetails}>
                {driverData?.car.model} - {driverData?.car?.color} ({driverData?.car?.year})
              </Text>
              <Text style={styles.driverRating}>
                ⭐ {driverData?.driver?.ratings} | {driverData?.driver?.totalRides} rides
              </Text>
              <Text style={styles.driverPrice}>💰 ${driverData?.price}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    )}
  </View>
)} */}

      {isBottomSheetVisible && (
        <View style={styles.bottomSheet}>
          {/* Close Button */}
          <TouchableOpacity onPress={() => setBottomSheetVisible(false)}>
            <Image source={images.cancel} style={{ width: 10, height: 10 }} />
          </TouchableOpacity>

          {/* If no driver is selected, show the list of drivers */}
          {!selectedDriver ? (
            <>
              <Text style={styles.bottomSheetTitle}>
                {loadingDrivers ? "Searching for Drivers..." : "Choose a Driver"}
              </Text>
              <Text style={styles.bottomSheetSubtitle}>
                Prices are given by the driver
              </Text>

              {loadingDrivers ? (
                <ActivityIndicator size="large" color="#000" style={{ marginTop: 60 }} />
              ) : (
                <ScrollView showsVerticalScrollIndicator={false}>
                  {availableDrivers.map((driverData, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.driverCard}
                      onPress={() => handleSelectDriver(driverData)}
                    >
                      <View style={styles.driverInfo}>
                        <Text style={styles.driverName}>
                          {driverData?.driver?.firstName} {driverData?.driver?.lastName}
                        </Text>
                        <Text style={styles.driverDetails}>
                          {driverData?.car?.model} - {driverData?.car?.color} ({driverData?.car?.year})
                        </Text>
                        <Text style={styles.driverRating}>
                          ⭐ {driverData?.driver?.ratings} | {driverData?.driver?.totalRides} rides
                        </Text>
                        <Text style={styles.driverPrice}>💰 ${driverData?.price}</Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              )}
            </>
          ) : (
            /* If a driver is selected, show driver info and allow changing driver or proceeding to payment */
            <View>
              <Text style={styles.bottomSheetTitle}>
                Selected Driver: {selectedDriver.driver.firstName} {selectedDriver.driver.lastName}
              </Text>
              <Text style={styles.bottomSheetSubtitle}>
                Car: {selectedDriver?.car?.model} - {selectedDriver?.car?.color} ({selectedDriver.car.year})
              </Text>

              {/* Button to go back and select another driver */}
              <TouchableOpacity
                style={styles.changeDriverButton}
                onPress={() => {
                  // setSelectedDriver(null);
                  setShowPaymentOptions(false);
                }}
              >
                <Text style={styles.changeDriverButtonText}>Change Driver</Text>
              </TouchableOpacity>

              {/* Proceed to Payment Button */}
              <TouchableOpacity
                style={styles.paymentButton}
                onPress={() => {
                  handleRequestDriver(newRideId, selectedDriver.driver.driverId);
                  setShowPaymentOptions(true);
                }}
              >
                <Text style={styles.paymentButtonText}>Proceed to Payment</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Payment Options Section */}
          {showPaymentOptions && (
            <View style={styles.paymentOptions}>
              <Text style={styles.paymentOptionsTitle}>Select Payment Type</Text>

              {/* Pay with Card */}
              <TouchableOpacity
                style={styles.paymentOptionButton}
                onPress={() => handleRidePayment(newRideId, "card")}
                disabled={paymentLoading}
              >
                {paymentLoading ? (
                  <ActivityIndicator size="small" color="#000" />
                ) : (
                  <Text style={styles.paymentOptionText}>Pay with Card</Text>
                )}
              </TouchableOpacity>

              {/* Pay Directly */}
              <TouchableOpacity
                style={styles.paymentOptionButton}
                onPress={() => handleRidePayment(newRideId, "direct")}
                disabled={paymentLoading}
              >
                {paymentLoading ? (
                  <ActivityIndicator size="small" color="#000" />
                ) : (
                  <Text style={styles.paymentOptionText}>Pay Directly</Text>
                )}
              </TouchableOpacity>

              {/* Pay with Wallet */}
              <TouchableOpacity
                style={styles.paymentOptionButton}
                onPress={() => handleRidePayment(newRideId, "wallet")}
                disabled={paymentLoading}
              >
                {paymentLoading ? (
                  <ActivityIndicator size="small" color="#000" />
                ) : (
                  <Text style={styles.paymentOptionText}>Pay with Wallet</Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}


    </View>
  );
}

const styles = StyleSheet.create({
  destinationContainer: {
    flexDirection: 'column'
  },

  paymentOptions: {
    marginTop: 20,
    zIndex: 999,
  },
  paymentOptionsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
  },
  paymentOptionButton: {
    backgroundColor: "#f0f0f0", // light gray button
    padding: 20,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: "center", // center text/loader
    justifyContent: "center",
  },
  paymentOptionText: {
    fontSize: 14,
    color: COLORS.black, // ensures text is visible on light gray
    textAlign: "center",
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  continueButton: {
    position: "static",
    width: 350,
    bottom: 20,
    alignSelf: 'center',
    paddingHorizontal: 0,
    marginTop: 20,
    backgroundColor: COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    // alignItems: 'center',
  },
  continueButtonn: {
    position: "static",
    width: 350,
    bottom: 20,
    alignSelf: 'center',
    paddingHorizontal: 0,
    marginTop: 20,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    // alignItems: 'center',
  },
  continueButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
    textAlign: 'center',
  },
  continueButtonTextt: {
    ...FONTS.h3,
    color: COLORS.primary,
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },

  scrollContent: {
    padding: SIZES.padding,
  },
  rideTypeContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  rideTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.white,
    marginHorizontal: 5,
    width: 100,
  },
  activeButton: {
    backgroundColor: COLORS.primary,
    width: 100,
  },
  buttonText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginLeft: 5,
  },
  activeText: {
    color: COLORS.white,
  },
  icon: {
    width: 20,
    height: 20,
  },
  inputContainer: {
    marginBottom: 20,
    flexDirection: 'row',
  },

  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  inputRow: {
    width: '70%',
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  sectionTitle: {
    ...FONTS.h4,
    color: COLORS.primary,
    fontSize: 14,
    marginVertical: 10,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    marginVertical: 5,
  },
  locationIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationTitle: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  locationSubtitle: {
    ...FONTS.body4,
    fontSize: 10,
    color: COLORS.grey,
  },
  nextIcon: {
    width: 10,
    height: 10,
  },



  addCardModalHeader: {
    marginBottom: 20,
    alignItems: 'center',
  },
  addCardModalTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  addCardModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  inputContainer: {
    marginBottom: 15,
    flexDirection: 'row'
  },
  inputContainer1: {
    marginBottom: 15,
    // flexDirection:'row'
  },
  inputLabel: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  // input: {
  //   backgroundColor: COLORS.light_blue,
  //   borderRadius: SIZES.radius,
  //   padding: 20,
  //   ...FONTS.body3,
  //   fontSize:22,
  //   color: COLORS.black,
  // },
  cardNumberInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    paddingHorizontal: 10,
  },
  cardIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  row: {
    // width: 100,
    flexDirection: 'row',
    // justifyContent: 'space-between',
  },
  addButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    marginTop: 20,
  },
  addButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  cancelButton: {
    marginTop: 10,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
  },
  cancelButtonText: {
    ...FONTS.body3,
    color: COLORS.grey,
  },
  saveCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  checkbox: {
    width: 20,
    marginTop: 10,

    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    marginRight: 10,
  },
  saveCardText: {
    ...FONTS.body4,
    color: COLORS.grey,
  },

  paymentModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  paymentModalHeader: {
    marginBottom: 20,
    marginHorizontal: 40,
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    padding: 10,
  },
  paymentModalTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 8,
  },
  paymentModalSubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
  // closeButton: {
  //   position: 'absolute',
  //   top: 30,
  //   right: 30,
  // },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  paymentIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  paymentOptionText: {
    flex: 1,
    ...FONTS.body3,
    fontSize: 14,
    marginLeft: 10,
    color: COLORS.black,
  },
  nextIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.grey,
    resizeMode: 'contain',
  },

  selectedDriverCard: {
    borderWidth: 0.5,
    backgroundColor: COLORS.light_blue,
    borderColor: COLORS.primary, // Highlight border for selected driver
  },
  paymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  paymentIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    borderRadius: SIZES.radius,
  },
  paymentButton: {
    flex: 1,
    backgroundColor: COLORS.white,
    marginLeft: 10,
    paddingVertical: 20,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    borderWidth: 0.5,
    borderColor: COLORS.primary,
  },
  paymentButtonText: {

    color: COLORS.primary,
  },
  map: {
    width: '100%',
    borderRadius: SIZES.radius,
    height: Dimensions.get('window').height * 0.5,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body4,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    padding: SIZES.padding,
  },

  inputRow: {},
  routeIndicator: {
    width: 60,
    height: 90,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft: -20,
  },
  input1: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: 250,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 10,
    width: '100%',
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  bottomSheet: {
    height: Dimensions.get('window').height * 0.7,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white, // Overlay color with transparency
    padding: SIZES.padding,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1, // Ensure it appears above other content
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 5,
  },
  bottomSheetSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    paddingBottom: 20,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: SIZES.radius,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    resizeMode: 'contain',
  },
  driverInfo: {
    flex: 1,
  },
  driverName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  driverDetails: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
  },
  driverRatingPrice: {
    alignItems: 'flex-end',
    fontSize: 12,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default ArrangeYourRide;