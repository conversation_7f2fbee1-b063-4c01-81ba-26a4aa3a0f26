import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons'; 
import { useNavigation } from '@react-navigation/native';
import { images } from '../../constants';

const CallScreen = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Back Button */}
      <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
        <Icon name="arrow-back" size={24} color="#fff" />
      </TouchableOpacity>
  
      {/* Display Calling Text or Call Status dynamically */}
      <Text style={styles.callingText}>{callStatus === 'Ringing' ? 'Ringing...' : 'Calling ...'}</Text>
  
      {/* Profile Image - Change dynamically based on call status or use default */}
      <View style={styles.profileContainer}>
        <Image
          source={profileImg ? { uri: profileImg } : images.driver1}  // Use a default image if no profile image is available
          style={styles.profileImage}
        />
      </View>
  
      {/* Name and Number - Dynamically loaded */}
      <Text style={styles.profileName}>{caller || "Driver"}</Text>  // Default name if no caller info
      <Text style={styles.phoneNumber}>{caller ? "(+44) 50 9285 3022" : "Number not available"}</Text>  // Default or specific number
  
      {/* Call Actions - Show based on call status */}
      <View style={styles.actionsContainer}>
        {callStatus === 'Incoming call' && (
          <TouchableOpacity style={styles.callButtonGreen} onPress={acceptCall}>
            <Icon name="call" size={30} color="#fff" />
          </TouchableOpacity>
        )}
  
        {(callStatus === 'Ringing' || callStatus === 'Connected' || callStatus === 'Incoming call') && (
          <TouchableOpacity style={styles.callButtonRed} onPress={endCall}>
            <Icon name="call" size={30} color="#fff" />
            <Icon name="close" size={14} color="#fff" style={styles.closeIcon} />
          </TouchableOpacity>
        )}
  
        {/* Show "Start Call" only if there is no ongoing or incoming call */}
        {(!callStatus || callStatus === 'Rejected' || callStatus === 'No Answer') && (
          <TouchableOpacity style={styles.callButtonGreen} onPress={startCall}>
            <Icon name="call" size={30} color="#fff" />
          </TouchableOpacity>
        )}

        
      </View>
      
      
    </View>
  );
  
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#000', alignItems: 'center', justifyContent: 'center' },
  
  backButton: { position: 'absolute', top: 50, left: 20 },
  
  callingText: { fontSize: 22, fontFamily: 'Poppins-Bold', color: '#fff', marginTop: 100 },
  
  profileContainer: { marginVertical: 40 },
  profileImage: { width: 150, height: 150, borderRadius: 75 },
  
  profileName: { fontSize: 24, fontFamily: 'Poppins-Bold', color: '#fff', marginBottom: 5 },
  phoneNumber: { fontSize: 16, fontFamily: 'Poppins-Regular', color: '#ccc' },

  actionsContainer: { flexDirection: 'row', marginTop: 40 },
  callButtonRed: { backgroundColor: '#E00000', width: 70, height: 70, borderRadius: 35, justifyContent: 'center', alignItems: 'center', marginHorizontal: 30 },
  callButtonGreen: { backgroundColor: '#00B800', width: 70, height: 70, borderRadius: 35, justifyContent: 'center', alignItems: 'center', marginHorizontal: 30 },
  
  closeIcon: { position: 'absolute', bottom: 15, right: 15, fontSize: 12 },
});

export default CallScreen;
