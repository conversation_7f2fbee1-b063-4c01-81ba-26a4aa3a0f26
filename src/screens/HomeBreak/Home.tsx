import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme'; // Update your theme path
import { images } from '../../constants';
import MapView, { Circle, Marker } from 'react-native-maps';
import { useNavigation } from '@react-navigation/native';
import GooglePlacesAutocompleteComponent from '../../components/shared/GooglePlaceAutoComplete';
import { useSelector } from 'react-redux';
import socketService from '../../services/SocketService';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Home = () => {
  const navigation = useNavigation();
  const user = useSelector(state => state?.store?.user) || [];
  const [query, setQuery] = useState('');
 
  const handleSelectLocation = (location) => {
    console.log("Selected:", location);
    setQuery(location.description);  // Update the input with the selected location's description
    loadRecentSearches();  // Refresh recent searches after a new selection
  };


 
useEffect(() => { 
  socketService.onEvent('nearbyDrivers', (response) => {
    console.log('Nearby drivers:', JSON.stringify(response.finalResult,null,2));
    
  });
}, []);

  useEffect(() => {
    console.log('User location:');
      socketService.emitEvent("getNearbyDrivers", { location: [8.1584839,4.2626603], type: "Point" });
      // socketService.emitEvent("getNearbyDrivers", { location: userLocation.coordinates, radius: 5 });
  
  }, [ ]);
    // Map region
    const [region, setRegion] = useState({
      latitude: 33.5186, // Birmingham latitude
      longitude: -86.8104, // Birmingham longitude
      latitudeDelta: 0.1,
      longitudeDelta: 0.1,
    });
    const [radius, setRadius] = useState(30000); 
    
    const [recentSearches, setRecentSearches] = useState([]);

    const loadRecentSearches = async () => {
      try {
        const locations = await AsyncStorage.getItem('recentLocations');
        console.log('Recent locations:', locations);
        
        if (locations) {
          setRecentSearches(JSON.parse(locations));
        }
      } catch (error) {
        console.error("Error loading recent searches from AsyncStorage:", error);
      }
    };
  
    useEffect(() => {
      loadRecentSearches();
    }, []);
  
  
  return (
    <View style={styles.container}>
    {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
    {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}
   
      <ScrollView contentContainerStyle={styles.scrollContent}>

       <Text>Welcome, {user?.firstName} {user?.lastName}</Text>
       <GooglePlacesAutocompleteComponent
         value={query}   
         onChangeText={setQuery}   
         onSelect={handleSelectLocation} 
        placeholder="Enter location"
       
       
        // onSelect={(location) => {
        //   console.log("Selected:", location);
        //   loadRecentSearches();  
        // }}
      />

      {/* Location Suggestions */}
{recentSearches.length > 0 &&
      <ScrollView style={styles.locationCardContainer}>
        {recentSearches.map((location, index) => (
          <TouchableOpacity key={index} style={styles.locationCard}  onPress={() => handleSelectLocation(location)}>
            <Image source={images.transcat} style={styles.locationIcon} />
            <View style={styles.locationTextContainer}>
              <Text style={styles.locationTitle}>{location.description}</Text>
              <Text style={styles.locationSubtitle}>
                Latitude: {location.location.lat}, Longitude: {location.location.lng}
              </Text>
            </View>
            <Image source={images.next} style={styles.nextIcon} />
          </TouchableOpacity>
        ))}
      </ScrollView>
      }

        {/* Drivers Nearby */}

        <TouchableOpacity style={styles.driversNearbyCard}>
          <Text style={styles.driversNearbyTitle}>Drivers nearby</Text>
          <Text style={styles.driversNearbySubtitle}>Pickups near →</Text>
          <Image source={images.locationBeep} style={styles.pinIcon} />
        </TouchableOpacity>

        {/* Services Section */}
        <Text style={styles.sectionTitle}>Our services</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.servicesContainer}>
            {[
              { icon: images.ride, label: 'Ride' , onPress:()=>navigation.navigate('ArrangeYourRide', { rideType: "personal" })},
              { icon: images.reserve, label: 'Reserve',onPress:()=>navigation.navigate('PickDate', { rideType: "schedule" }) },
              // { icon: images.reserve, label: 'Reserve',onPress:()=>navigation.navigate('ArrangeYourRide', { rideType: "schedule" }) },
              { icon: images.gride, label: 'Group ride',onPress:()=>navigation.navigate('ArrangeYourRide', { rideType: "group" }) },
               
              // { icon: images.bus, label: 'Buses fare' , onPress:()=>navigation.navigate('ViewFare')},
              { icon: images.delivery, label: 'Delivery' , onPress:()=>navigation.navigate('ArrangeYourRide', { rideType: "delivery" })},
            ].map((service, index) => (
              <TouchableOpacity onPress={service.onPress} key={index} style={styles.serviceCard}>
                <Image source={service.icon} style={styles.serviceIcon} />
                <Text style={styles.serviceText}>{service.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        {/* Promotional Cards */}
        <Text style={styles.sectionTitle}>Ways to use <Text style={{color:COLORS.primary}}>RideFuze</Text></Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.horizontalScrollContainer}>
          {[
            { image: images.way1, title: 'Schedule ride in to the future →', label: 'Book a ride' ,onPress:()=>navigation.navigate('PickDate', { rideType: "schedule" })}, 
            { image: images.way2, title: 'We help you with delivery →', label: 'Send and receive parcels swiftly'  , onPress:()=>navigation.navigate('ArrangeYourRide', { rideType: "delivery" })}, 
            { image: images.way1, title: 'Order a ride→', label: 'Get to your location easily',  onPress:()=>navigation.navigate('ArrangeYourRide', { rideType: "personal" })},
            

          ].map((promoImage, index) => (
            <TouchableOpacity style={{}} onPress={promoImage.onPress}>
            <Image key={index} source={promoImage?.image} style={styles.promoCard} />
            <Text style={[styles.locationTitle,{marginVertical:4}]}>{promoImage.title}</Text>
            <Text style={[styles.serviceText,{fontSize:11}]}>{promoImage.label}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <TouchableOpacity style={{}}>
        
          <Image source={images.homeBanner2} style={{width:'100%', marginVertical:10,height:130,borderRadius:7,resizeMode:'contain'}} />
        </TouchableOpacity>

        {/* Other Services */}
        <Text style={styles.sectionTitle}>Other services</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.otherServicesContainer}>
          {[
            { image: images.image1, label: 'Group ride →' ,title: 'Schedule ride in to the future',},
            { image: images.image, label: 'Public transport →' ,title: 'Schedule ride in to the future',},
          ].map((service, index) => (
        <TouchableOpacity>

            <View key={index} style={styles.otherServiceCard}>
              <Image source={service.image} style={styles.otherServiceImage} />
              <Text style={[styles.locationTitle,{marginVertical:3}]}>{service.label}</Text>

              <Text style={[styles.serviceText,{fontSize:11}]}>{service.title}</Text>

            </View> 
              </TouchableOpacity>
          ))}
        </View>
        </ScrollView> 
     

        {/* Map Section */}
        <Text style={styles.sectionTitle}>Around you</Text>
     

        <MapView
        style={styles.map}
        region={region}
        onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
      >
        {/* Circle showing the search area */}
        <Circle
          center={{ latitude: region.latitude, longitude: region.longitude }}
          radius={radius} // Circle radius in meters
          strokeColor={COLORS.primary}
          fillColor="rgba(0, 122, 255, 0.2)"
        />
        {/* Marker in the center */}
        <Marker
          coordinate={{ latitude: region.latitude, longitude: region.longitude }}
          draggable
          onDragEnd={(e) => {
            const { latitude, longitude } = e.nativeEvent.coordinate;
            setRegion({ ...region, latitude, longitude });
          }}
        />
      </MapView>

      </ScrollView>
 
     

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
   map: {
    alignSelf: 'center',
      width: '100%',
      borderRadius: SIZES.radius,
      height: 200,
      position: 'relative',
      bottom: 0,
    },
  searchBarContainer: {
    // paddingHorizontal: SIZES.padding,
    paddingVertical: 10,
  },
  searchBar: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: 13,
    ...FONTS.body3,
    color: COLORS.black,
  },
  scrollContent: {
    paddingHorizontal: SIZES.padding,
  },
  locationCardContainer: {
    maxHeight: 150,
    marginVertical: 10,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    // backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    marginVertical: 5,
  },
  locationIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationTitle: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  locationSubtitle: {
    ...FONTS.body4,
    fontSize: 11,

    color: COLORS.grey,
  },
  nextIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain'
  },
  driversNearbyCard: {
    paddingVertical: 15,
    height: 100,
    backgroundColor: COLORS.primary,
    borderRadius: 7,
    padding: 15,
    marginVertical: 10,
  },
  driversNearbyTitle: {
    ...FONTS.h3,
    fontSize: 17,

    color: COLORS.white,
  },
  driversNearbySubtitle: {
    ...FONTS.body4,
    marginVertical: 20,
    fontSize: 14,

    color: COLORS.white,
  },
  pinIcon: {
    width: 60,
    height: 60,
    position: 'absolute',
    resizeMode: 'contain',
    right: 15,
    bottom: 25,
  },
  sectionTitle: {
    ...FONTS.h3,
    color: COLORS.tertiaryText,
    marginVertical: 10,
  },
  servicesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,

  },
  serviceCard: {
    alignItems: 'center',
    backgroundColor: COLORS.white,
    marginHorizontal: 5,
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 7
  },
  serviceIcon: {
    width: 40,
    height: 40,
    marginBottom: 5,
    resizeMode: 'contain',
  },
  serviceText: {
    ...FONTS.body5,
    fontSize: 10,
    color: COLORS.black,
  },
  horizontalScrollContainer: {
    marginVertical: 10,
  },
  promoCard: {
    width: 250,
    height: 100,
    borderRadius: 7,
    // resizeMode:'contain',
    marginRight: 15,
  },
  otherServicesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  otherServiceCard: {
    // alignItems: 'center',
  },
  otherServiceImage: {
    width: 250,
    height: 100,
    marginRight:15,
    borderRadius: 7,
    marginBottom: 5,
  },
  otherServiceText: {
    ...FONTS.body5,
    color: COLORS.black,
  },
  mapImage: {
    width: '100%',
    height: 200,
    borderRadius: SIZES.radius,
  },
  bottomNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    backgroundColor: COLORS.white,
  },
  navItem: {
    alignItems: 'center',
  },
  navIcon: {
    width: 24,
    height: 24,
    marginBottom: 5,
  },
  navLabel: {
    ...FONTS.body6,
    color: COLORS.black,
  },
});

export default Home;
