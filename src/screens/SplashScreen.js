import React, { useRef, useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import { images } from '../constants';

const SplashScreen = ({ }) => {

  return (
    <View style={styles.container}>
        <View>
          <Image
            source={images.splash} style={{ height: '100%', width:'100%'}}
            resizeMode='cover'
          />
        </View>
      </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SplashScreen;
