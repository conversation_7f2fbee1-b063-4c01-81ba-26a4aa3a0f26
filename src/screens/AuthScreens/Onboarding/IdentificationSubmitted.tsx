 

import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const IdentificationSubmitted = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from the previous screen
  const {  firstName,
    lastName,
    email,
    idCardType,
    ssn,
    driverLincenseImgFront,mobileNumber,
    driverLincenseImgBack} = route.params || {};
 
  const handleNext = () => {
    // Pass all data to the next screen
    navigation.navigate('AddProfilePicture', {
      firstName,
      lastName,
      email,
      idCardType,
      ssn, driverLincenseImgFront,
      driverLincenseImgBack,mobileNumber
    });
  };

  return (
    <GradientBackground>
      {/* Header Section */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}

      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon} // Replace with your back icon path
            style={styles.icon}
          />
        </TouchableOpacity >
      <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
             <Text style={styles.helpText}>Help</Text>
             </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Driver’s License Add</Text>

    

      {/* Description */}
      <Text style={styles.description}>
      Driver’s license to verify your identity has been successfully add
      </Text>

      {/* Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.primaryButton} onPress={handleNext}>
          <Text style={styles.primaryButtonText}>Add profile picture</Text>
        </TouchableOpacity>

       
      </View>
    </GradientBackground>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
    backgroundColor: '#F8FBFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal:20
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },
  title: {
   ...FONTS.body2,
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal:20,
    marginTop:100,
    color: '#000',
  },
  illustration: {
    marginTop:50,
    width: '80%',
    height: 100,
    alignSelf: 'center',
  },
  description: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
    marginVertical: 20,
  },
  buttonContainer: {
    marginVertical: 30,
    marginHorizontal:20

  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryButtonText: {
    color: '#FFF',
    ...FONTS.h3

  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor:COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: COLORS.primary,
    ...FONTS.h3
  },
});

export default IdentificationSubmitted;
