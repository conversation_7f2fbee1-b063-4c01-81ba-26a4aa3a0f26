import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { COLORS, FONTS, icons } from '../../../constants';
import Grad<PERSON>Background from '../../../components/shared/GradientBackground';
import { useNavigation, useRoute } from '@react-navigation/native';

const GOOGLE_API_KEY = 'AIzaSyC0VJu9ttMNPOWP-vxTuXtzAaR932hdKUc';

const WhereToDrive = () => {
  const navigation = useNavigation();
  const route = useRoute();

  const { firstName, lastName, email, mobileNumber } = route.params || {};

  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [opreatingCity, setopreatingCity] = useState('');
  const [loading, setLoading] = useState(false);

  const handleInputChange = async (text) => {
    setQuery(text);
    if (text.length > 2) {
      setLoading(true);
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&types=(cities)&key=${GOOGLE_API_KEY}`
        );
        const data = await response.json();
        console.log('Google Places API Response:', data);
        
        if (data.status === 'OK') {
          const predictions = data.predictions.map((prediction) => ({
            id: prediction.place_id,
            description: prediction.description,
          }));
          setSuggestions(predictions);
        } else {
          console.error('Google Places API Error:', data.status);
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Google Places API Error:', error);
      } finally {
        setLoading(false);
      }
    } else {
      setSuggestions([]);
    }
  };

  const handleCitySelect = (city) => {
    setQuery(city.description);
    setopreatingCity(city.description);
    setSuggestions([]);
  };

  const handleNext = () => {
    console.log('firstName:', firstName, 'lastName:', lastName, 'email:', email, 'opreatingCity:', opreatingCity, 'mobileNumber:', mobileNumber);

    navigation.navigate('TermOfService', {
      firstName,
      lastName,
      email,
      opreatingCity,
      mobileNumber,
    });
  };

  return (
    <GradientBackground style={styles.container}>
            {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}


      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
          <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Where do you plan to drive?</Text>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <Image source={icons.Search} style={styles.icon} />
        <TextInput
          style={styles.searchInput}
          placeholderTextColor={COLORS.grey}
          placeholder="Your city"
          value={query}
          onChangeText={handleInputChange}
        />
        {loading && <ActivityIndicator size="small" color={COLORS.primary} />}
      </View>

      {/* Suggestions List */}
      {suggestions.length > 0 && (
        <FlatList
          data={suggestions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.suggestionItem}
              onPress={() => handleCitySelect(item)}
            >
              <Text style={styles.suggestionText}>{item.description}</Text>
            </TouchableOpacity>
          )}
          style={[
            styles.suggestionsList,
            {
              height: suggestions.length === 1 ? 50 : 150,
            },
          ]}
        />
      )}

      {/* Subtitle */}
      <Text style={styles.subtitle}>
        This will be based on the requirement of where you plan to drive
      </Text>

      {/* Next Button */}
      <TouchableOpacity
        onPress={handleNext}
        style={[styles.nextButton, { opacity: opreatingCity ? 1 : 0.5 }]}
        disabled={!opreatingCity}
      >
        <Image source={icons.ArrowIcon} style={styles.icon} />
      </TouchableOpacity>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // alignItems: 'center',
  },
  header: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body2,
    textAlign: 'center',
    marginVertical: 20,
    color: '#000',
    marginBottom: 10,
  },
  searchContainer: {
    marginHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
  },
  searchInput: {
    flex: 1,
    height: 50,
    ...FONTS.h4,
    paddingLeft:10
  },
  suggestionsList: {
    marginHorizontal: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    minHeight:50,
    maxHeight: 300, // Limit the height of the suggestion
  },
  suggestionItem: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionText: {
    ...FONTS.body3,
    color: '#000',
  },
  subtitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#555',
    margin: 25,
  },
  nextButton: {
    marginTop:50,
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  helpText: {
        ...FONTS.body3,
        color: COLORS.black,
      },
});

export default WhereToDrive;
