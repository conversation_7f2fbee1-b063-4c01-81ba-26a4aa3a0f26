import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
  Platform,
} from 'react-native';
import CountryPicker from 'react-native-country-picker-modal';
import { COLORS, FONTS, icons } from '../../../constants'; // Replace with your constants
import GradientBackground from '../../../components/shared/GradientBackground';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import { RegisterNewPassenger } from '../../../../redux/api';
import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import AuthUtils from '../../../utils/AuthUtils';
import { overwriteStore } from '../../../../redux/ActionCreator';
import { BASE_URL } from '../../../../Baseurl';

const MobileNumber = ({ navigation }) => {
  const [mobileNumber, setMobileNumber] = useState('');
  const [countryCode, setCountryCode] = useState('CA'); // Default country code
  const [callingCode, setCallingCode] = useState('1'); // Default calling code
  const [loader, setLoader] = useState(false);
  const fullMobileNumber = `+${callingCode}${mobileNumber}`;
  const dispatch = useDispatch();

  const handleGoogleSignUp = async () => {
    try {
        // Ensure Google Play Services are available
        await GoogleSignin.hasPlayServices();

        // Sign out before signing in to force account selection
        await GoogleSignin.signOut();

        // Initiate sign-in process
        const userInfo = await GoogleSignin.signIn();
        console.log("Google Sign-In Success:", userInfo);

        // Destructure necessary details from userInfo
        const { email, name, photo } = userInfo?.data?.user;// Ensure correct path to user info based on your SDK version

        // Send user details to backend for authentication
        signUpWithGoogle(email, name, photo);
    } catch (error) {
        if (error.code === statusCodes.SIGN_IN_CANCELLED) {
            alert("Google sign-in was cancelled");
        } else if (error.code === statusCodes.IN_PROGRESS) {
            alert("Google sign-in is already in progress");
        } else {
            console.error("Google Sign-In Error:", error);
            alert("An error occurred during Google Sign-In");
        }
    }
};

  

  const signUpWithGoogle = async (email, name, photo) => {
    try {
      setLoader(true); // Show loader
  
      let profileImg = photo;
      const response = await fetch(
        `${BASE_URL}/api/passenger/auth/signupWithGoogle`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({email,name,profileImg}),
        }
      );
  console.log(JSON.stringify({email,name,profileImg}))
  
      const data = await response.json();
      console.log("Google Sign-Up Response:", JSON.stringify(data, null, 2));
      console.log('🔍 Google signup token (data.token):', data?.token);
      console.log('🔍 Google signup message (data.message):', data?.message);
      console.log('🔍 Google signup data:', data?.data);

      dispatch(overwriteStore({ name: 'user', value: data?.data || [] }));
      console.log('saved in store');

      if (response.ok && data?.success) {
        // Save authentication data using AuthUtils
        // Check both data.token and data.message for the token
        const token = data?.token || data?.message;
        console.log('💾 Attempting to save Google signup auth data...');
        const authSaved = await AuthUtils.saveAuthData(token, data?.data);
        console.log('💾 Google signup auth data saved:', authSaved);

        // Also store passenger ID separately if available
        if (data?.data?.passengerId) {
          await AsyncStorage.setItem("passengerId", data.data.passengerId);
        }

        if (authSaved) {
          navigation.replace("SNotification");
        } else {
          alert("Failed to save login data. Please try again.");
        }
      } else {
        alert(data.data || "Failed to sign up with Google");
      }
    } catch (error) {
      console.error("Error signing up with Google:", error);
      alert( "An error occurred during Google Sign-Up");
    } finally {
      setLoader(false); // Hide loader
    }
  };
  
  
  const handleContinue = async () => {
    if (!mobileNumber || mobileNumber.length < 10) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Mobile Number',
        text2: 'Please enter a valid 10-digit mobile number.',
      });
      return;
    }
  
    try {
      setLoader(true);
  
      const fullMobileNumber = `+${callingCode}${mobileNumber}`;
      const response = await RegisterNewPassenger({ mobileNumber: fullMobileNumber });
  
      // Validate response structure
      if (!response || !response.success) {
        throw new Error(response?.data || 'Unable to register driver number');
      }
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: response?.data || 'An unexpected error occurred.',
      });
  
      console.log('Registration Response:', response);
      const otp = response?.message
  
      // Navigate to next screen if successful
      navigation.navigate('RSecretCode', { mobileNumber: fullMobileNumber,otp:otp });
    } catch (error) {
      console.error('Error in registration:', error);
   
  
      // Toast.show({
      //   type: 'error',
      //   text1: 'Error',
      //   text2: error || 'An unexpected error occurred.',
      // });
    } finally {
      setLoader(false);
    }
  };
  return (
    <GradientBackground>
      {/* Loader */}
      <Spinner visible={loader} />
{Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}

      {/* Header */}
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>

      {/* Title */}
      <Text style={styles.title}>Enter your mobile number</Text>
      <Text style={styles.subtitle}>We’ll text a code to verify your mobile number</Text>

      {/* Mobile Number Input */}
      <View style={styles.inputContainer}>
        {/* Country Picker */}
        <CountryPicker
          countryCode={countryCode}
          withFlag
          withFilter
          withCallingCode
          onSelect={(country) => {
            setCountryCode(country.cca2);
            setCallingCode(country.callingCode[0]);
          }}
          containerButtonStyle={styles.countryPicker}
        />
        <Text style={styles.callingCode}>+{callingCode}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your mobile number"
          value={mobileNumber}
          placeholderTextColor={COLORS.grey}
          onChangeText={setMobileNumber}
          keyboardType="phone-pad"
          maxLength={10}
        />
      </View>

      {/* Continue Button */}
      <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
        <Text style={styles.continueButtonText}>Continue →</Text>
      </TouchableOpacity>

      {/* OR Divider */}
      <Text style={styles.orText}>OR</Text>

      {/* Social Signup Buttons */}
      <TouchableOpacity style={styles.socialButton} onPress={handleGoogleSignUp}>
  <Image source={icons.GoogleIcon} style={styles.socialIcon} />
  <Text style={styles.socialButtonText}>Sign up with Google</Text>
</TouchableOpacity>
      

      {/* Footer */}
      <Text style={styles.footerText}>
        Already have an account?{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('WelcomeBack')}>
          Sign in
        </Text>
      </Text>
      <Text style={styles.termsText}>
        By Proceeding, you agree to get calls, SMS messages, including automated calls from{' '}
        <Text style={styles.brandText}>RideFuze</Text> and its affiliated numbers. Text “STOP” to 20013 to opt-out and you consent to its{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('PrivacyPolicyScreen')}>
          Privacy Policy
        </Text>{' '}
        and{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('TermsOfServiceScreen')}>
          Terms of Service
        </Text>.
      </Text>
    </GradientBackground>
  );
};

export default MobileNumber;


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F8FBFF',
  },
  backButton: {
    marginBottom: 20,
  },
  icon: {
    margin:20,
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.h4,
    textAlign: 'center',
    marginBottom: 10,
    color: COLORS.black,
  },
  subtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    marginBottom: 30,
    color: '#555',
  },
  inputContainer: {
    marginHorizontal:20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    marginBottom: 30,
  },
  countryPicker: {
    marginRight: 10,
  },
  callingCode: {
    ...FONTS.body3,
    marginRight: 10,
    color: COLORS.black,
  },
  input: {
    flex: 1,
    height: 50,
    ...FONTS.body3,
    color: COLORS.black,
    backgroundColor: '#fff',
  },
    marginHorizontal:20,
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal:20,

  },
  continueButtonText: {
    color: '#fff',
  ...FONTS.h3
  },
  orText: {
    textAlign: 'center',
    ...FONTS.body3,
    marginVertical: 10,
    color: '#555',
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal:20,

    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingVertical: 13,
    paddingHorizontal: 20,
    marginBottom: 10,
    backgroundColor: '#fff',
  },
  socialIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  socialButtonText: {
    ...FONTS.h4,
    color: COLORS.black,
  },
  footerText: {
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal:20,

    ...FONTS.body3,
    color: '#555',
  },
  linkText: {
    color: COLORS.primary,
    ...FONTS.h4,
    fontSize:12
  },
  termsText: {
    textAlign: 'justify',
    ...FONTS.body4,
    marginHorizontal:22,

    color: '#555',
  },
  brandText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
});
