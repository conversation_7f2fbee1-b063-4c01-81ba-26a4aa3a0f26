import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  PermissionsAndroid,
  Platform,
  ScrollView,
} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import { CompleteNewDriverRegistration } from '../../../../redux/api';
import { launchImageLibrary } from 'react-native-image-picker';
import { BASE_URL } from '../../../../Baseurl';

const CarFormDetails = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [loader, setLoader] = useState(false);
  // const [pricePerKm, setPricePerKm] = useState('');

  // Retrieve data passed from the previous screen
  const allData = route.params || {};

  const granted = PermissionsAndroid.request(
    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
    {
      title: 'Location Permission',
      message: 'We need your location to complete the registration.',
      buttonPositive: 'OK',
    }
  );

  const [form, setForm] = useState({
    registrationNumber: '',
    year: '',
    model: '',
    color: '',
    noOfSeats: '',
    carImg: null,
  });

  const [isFormComplete, setIsFormComplete] = useState(false);
  const [locationGranted, setLocationGranted] = useState(false);
  const [coordinates, setCoordinates] = useState(null);

  useEffect(() => {
    const allFieldsFilled = Object.values(form).every((field) => {
      if (typeof field === 'string') {
        return field.trim() !== '';
      }
      return field !== null && field !== undefined;
    });
    setIsFormComplete(allFieldsFilled);
  }, [form]);

  useEffect(  () => {
  //  granted();
    getLocation();
    requestLocationPermission();
  }, []);

// console.log(allData,'allData');


  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'We need your location to complete the registration.',
            buttonPositive: 'OK',
          }
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setLocationGranted(true);
          getLocation();
        } else {
          setLocationGranted(false);
          Alert.alert('Permission Denied', 'Location permission is required to proceed.');
        }
      } else {
        Geolocation.requestAuthorization('whenInUse').then((status) => {
          if (status === 'granted') {
            setLocationGranted(true);
            getLocation();
          } else {
            setLocationGranted(false);
            Alert.alert('Permission Denied', 'Location permission is required to proceed.');
          }
        });
      }
    } catch (error) {
      console.error('Permission Error:', error);
    }
  };

  const getLocation = () => {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const coords = [position.coords.longitude, position.coords.latitude];
          setCoordinates(coords);
          console.log('Location:', coords);
          resolve(coords);
        },
        (error) => {
          console.error('Location Error:', error);
          Alert.alert('Error', 'Unable to fetch location. Please try again.');
          reject(error);
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
      );
    });
  };

  const handleCarImageUpload = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: 'photo',
        quality: 1,
      });

      if (result.didCancel) {
        console.log('Image picker canceled');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];

        setForm((prevForm) => ({
          ...prevForm,
          carImg: {
            name: selectedImage.fileName || selectedImage.uri.split('/').pop(),
            type: selectedImage.type,
            uri: selectedImage.uri,
          },
        }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'An error occurred while selecting the image. Please try again.');
    }
  };

  const handleChange = (key, value) => {
    setForm((prev) => ({ ...prev, [key]: value }));
  };

  // const handleSubmit = async () => {
  //   if (!isFormComplete) {
  //     Alert.alert('Error', 'Please fill all fields before proceeding.');
  //     return;
  //   }

  //   try {
  //     if (!locationGranted) {
  //       await requestLocationPermission();
  //       if (!locationGranted) {
  //         return;
  //       }
  //     }

  //     const location = await getLocation();
  //     if (!location) {
  //       Alert.alert(
  //         'Error',
  //         'Unable to fetch location. Please try again or ensure location is enabled.'
  //       );
  //       return;
  //     }

  //     const carDetails = { ...form };

  //     const payload = {
  //       ...allData,
  //       pricePerKm:1000,
  //       carDetails,
  //       coordinates, // Use the array format of coordinates
  //     };

  //     setLoader(true);

  //     const response = await CompleteNewDriverRegistration(payload);
  //     console.log('API Response:', response);

  //     if (response.success) {
  //       Toast.show({
  //         type: 'success',
  //         text1: 'Registration Complete',
  //         text2: 'Your details have been successfully submitted.',
  //       });

  //       navigation.navigate('NextScreen', payload);
  //     } else {
  //       Toast.show({
  //         type: 'error',
  //         text1: 'Registration Failed',
  //         text2: response.message || 'Unable to complete registration. Please try again.',
  //       });
  //     }
  //   } catch (error) {
  //     console.error('API Error:', error);
  //     Toast.show({
  //       type: 'error',
  //       text1: 'Error',
  //       text2: 'Something went wrong. Please try again.',
  //     });
  //   } finally {
  //     setLoader(false);
  //   }
  // };

  // const handleSubmit = async () => {
  //   if (!isFormComplete) {
  //     Alert.alert('Error', 'Please fill all fields before proceeding.');
  //     return;
  //   }
  
  //   try {
  //     // Ensure location permission is granted
  //     if (!locationGranted) {
  //       await requestLocationPermission();
  //       if (!locationGranted) {
  //         return; // Stop if permission is still not granted
  //       }
  //     }
  
  //     // Fetch current location
  //     const location = await getLocation();
  //     console.log(location,'dddddd');
  //     // return
  //     // if (!location || !location.latitude || !location.longitude) {
  //     //   Alert.alert(
  //     //     'Error',
  //     //     'Unable to fetch location. Please try again or ensure location is enabled.'
  //     //   );
  //     //   return;
  //     // }
  
  //     // Prepare the FormData object
  //     const formData = new FormData();
  
  //     // Append carDetails fields
  //     formData.append('carDetails[registrationNumber]', form.registrationNumber);
  //     formData.append('carDetails[year]', form.year);
  //     formData.append('carDetails[model]', form.model);
  //     formData.append('carDetails[color]', form.color);
  //     formData.append('carDetails[noOfSeats]', form.noOfSeats);
  //     // formData.append('carDetails[pricePerKm]', form.pricePerKm);
  
  //     // Append car image
  //     if (form.carImg) {
  //       formData.append('carDetails[carImg]', {
  //         uri: form.carImg.uri,
  //         type: form.carImg.type || 'image/jpeg',
  //         name: form.carImg.name || `carImg_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     // Append coordinates as an array [longitude, latitude]
  //     formData.append('coordinates', JSON.stringify([location.longitude, location.latitude]));
  
  //     // Append other details
  //     formData.append('email', allData.email);
  //     formData.append('firstName', allData.firstName);
  //     formData.append('lastName', allData.lastName);
  //     formData.append('mobileNumber', allData.mobileNumber);
  //     formData.append('opreatingCity', allData.opreatingCity);
  //     formData.append('pricePerKm', allData.pricePerKm);
  //     formData.append('ssn', allData.ssn);
  
  //     // Append profile image
  //     if (allData.profileImg) {
  //       formData.append('profileImg', {
  //         uri: allData.profileImg.uri,
  //         type: allData.profileImg.type || 'image/jpeg',
  //         name: allData.profileImg.name || `profileImg_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     // Append driver license images
  //     if (allData.driverLincenseImgFront) {
  //       formData.append('driverLincenseImgFront', {
  //         uri: allData.driverLincenseImgFront.uri,
  //         type: allData.driverLincenseImgFront.type || 'image/jpeg',
  //         name: allData.driverLincenseImgFront.name || `licenseFront_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     if (allData.driverLincenseImgBack) {
  //       formData.append('driverLincenseImgBack', {
  //         uri: allData.driverLincenseImgBack.uri,
  //         type: allData.driverLincenseImgBack.type || 'image/jpeg',
  //         name: allData.driverLincenseImgBack.name || `licenseBack_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     // Show the loader
  //     setLoader(true);
  // console.log(formData,'llllllllllllllll');
  
  //     // Perform the API request
  //     const response = await fetch(
  //       'https://inride-server.onrender.com/api/driver/auth/completeNewDriverRegistration',
  //       {
  //         method: 'POST',
  //         headers: {
  //           'Content-Type': 'multipart/form-data',
  //           'frontend-source': 'app',
  //         },
  //         body: formData,
  //       }
  //     );
  
  //     // Handle response
  //     const result = await response.json();
  
  //     if (response.ok && result.success) {
  //       Toast.show({
  //         type: 'success',
  //         text1: 'Registration Complete',
  //         text2: 'Your details have been successfully submitted.',
  //       });
  
  //       navigation.navigate('TabStack', { ...allData, carDetails: form, coordinates: [location.longitude, location.latitude] });
  //     } else {
  //       console.error('API Error Response:', result);
  //       Toast.show({
  //         type: 'error',
  //         text1: 'Registration Failed',
  //         text2: result.message || 'Unable to complete registration. Please try again.',
  //       });
  //     }
  //   } catch (error) {
  //     console.error('API Error:', error);
  //     Toast.show({
  //       type: 'error',
  //       text1: 'Error',
  //       text2: 'Something went wrong. Please try again.',
  //     });
  //   } finally {
  //     // Hide the loader
  //     setLoader(false);
  //   }
  // };
  

  // const handleSubmit = async () => {
  //   if (!isFormComplete) {
  //     Alert.alert('Error', 'Please fill all fields before proceeding.');
  //     return;
  //   }
  
  //   try {
  //     // Ensure location permission is granted
  //     if (!locationGranted) {
  //       await requestLocationPermission();
  //       if (!locationGranted) {
  //         return; // Stop if permission is still not granted
  //       }
  //     }
  
  //     // Fetch current location
  //     const location = await getLocation();
  //     // if (!location || !location.latitude || !location.longitude) {
  //     //   Alert.alert(
  //     //     'Error',
  //     //     'Unable to fetch location. Please try again or ensure location is enabled.'
  //     //   );
  //     //   return;
  //     // }
  
  //     // Prepare the FormData object
  //     const formData = new FormData();
  
  //     // Append carDetails fields
  //     formData.append('carDetails[registrationNumber]', form.registrationNumber);
  //     formData.append('carDetails[year]', form.year);
  //     formData.append('carDetails[model]', form.model);
  //     formData.append('carDetails[color]', form.color);
  //     formData.append('carDetails[noOfSeats]', form.noOfSeats);
  //     // formData.append('carDetails[pricePerKm]', form.pricePerKm);
  
  //     // Append car image
  //     if (form.carImg) {
  //       formData.append('carDetails[carImg]', {
  //         uri: form.carImg.uri,
  //         type: form.carImg.type || 'image/jpeg',
  //         name: form.carImg.name || `carImg_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     // Append coordinates as an array [longitude, latitude]
  //     formData.append('coordinates', JSON.stringify([location.longitude, location.latitude]));
  
  //     // Append other details
  //     formData.append('email', allData.email);
  //     formData.append('firstName', allData.firstName);
  //     formData.append('lastName', allData.lastName);
  //     formData.append('mobileNumber', allData.mobileNumber);
  //     formData.append('opreatingCity', allData.opreatingCity);
  //     formData.append('pricePerKm', allData.pricePerKm);
  //     formData.append('ssn', allData.ssn);
  
  //     // Append profile image
  //     if (allData.profileImg) {
  //       formData.append('profileImg', {
  //         uri: allData.profileImg.uri,
  //         type: allData.profileImg.type || 'image/jpeg',
  //         name: allData.profileImg.name || `profileImg_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     // Append driver license images
  //     if (allData.driverLincenseImgFront) {
  //       formData.append('driverLincenseImgFront', {
  //         uri: allData.driverLincenseImgFront.uri,
  //         type: allData.driverLincenseImgFront.type || 'image/jpeg',
  //         name: allData.driverLincenseImgFront.name || `licenseFront_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     if (allData.driverLincenseImgBack) {
  //       formData.append('driverLincenseImgBack', {
  //         uri: allData.driverLincenseImgBack.uri,
  //         type: allData.driverLincenseImgBack.type || 'image/jpeg',
  //         name: allData.driverLincenseImgBack.name || `licenseBack_${Date.now()}.jpg`,
  //       });
  //     }
  
  //     // Show the loader
  //     setLoader(true);
  
  //     // Perform the API request
  //     const response = await fetch(
  //       'https://inride-server.onrender.com/api/driver/auth/completeNewDriverRegistration',
  //       {
  //         method: 'POST',
  //         headers: {
  //           // 'Content-Type': 'application/json',
  //           'Content-Type': 'multipart/form-data',
  //           // 'frontend-source': 'app',
  //         },
  //         body: formData,
  //       }
  //     );
  
  //     // Handle response
  //     const result = await response.json();
  
  //     if (response.ok && result.success) {
  //       Toast.show({
  //         type: 'success',
  //         text1: 'Registration Complete',
  //         text2: 'Your details have been successfully submitted.',
  //       });
  
  //       navigation.navigate('NextScreen', { ...allData, carDetails: form, coordinates: [location.longitude, location.latitude] });
  //     } else {
  //       console.error('API Error Response:', result);
  //       Toast.show({
  //         type: 'error',
  //         text1: 'Registration Failed',
  //         text2: result.message || 'Unable to complete registration. Please try again.',
  //       });
  //     }
  //   } catch (error) {
  //     console.error('API Error:', error);
  //     Toast.show({
  //       type: 'error',
  //       text1: 'Error',
  //       text2: 'Something went wrong. Please try again.',
  //     });
  //   } finally {
  //     // Hide the loader
  //     setLoader(false);
  //   }
  // };
  
  // const logFormData = (formData) => {
  //   console.log('--- FormData Content ---');
  //   console.log('--- FormData Content ---');
  //   for (const [key, value] of formData._parts) {
  //     console.log(`${key}:`, value);
  //   }
  //   console.log('--- End of FormData ---');
  // };
  
  // Inside handleSubmit
  // const handleSubmit = async () => {
  //   if (!isFormComplete) {
  //     Alert.alert('Error', 'Please fill all fields before proceeding.');
  //     return;
  //   }
  
  //   try {
  //     if (!locationGranted) {
  //       await requestLocationPermission();
  //       if (!locationGranted) {
  //         return;
  //       }
  //     }
  
  //     const location = await getLocation();
  //     if (!location) {
  //       Alert.alert(
  //         'Error',
  //         'Unable to fetch location. Please try again or ensure location is enabled.'
  //       );
  //       return;
  //     }
  
  //     setLoader(true);
  
  //     const formData = new FormData();
  
  //     // Add non-file data to FormData (res.body)
  //     formData.append('firstName', allData.firstName);
  //     formData.append('lastName', allData.lastName);
  //     formData.append('email', allData.email);
  //     formData.append('mobileNumber', allData.mobileNumber);
  //     formData.append('opreatingCity', allData.opreatingCity);
  //     formData.append('ssn', allData.ssn);
  //     formData.append('coordinates', JSON.stringify([location.longitude, location.latitude]));
  
  //     // Add car details to FormData (res.body)
  //     formData.append('carDetails[color]', form.color);
  //     formData.append('carDetails[model]', form.model);
  //     formData.append('carDetails[registrationNumber]', form.registrationNumber);
  //     formData.append('carDetails[noOfSeats]', form.noOfSeats);
  //     formData.append('carDetails[year]', form.year);
  
  //     // Add file data to FormData (res.file)
  //     if (form.carImg) {
  //       formData.append('carImg', {
  //         uri: form.carImg.uri,
  //         type: form.carImg.type,
  //         name: form.carImg.name,
  //       });
  //     }
  
  //     if (allData.profileImg) {
  //       formData.append('profileImg', {
  //         uri: allData.profileImg.uri,
  //         type: allData.profileImg.type,
  //         name: allData.profileImg.name,
  //       });
  //     }
  
  //     if (allData.driverLincenseImgFront) {
  //       formData.append('driverLincenseImgFront', {
  //         uri: allData.driverLincenseImgFront.uri,
  //         type: allData.driverLincenseImgFront.type,
  //         name: allData.driverLincenseImgFront.name,
  //       });
  //     }
  
  //     if (allData.driverLincenseImgBack) {
  //       formData.append('driverLincenseImgBack', {
  //         uri: allData.driverLincenseImgBack.uri,
  //         type: allData.driverLincenseImgBack.type,
  //         name: allData.driverLincenseImgBack.name,
  //       });
  //     }
  
  //     // Log the FormData content
  //     logFormData(formData);
  
  //     // Make the API request
  //     const response = await fetch('https://inride-server.onrender.com/api/passenger/auth/registerUser', {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'multipart/form-data',
  //       },
  //       body: formData,
  //     });
  
  //     const result = await response.json();
  //     console.log('API Response:', result);
  
  //     if (response.ok) {
  //       Toast.show({
  //         type: 'success',
  //         text1: 'Registration Complete',
  //         text2: 'Your details have been successfully submitted.',
  //       });
  
  //       navigation.navigate('NextScreen', { ...allData, coordinates: [location.longitude, location.latitude] });
  //     } else {
  //       Toast.show({
  //         type: 'error',
  //         text1: 'Registration Failed',
  //         text2: result.message || 'Unable to complete registration. Please try again.',
  //       });
  //     }
  //   } catch (error) {
  //     console.error('API Error:', error);
  //     Toast.show({
  //       type: 'error',
  //       text1: 'Error',
  //       text2: 'Something went wrong. Please try again.',
  //     });
  //   } finally {
  //     setLoader(false);
  //   }
  // };
  
    const handleSubmit = async () => {
    if (!isFormComplete) {
      Alert.alert('Error', 'Please fill all fields before proceeding.');
      return;
    }
  
    try {
      if (!locationGranted) {
        await requestLocationPermission();
        if (!locationGranted) {
          return;
        }
      }
  
      const location = await getLocation();
      if (!location) {
        Alert.alert(
          'Error',
          'Unable to fetch location. Please try again or ensure location is enabled.'
        );
        return;
      }
  
      setLoader(true);
  
      const form= new FormData();
  
   console.log(allData,'formmmmm');
   
      form.append('mobileNumber', allData.mobileNumber);
      form.append('firstName', allData.firstName);
      form.append('lastName', allData.lastName);
      form.append('email', allData.email);
      form.append('opreatingCity', allData.opreatingCity);
      form.append('ssn', allData.ssn);
      // form.append('coordinates', [{longitude:coordinates[0], latitude:coordinates[1]}]);
      // form.append('coordinates', ['longitude','latitude']),
      form.append('idCardType', 'driverLicense');
      form.append('idCardImgFront', {
        uri: allData.driverLincenseImgFront.uri,
        type: allData.driverLincenseImgFront.type,
        name: allData.driverLincenseImgFront.uri.split('/').pop(),
      });
      form.append('idCardImgBack', {
        uri: allData.driverLincenseImgBack.uri,
        type: allData.driverLincenseImgBack.type || 'image/jpeg',
        name: allData.driverLincenseImgBack.uri.split('/').pop(),
      });
      form.append('profileImg', {
        uri: allData.profileImg.uri,
        type: allData.profileImg.type || 'image/jpeg',
        name: allData.profileImg.uri.split('/').pop(),
      });

   console.log(form,'form');

  
      // Log the FormData content
     
  
      // Make the API request
      // const response = await fetch('https://inride-server.onrender.com/api/driver/auth/completeDriverRegistration', {
        const response = await fetch(`${BASE_URL}/api/passenger/auth/registerUser`, {
        
        method: 'POST',
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        body: form,
      });
      console.log('API Response:', response);
  
      const result = await response.json();
      // console.log('API Response:', result);
  
      if (response.ok) {
        Toast.show({
          type: 'success',
          text1: 'Registration Complete',
          text2: 'Your details have been successfully submitted.',
        });
  
      } else {
        console.log(result.data,'result.data');
        
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2: result.data || 'Unable to complete registration. Please try again.',
        });
      }
    } catch (error) {
      console.error('API Error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Something went wrong. Please try again.',
      });
    } finally {
      setLoader(false);
    }
  };
  return (
    <GradientBackground>
    {/* Header Section */}
    <Spinner visible={loader} />
    <ScrollView>
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>
   <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
          <Text style={styles.helpText}>Help</Text>
          </TouchableOpacity>
    </View>
  
    {/* Title */}
    <Text style={styles.title}>Add car details</Text>
  
    {/* Form Fields */}
    <View style={styles.formContainer}>
      <Text style={styles.label}>Registration Number</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Registration Number"
        placeholderTextColor={COLORS.grey}
        value={form.registrationNumber}
        onChangeText={(text) => handleChange('registrationNumber', text)}
      />
  
      <Text style={styles.label}>Year</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter year"
        value={form.year}
        placeholderTextColor={COLORS.grey}
        onChangeText={(text) => handleChange('year', text)}
        keyboardType="numeric"
      />
  
      <Text style={styles.label}>Model</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter model"
        placeholderTextColor={COLORS.grey}
        value={form.model}
        onChangeText={(text) => handleChange('model', text)}
      />
  
      <Text style={styles.label}>Color</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter color"
        placeholderTextColor={COLORS.grey}
        value={form.color}
        onChangeText={(text) => handleChange('color', text)}
      />

{/* <Text style={styles.label}>Price Per Km</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter price per km"
        value={pricePerKm}
        onChangeText={(text) => setPricePerKm('pricePerKm', text)}
      /> */}


  
      <Text style={styles.label}>Number of Seats</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Number of Seats"
        placeholderTextColor={COLORS.grey}
        value={form.noOfSeats}
        onChangeText={(text) => handleChange('noOfSeats', text)}
        keyboardType="numeric"
      />
  
{/* Car Image Upload */}
<Text style={styles.label}>Car Image</Text>
<TouchableOpacity style={styles.uploadButton} onPress={handleCarImageUpload}>
  {form.carImg && form.carImg.uri ? (
    <Image
      source={{ uri: form.carImg.uri }}
      style={styles.carImagePreview} // Add styling for the image preview
      resizeMode="contain"
    />
  ) : (
    <Text style={styles.uploadButtonText}>Upload Car Image</Text>
  )}
</TouchableOpacity>


    </View>
  
    {/* Submit Button */}
    <TouchableOpacity
      style={[
        styles.submitButton,
        isFormComplete ? styles.submitButtonEnabled : styles.submitButtonDisabled,
      ]}
      onPress={handleSubmit}
      disabled={!isFormComplete} // Disable the button when the form is incomplete
    >
      <Text style={styles.submitButtonText}>Submit</Text>
    </TouchableOpacity>
    </ScrollView>
  </GradientBackground>
  
  );
};
const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  title: {
  ...FONTS.h2,
    textAlign: 'center',
    marginVertical: 16,
    color: COLORS.black,
  },
  formContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  label: {
     ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
  },
  input: {
    height: 48,
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.black,
    backgroundColor: COLORS.white,
  },
  submitButton: {
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonEnabled: {
    backgroundColor: COLORS.primary,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.lightGray,
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: FONTS.bold,
    color: COLORS.white,
  },
  carImagePreview: {
    width: 150, // Adjust width as needed
    height: 150, // Adjust height as needed
    borderRadius: 10,
    marginVertical: 10,
  },
  uploadButton: {
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 10,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  uploadButtonText: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
  
});

export default CarFormDetails;
