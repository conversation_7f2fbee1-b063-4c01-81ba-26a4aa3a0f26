import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Camera, useCameraDevices } from 'react-native-vision-camera';

const CCamera = () => {
  const devices = useCameraDevices();
  const device = devices.back; // Using the back camera
  const [hasPermission, setHasPermission] = React.useState(false);

  // Request camera permissions on mount
  useEffect(() => {
    (async () => {
      const status = await Camera.requestCameraPermission();
      setHasPermission(status === 'authorized');
    })();
  }, []);

  // If permission is not granted, show a permissions page
  if (!hasPermission) return <PermissionsPage />;

  // If no camera device is available, show an error
  if (device == null) return <NoCameraDeviceError />;

  return (
    <Camera
      style={StyleSheet.absoluteFill} // Camera takes up the full screen
      device={device}
      isActive={true} // Keep the camera active
      onInitialized={() => console.log('Camera initialized')}
      onError={(error) => console.error('Camera error:', error)}
    />
  );
};

// Permissions Page Component
const PermissionsPage = () => (
  <View style={styles.centered}>
    <Text style={styles.text}>Camera permission is required to continue.</Text>
    <TouchableOpacity
      style={styles.button}
      onPress={async () => {
        const status = await Camera.requestCameraPermission();
        if (status === 'authorized') {
          Alert.alert('Permission Granted', 'You can now use the camera.');
        } else {
          Alert.alert('Permission Denied', 'Camera access is required.');
        }
      }}
    >
      <Text style={styles.buttonText}>Grant Permission</Text>
    </TouchableOpacity>
  </View>
);

// No Camera Device Error Component
const NoCameraDeviceError = () => (
  <View style={styles.centered}>
    <Text style={styles.text}>No camera device found.</Text>
  </View>
);

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FBFF',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 10,
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CCamera;
