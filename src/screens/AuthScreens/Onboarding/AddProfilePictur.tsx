import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import * as ImagePicker from 'react-native-image-picker'; // Ensure this library is installed
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import { BASE_URL } from '../../../../Baseurl';



const AddProfilePicture = () => {
  const navigation = useNavigation();
  const route = useRoute();

 

  const [profileImg, setProfileImg] = useState(null);
  const [loader, setLoader] = useState(false);
  const allData = route.params || {};
  console.log(allData, 'allData');
  

  const handleImagePick = async () => {
    try {
      const result = await ImagePicker.launchImageLibrary({
        mediaType: 'photo',
        includeBase64: false,
        selectionLimit: 1,
      });

      if (result.didCancel) {
        console.log('User cancelled image picker');
        return;
      }

      if (result.errorCode) {
        console.log('Error Code:', result.errorCode);
        Alert.alert('Error', result.errorMessage || 'Unable to pick an image.');
        return;
      }

      if (result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const formattedImage = {
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          name: asset.fileName || `profile_${Date.now()}.jpg`,
        };

        setProfileImg(formattedImage);
      } else {
        Alert.alert('Error', 'No image selected.');
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Error', 'Unable to pick an image. Please try again.');
    }
  };

 
  const handleSubmit = async () => {

    if (!profileImg) {
      Alert.alert('Incomplete', 'Please upload a profile picture.');
      return;
    }

 try {
       
      
       setLoader(true);
   
       const form= new FormData();

    form.append('mobileNumber', allData.mobileNumber);
    form.append('firstName', allData.firstName);
    form.append('lastName', allData.lastName);
    form.append('email', allData.email);
    form.append('idCardType', 'driverLicense');
    form.append('ssn', allData.ssn);
    // form.append('idCardType', 'driverLicense');

    // form.append('idCardType', allData.idCardType);
    form.append('idCardImgFront', {
      uri: allData.driverLincenseImgFront.uri,
      type: allData.driverLincenseImgFront.type,
      name: allData.driverLincenseImgFront.uri.split('/').pop(),
    });
    form.append('idCardImgBack', {
      uri: allData.driverLincenseImgBack.uri,
      type: allData.driverLincenseImgBack.type || 'image/jpeg',
      name: allData.driverLincenseImgBack.uri.split('/').pop(),
    });
    form.append('profileImg', {
      uri: profileImg.uri,
      type: profileImg.type || 'image/jpeg',
      name: profileImg.uri.split('/').pop(),
    });

    console.log(form, 'form');


    // Log the FormData content


    // Make the API request
    const response = await fetch(`${BASE_URL}/api/passenger/auth/registerUser`, {

      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      body: form,
    });
    // console.log('API Response:', response.json());

    const result = await response.json();
    console.log('API Response:', result);

    if (response.ok) {
      Toast.show({
        type: 'success',
        text1: 'Registration Complete',
        text2: 'Your details have been successfully submitted.',
      });
      navigation.navigate('WelcomeBack')

    } else {
      console.log(result.data, 'result.data');

      Toast.show({
        type: 'error',
        text1: 'Registration Failed',
        text2: result.data || 'Unable to complete registration. Please try again.',
      });
    }
  } catch (error) {
    console.error('API Error:', error);
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: 'Something went wrong. Please try again.',
    });
  } finally {
    setLoader(false);
  }}

return (
  <GradientBackground>
      {/* Loader */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}


      <Spinner visible={loader} />
      
    {/* Header Section */}
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Image
          source={icons.BackIcon} // Replace with your back icon path
          style={styles.icon}
        />
      </TouchableOpacity>
    <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
           <Text style={styles.helpText}>Help</Text>
           </TouchableOpacity>
    </View>
    <View style={styles.container}>
      <Text style={styles.title}>Add Profile Picture</Text>
      <TouchableOpacity onPress={handleImagePick} style={styles.imagePicker}>
        {profileImg ? (
          <Image
            source={{ uri: profileImg.uri }}
            style={styles.profileImage}
          />
        ) : (
          <>
            <Image
              source={images.uploadimage}
              style={styles.profileImagee}
            />
            <Text style={styles.imagePickerText}>Upload Profile Picture</Text>
          </>
        )}
      </TouchableOpacity>

      <TouchableOpacity onPress={handleSubmit} style={styles.nextButton}>
        <Text style={styles.nextButtonText}>Next</Text>
      </TouchableOpacity>
    </View>
  </GradientBackground>
);

};
const styles = StyleSheet.create({
  container: {
    // flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
    // backgroundColor: '#F8FBFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 100,
    marginHorizontal: 20,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },

  title: {
    marginBottom: 50,

    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imagePicker: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.light_grey,
    borderRadius: 100,
    width: 150,
    height: 150,
    alignSelf: 'center',
    marginBottom: 30,
  },
  imagePickerText: {
    marginTop: 30,
    textAlign: 'center',
    color: COLORS.grey,
  },
  profileImage: {
    width: 150,
    height: 150,
    borderRadius: 100,
  },
  profileImagee: {
    width: 150,
    height: 150,
    borderRadius: 100,
  },
  nextButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  nextButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
});
 
export default AddProfilePicture;
