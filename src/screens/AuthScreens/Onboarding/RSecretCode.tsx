import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  Alert,
} from 'react-native';
import { <PERSON>Field, Cursor, useBlurOnFulfill, useClearByFocusCell } from 'react-native-confirmation-code-field';
import { COLORS, FONTS, icons } from '../../../constants'; // Replace with your constants
import GradientBackground from '../../../components/shared/GradientBackground';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import { ResendOtp, VerifyOtp } from '../../../../redux/api';
import { api } from '../../../../redux/shared';

const CELL_COUNT = 4;

const RSecretCode = ({ navigation, route }) => {
  const [value, setValue] = useState('');
  const { mobileNumber } = route.params ; // Get the mobile number from route params
  const { otp } = route.params ; 
  const [loader, setLoader] = useState(false);
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  console.log('mobileNumber:', mobileNumber);
  
  const isOtpComplete = value.length === CELL_COUNT;

  const handleNext = async () => {
    if (!isOtpComplete) return;

    try {
      setLoader(true);
      const response = await VerifyOtp({otp: value });
      console.log('OTP Verification Response:', response);

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'OTP Verified',
          text2: response?.data
        });
        navigation.navigate('AddName',{mobileNumber}); // Replace with your navigation screen
     
      } else {
        Toast.show({
          type: 'error',
          text1: 'Verification Failed',
          text2: response.data
        });
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      // Toast.show({
      //   type: 'error',
      //   text1: 'Error',
      //   text2: error?.message || 'An unexpected error occurred.',
      // });
    } finally {
      setLoader(false);
    }
  };

  const handleResendOtp = async () => {
    try {
      setLoader(true);
      const response = await ResendOtp({mobileNumber:mobileNumber} );
      console.log('Resend OTP Response:', response);

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'OTP Sent',
          text2: response?.data,
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Resend Failed',
          text2: response.data
        });
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      // Toast.show({
      //   type: 'error',
      //   text1: 'Error',
      //   text2: error?.data || 'An unexpected error occurred.',
      // });
    } finally {
      setLoader(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Loader */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}


      <Spinner visible={loader} />

      {/* Header */}
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>

      {/* Title */}
<Text style={styles.title}>Text Alert! Find Your Secret Code inside</Text>
<Text style={styles.subtitle}>
  Verification Otp sent to: 
  <Text style={styles.phoneNumber}>{` ${mobileNumber}`}</Text>
  <Text style={styles.phoneNumber}>{otp}</Text>
  . Code is valid for 10min
</Text>

      {/* OTP Input */}
      <CodeField
        secureTextEntry
        ref={ref}
        {...props}
        value={value}
        onChangeText={setValue}
        cellCount={CELL_COUNT}
        rootStyle={styles.codeFieldRoot}
        keyboardType="number-pad"
        textContentType="oneTimeCode"
        autoComplete={Platform.select({ android: 'sms-otp', default: 'one-time-code' })}
        renderCell={({ index, symbol, isFocused }) => (
          <Text
            key={index}
            style={[styles.cell, isFocused && styles.focusCell]}
            onLayout={getCellOnLayoutHandler(index)}
          >
            {symbol || (isFocused ? <Cursor /> : null)}
          </Text>
        )}
      />

     {/* Resend Code */}
<Text style={styles.resendText}>
  Didn’t receive code?{' '}
  <Text style={styles.resendLink} onPress={handleResendOtp}>
    Resend
  </Text>
</Text>

      {/* Next Button */}
      <TouchableOpacity
        style={[styles.nextButton, isOtpComplete ? styles.activeButton : styles.inactiveButton]}
        disabled={!isOtpComplete}
        onPress={handleNext}
      >
        <Image source={icons.ArrowIcon} style={styles.nextIcon} /> 
      </TouchableOpacity>
    </View>
  );
};

export default RSecretCode;

const styles = StyleSheet.create({
  root: {flex: 1, padding: 40},
    codeFieldRoot: {marginTop: 20,borderRadius: 10,width:'70%',alignSelf: 'center'},
  cell: {
    width: 55,
    height: 55,
    lineHeight: 60,
    fontSize: 24,
    borderWidth: 0.3,
    // borderColor: '#00000030',
    // elevation: 1,
    borderRadius: 5,
    textAlign: 'center',
  },
  focusCell: {
    borderColor: '#000',
  },
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#E5F3FF', // Light blue background
  },
  backButton: {
    marginBottom: 20,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.h2,
    textAlign: 'center',
    marginVertical: 20,
    color: COLORS.black,
  },
  subtitle: {
    ...FONTS.body3,
    textAlign: 'center',
    marginBottom: 30,
    color: '#555',
  },
  phoneNumber: {
    fontWeight: '600',
    color: COLORS.black,
  },
 
  resendText: {
    textAlign: 'center',
    ...FONTS.body3,
    color: '#555',
    marginTop: 20,
  },
  resendLink: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  nextButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  activeButton: {
    backgroundColor: COLORS.primary,
  },
  inactiveButton: {
    backgroundColor: '#ddd',
  },
  nextIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: '#fff',
  },
});
 