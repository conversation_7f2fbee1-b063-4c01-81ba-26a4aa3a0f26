import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const AddSocialSecurity = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from previous screen
  const { firstName, lastName, email, opreatingCity,mobileNumber } = route.params || {};

  const handleNext = () => {
    // Pass all data to the next screen
    navigation.navigate('AddSocialSecurityNumber', {
      firstName,
      lastName,
      email,
      opreatingCity,mobileNumber
    });
  };

  console.log('firstName:', firstName,'lastName:', lastName, 'email:', email, 'opreatingCity:', opreatingCity, 'mobileNumber:', mobileNumber);
  

  return (
    <GradientBackground>
            {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}


      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon} // Replace with your back icon path
            style={styles.icon}
          />
        </TouchableOpacity>
       <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
              <Text style={styles.helpText}>Help</Text>
              </TouchableOpacity>
      </View>

<View style={{marginHorizontal:20,backgroundColor:'#F8FBFF',marginTop:80,marginBottom:20,padding:0,borderRadius:10}}>
      {/* Title Section */}
      <Text style={styles.title}>Verify your identity to choose a driver</Text>

  {/* Description */}
  <Text style={styles.description}>
      Protects your accounts and helps with compliance. it also boosts visibility to buyers drivers great opportunity!
      </Text>
      {/* Illustration */}
      <Image
        source={images.id} // Replace with your placeholder image
        style={styles.illustration}
        resizeMode="contain"
      />

    

      {/* Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={handleNext} // Navigate to the next screen with all data
        >
          <Text style={styles.primaryButtonText}>Start Verification</Text>
        </TouchableOpacity>

        
      </View>
      </View>
    </GradientBackground>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
    backgroundColor: '#F8FBFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal:20,
    marginBottom:50
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },
  title: {
   ...FONTS.h3,
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal:60,
    marginTop:30,
    color: '#000',
  },
  illustration: {
    marginVertical:20,
    width: '80%',
    height: 100,
    alignSelf: 'center',
  },
  description: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
    fontSize: 13,
    lineHeight: 14,
    marginVertical: 0,
    marginHorizontal:40
  },
  buttonContainer: {
    marginVertical: 10,
    marginHorizontal:20

  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryButtonText: {
    color: '#FFF',
    ...FONTS.h3,
    fontSize: 15,

  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor:COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: COLORS.primary,
    ...FONTS.h3
  },
});

export default AddSocialSecurity;
