import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Grad<PERSON>Background from '../../../components/shared/GradientBackground';
import { icons, images,FONTS,COLORS } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const DriverLicense = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const payload = route.params || {};
  const [idCardType, setIdCardType] = useState('');
console.log(idCardType);

  const handleNext = () => {
    if (!idCardType) {
      alert('Please select an ID type');
      return;
    }
    navigation.navigate('DriverLicenseImage', {
      ...payload,
      idCardType,
    });
  };

  return (
    <GradientBackground>
      <View style={{marginTop: 60}} />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon}
            style={styles.icon}
          />
        </TouchableOpacity>
       <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
              <Text style={styles.helpText}>Help</Text>
              </TouchableOpacity>
      </View>

      <Text style={styles.title}>Select ID type</Text>
      <Image
        source={images.id}
        style={styles.illustration}
        resizeMode="contain"
      />

      <Text style={styles.description}>
        Protects your accounts and helps with compliance. It also boosts visibility to buyers and drivers. Great opportunity!
      </Text>

      <View style={{ marginHorizontal: 30 }}>
        {[
          // { display: 'Driver License', value: 'driverLicense' },
          { display: 'International Passport', value: 'internationalPassport' },
          { display: "State Id", value: 'stateId' }
        ].map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.optionButton,
              idCardType === option.value && styles.selectedOption
            ]}
            onPress={() => setIdCardType(option.value)}
          >
            <Text 
              style={[
                styles.optionText,
                idCardType === option.value && styles.selectedOptionText
              ]}
            >
              {option.display}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity style={styles.primaryButton} onPress={handleNext}>
        <Text style={styles.primaryButtonText}>Next</Text>
      </TouchableOpacity>
    </GradientBackground>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 30,
    justifyContent: 'space-between',
    backgroundColor: '#F8FBFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal: 20,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },
  title: {
    ...FONTS.h2,
    textAlign: 'center',
    marginVertical: 20,
    color: '#000',
  },
  illustration: {
    marginTop: 20,
    width: 100,
    height: 100,
    alignSelf: 'center',
  },
  description: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  optionButton: {
    width: '100%',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderWidth: 1,
    backgroundColor: COLORS.primaryLight,
    borderColor: COLORS.grey,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginHorizontal:30
  },
  selectedOption: {
    borderColor: COLORS.grey,
    color: COLORS.white,
    backgroundColor: COLORS.primary,
  },
  optionText: {
    ...FONTS.body3,
    color: '#343A40',
  },
  SelectedOptionText: {
    color: COLORS.white,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal: 30,
  },
  primaryButtonText: {
    color: '#FFF',
    ...FONTS.h3,
  },
});

export default DriverLicense;
