import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  Alert,
} from 'react-native';
import { CodeField, Cursor, useBlurOnFulfill, useClearByFocusCell } from 'react-native-confirmation-code-field';
import { COLORS, FONTS, icons } from '../../../constants'; // Replace with your constants
import GradientBackground from '../../../components/shared/GradientBackground';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AuthUtils from '../../../utils/AuthUtils';
import { ResendOtp, VerifyLoginOtp } from '../../../../redux/api';
import { useDispatch } from 'react-redux';
import { overwriteStore } from '../../../../redux/ActionCreator';

const CELL_COUNT = 4;

const SecretCode = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const { mobileNumber,otp } = route.params ; // Get the mobile number from route params
  const [value, setValue] = useState('');
  const [loader, setLoader] = useState(false);
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });
console.log('otp:', otp);

  console.log('mobileNumber:', mobileNumber);
  
  const isOtpComplete = value.length === CELL_COUNT;

  const handleNext = async () => {
    if (!isOtpComplete) return;

    try {
      setLoader(true);
      // navigation.navigate('SNotification'); // Replace with your navigation screen
 
      const response = await VerifyLoginOtp({ mobileNumber, otp: value });

      console.log('🔍 OTP Verification Response:', JSON.stringify(response, null, 2));
      console.log('🔍 Response token (response.token):', response?.token);
      console.log('🔍 Response message (response.message):', response?.message);
      console.log('🔍 Response data:', response?.data);

      // Detailed token analysis
      console.log('🔍 TOKEN ANALYSIS:');
      console.log('- Token type:', typeof response?.message);
      console.log('- Token length:', response?.message?.length);
      console.log('- Token value:', response?.message);
      console.log('- Is token truthy:', !!response?.message);
      console.log('- Token stringified:', JSON.stringify(response?.message));

      dispatch(overwriteStore({ name: 'user', value: response?.data || [] }));

      if (response.success) {
        // Save authentication data using AuthUtils
        // The token is in response.message, not response.token
        console.log('💾 Attempting to save auth data...');
        const authSaved = await AuthUtils.saveAuthData(response?.message, response?.data);
        console.log('💾 Auth data saved:', authSaved);

        if (authSaved) {
          Toast.show({
            type: 'success',
            text1: 'OTP Verified',
            text2: 'You have been successfully logged in.',
          });
          navigation.navigate('SNotification',{mobileNumber}); // Replace with your navigation screen
        } else {
          Toast.show({
            type: 'error',
            text1: 'Login Error',
            text2: 'Failed to save login data. Please try again.',
          });
        }
     
      } else {
        Toast.show({
          type: 'error',
          text1: 'Verification Failed',
          text2: response.data || 'Invalid OTP. Please try again.',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: data?.data || 'An unexpected error occurred.',
      });
    } finally {
      setLoader(false);
    }
  };

  const handleResendOtp = async () => {
    try {
      setLoader(true);
      const response = await ResendOtp({ mobileNumber: mobileNumber} );
      console.log('Resend OTP Response:', response);

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'OTP Sent',
          text2: 'A new OTP has been sent to your mobile number.',
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Resend Failed',
          text2: response.data || 'Unable to resend OTP. Please try again.',
        });
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error?.data || 'An unexpected error occurred.',
      });
    } finally {
      setLoader(false);
    }
  };

  return (
    <View style={styles.container}>
         {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
         {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}
      {/* Loader */}
      <View style={{paddingTop:50}}/>
      
      <Spinner visible={loader} />

      {/* Header */}
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>

      {/* Title */}
      <Text style={styles.title}>Text Alert! Find Your Secret Code inside</Text>
      <Text style={styles.subtitle}>
        Enter the code sent to you at <Text style={styles.phoneNumber}>{mobileNumber} {otp}</Text>
      </Text>

      {/* OTP Input */}
      <CodeField
        secureTextEntry
        ref={ref}
        {...props}
        value={value}
        onChangeText={setValue}
        cellCount={CELL_COUNT}
        rootStyle={styles.codeFieldRoot}
        keyboardType="number-pad"
        textContentType="oneTimeCode"
        autoComplete={Platform.select({ android: 'sms-otp', default: 'one-time-code' })}
        renderCell={({ index, symbol, isFocused }) => (
          <Text
            key={index}
            style={[styles.cell, isFocused && styles.focusCell]}
            onLayout={getCellOnLayoutHandler(index)}
          >
            {symbol || (isFocused ? <Cursor /> : null)}
          </Text>
        )}
      />

      {/* Resend Code */}
      <Text style={styles.resendText}>
        Didn’t receive code?{' '}
        <Text style={styles.resendLink} onPress={handleResendOtp}>
          Resend
        </Text>
      </Text>

     

      {/* Next Button */}
      <TouchableOpacity
        style={[styles.nextButton, isOtpComplete ? styles.activeButton : styles.inactiveButton]}
        disabled={!isOtpComplete}
        onPress={handleNext}
      >
        <Image source={icons.ArrowIcon} style={styles.nextIcon} /> {/* Replace with your icon */}
      </TouchableOpacity>
    </View>
  );
};

export default SecretCode;

const styles = StyleSheet.create({
  root: {flex: 1, padding: 40},
    codeFieldRoot: {marginTop: 20,borderRadius: 10,width:'70%',alignSelf: 'center'},
  cell: {
    width: 55,
    height: 55,
    lineHeight: 60,
    fontSize: 24,
    borderWidth: 0.3,
    // borderColor: '#00000030',
    // elevation: 1,
    borderRadius: 5,
    textAlign: 'center',
  },
  focusCell: {
    borderColor: '#000',
  },
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#E5F3FF', // Light blue background
  },
  backButton: {
    marginBottom: 20,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.h2,
    textAlign: 'center',
    marginVertical: 20,
    color: COLORS.black,
  },
  subtitle: {
    ...FONTS.body3,
    textAlign: 'center',
    marginBottom: 30,
    color: '#555',
  },
  phoneNumber: {
    fontWeight: '600',
    color: COLORS.black,
  },
 
  resendText: {
    textAlign: 'center',
    ...FONTS.body3,
    color: '#555',
    marginTop: 20,
  },
  resendLink: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  nextButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  activeButton: {
    backgroundColor: COLORS.primary,
  },
  inactiveButton: {
    backgroundColor: '#ddd',
  },
  nextIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: '#fff',
  },
});
 