import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import CountryPicker from 'react-native-country-picker-modal';
import { COLORS, FONTS, icons, images } from '../../../constants'; // Replace with your constants
import GradientBackground from '../../../components/shared/GradientBackground';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { overwriteStore } from '../../../../redux/ActionCreator';
import { useDispatch } from 'react-redux';
import { BASE_URL } from '../../../../Baseurl';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AuthUtils from '../../../utils/AuthUtils';

const WelcomeBack = ({ navigation }) => {
  const [mobileNumber, setMobileNumber] = useState('9059309836');
  const [countryCode, setCountryCode] = useState('CA'); // Default country code
  const [callingCode, setCallingCode] = useState('1'); // Default calling code
  const [loader, setLoader] = useState(false);
  const dispatch = useDispatch();

  const handleContinue = async () => {
    if (!mobileNumber || mobileNumber.length < 10) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Mobile Number',
        text2: 'Please enter a valid 10-digit mobile number.',
      });
      return;
    }
  
    try {
      setLoader(true);
      
      const fullMobileNumber = `+${callingCode}${mobileNumber}`;
      const response = await fetch(`${BASE_URL}/api/passenger/auth/signin`, {
        method: 'POST',
       credentials:'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mobileNumber: fullMobileNumber })
      });
      // console.log('Sign in Response:', body);
      
      const data = await response.json();
      console.log('Sign in Response:', JSON.stringify(data,null,2));
      if(response){
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: data?.data,
        });
      }

      if(data?.data == "Unverified account"){
        Alert.alert(
          'Unverified account',
          'You have not verified your account. Do you want to verify it now?',
          [
            {
              text: 'Cancel',
              onPress: () => console.log('Cancel Pressed'),
              style: 'cancel'
            },
            { text: 'Verify', onPress: () => navigation.navigate('MobileNumber', { mobileNumber: fullMobileNumber }) }
          ]
        );
        return;
      }

      if(data.data == "Mobile number does not exist"){
        Alert.alert(
          'Mobile number does not exist',
          'You have not registered with this mobile number. Do you want to register now?',
          [
            {
              text: 'Cancel',
              onPress: () => console.log('Cancel Pressed'),
              style: 'cancel'
            },
            { text: 'Register', onPress: () => navigation.navigate('MobileNumber', { mobileNumber: fullMobileNumber }) }
          ]
        );
        return;
      }
       
      if (!response.ok || !data.success) {
        throw new Error(data?.message || 'Something went wrong');
      }
      const otp = data.message;
      navigation.navigate('SecretCode', { mobileNumber: fullMobileNumber, otp:otp });
    } catch (error) {
      console.error('Error in registration:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error?.data || 'An unexpected error occurred.',
      });
    } finally {
      setLoader(false);
    }
  };
  

  const handleGoogleSignIn = async () => {
    try {
      console.log("Google Sign-In initiated");
      
      await GoogleSignin.hasPlayServices();
      console.log("Google Play Services are available");
        await GoogleSignin.signOut();
      const userInfo = await GoogleSignin.signIn();
      console.log(userInfo);
     
      console.log(userInfo);
      const { email} = userInfo?.data?.user;
      
  
      console.log("Google Sign-In Success:", userInfo);
  
      signInWithGoogle(email);
    } catch (error) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        alert("Google sign-in was cancelled");
      } else if (error.code === statusCodes.IN_PROGRESS) {
        alert("Google sign-in is already in progress");
      } else {
        console.error("Google Sign-In Error:", error);
        alert("An error occurred during Google Sign-In");
      }
    }
  };
  
 
  const signInWithGoogle = async (email) => {
    try {
      setLoader(true); 
      const response = await fetch(
        `${BASE_URL}/api/passenger/auth/googleAuth`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({email}),
        }
      );
  console.log(email);
      const data = await response.json();
      console.log("Google Sign-Up Response:", JSON.stringify(data, null, 2));
      console.log('🔍 Google token (data.token):', data?.token);
      console.log('🔍 Google message (data.message):', data?.message);
      console.log('🔍 Google data:', data?.data);

      // Detailed token analysis for Google sign-in
      console.log('🔍 GOOGLE TOKEN ANALYSIS:');
      console.log('- Token field type:', typeof data?.token);
      console.log('- Token field value:', data?.token);
      console.log('- Message field type:', typeof data?.message);
      console.log('- Message field value:', data?.message);
      console.log('- Token field length:', data?.token?.length);
      console.log('- Message field length:', data?.message?.length);
      console.log('- Token stringified:', JSON.stringify(data?.token));
      console.log('- Message stringified:', JSON.stringify(data?.message));

      dispatch(overwriteStore({ name: 'user', value: data?.data || [] }));

      if (response.ok && data?.success) {
        // Save authentication data using AuthUtils
        // Check both data.token and data.message for the token
        const token = data?.token || data?.message;
        console.log('💾 Attempting to save Google auth data...');
        const authSaved = await AuthUtils.saveAuthData(token, data?.data);
        console.log('💾 Google auth data saved:', authSaved);

        if (authSaved) {
          navigation.replace("SNotification");
        } else {
          alert("Failed to save login data. Please try again.");
        }
      } else {
        alert(data?.data || "Failed to sign up with Google");
      }
    } catch (error) {
      console.error("Error signing up with Google:", error);
      alert("An error occurred during Google Sign-Up");
    } finally {
      setLoader(false); // Hide loader
    }
  };

  return (
    <GradientBackground>
      {/* Loader */} 
      <Spinner visible={loader} />
<View style={{paddingTop:50}}/>

 
      {/* Header */}
      <TouchableOpacity style={styles.backButton}>
        <Image source={images.logo} style={styles.icon} />
      </TouchableOpacity>

      {/* Title */}
      <Text style={styles.title}>Welcome back to RIDEFUZE</Text>

      {/* Mobile Number Input */}
      <View style={styles.inputContainer}>
        {/* Country Picker */}
<TouchableWithoutFeedback
   >
        <CountryPicker
          countryCode={countryCode}
          withFlag
          withFilter
          withCallingCode
          onSelect={(country) => {
            setCountryCode(country.cca2);
            setCallingCode(country.callingCode[0]);
          }}
          containerButtonStyle={styles.countryPicker}
        />
</TouchableWithoutFeedback>
        <Text style={styles.callingCode}>+{callingCode}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your mobile number"
          placeholderTextColor={COLORS.grey}
          value={mobileNumber}
          onChangeText={setMobileNumber}
          keyboardType="phone-pad"
          maxLength={10}
        />
      </View>
      <Text style={styles.subtitle}>We’ll text a code to verify your mobile number</Text>

      {/* Continue Button */}
      <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
        <Text style={styles.continueButtonText}>Continue →</Text>
      </TouchableOpacity>
   {/* OR Divider */}
      <Text style={styles.orText}>OR</Text>

      {/* Social Signup Buttons */}
      <TouchableOpacity style={styles.socialButton} onPress={handleGoogleSignIn}>
        <Image source={icons.GoogleIcon} style={styles.socialIcon} />
        <Text style={styles.socialButtonText}>Sign in with Google</Text>
      </TouchableOpacity>


      {/* Footer */}
      <Text style={styles.footerText}>
        Don't have an Account?{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('GetStarted')}>
          Sign up
        </Text>
      </Text>
      <Text style={styles.termsText}>
        By Proceeding, you agree to get calls, SMS messages, including automated calls from{' '}
        <Text style={styles.brandText}>RideFuze</Text> and its affiliated numbers. Text “STOP” to 20013 to opt-out and you consent to its{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('PrivacyPolicyScreen')}>
          Privacy Policy
        </Text>{' '}
        and{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('TermsOfServiceScreen')}>
          Terms of Service
        </Text>.
      </Text>
    </GradientBackground>
  );
};

 


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F8FBFF',
  },
  backButton: {
    alignItems:'center',
    // marginBottom: 20,
  },
  icon: {
    // margin:20,
    width: 150,
    height: 150,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.h3,
    textAlign: 'center',
    marginBottom: 10,
    color: COLORS.black,
  },
  subtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    marginBottom: 10,
    color: '#555',
  },
  inputContainer: {
    marginHorizontal:20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    marginBottom: 20,
  },
  countryPicker: {
    marginRight: 10,
  },
  callingCode: {
    ...FONTS.body3,
    marginRight: 10,
    color: COLORS.black,
  },
  input: {
    flex: 1,
    height: 50,
    ...FONTS.body3,
    color: COLORS.black,
    backgroundColor: '#fff',
  },
    marginHorizontal:20,
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal:20,

  },
  continueButtonText: {
    color: '#fff',
  ...FONTS.h3
  },
  orText: {
    textAlign: 'center',
    ...FONTS.body3,
    marginVertical: 10,
    color: '#555',
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal:20,

    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingVertical: 13,
    paddingHorizontal: 20,
    marginBottom: 10,
    backgroundColor: '#fff',
  },
  socialIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  socialButtonText: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  footerText: {
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal:20,

    ...FONTS.body3,
    color: '#555',
  },
  linkText: {
    color: COLORS.primary,
    ...FONTS.h4,
    fontSize:12
  },
  termsText: {
    textAlign: 'justify',
    ...FONTS.body4,
    marginHorizontal:22,

    color: '#555',
  },
  brandText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
});

export default WelcomeBack;
