import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ActivityIndicator,
  Platform,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useNavigation } from '@react-navigation/native';

const SNotification = () => {
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);

  const handleNavigate = () => {
    setModalVisible(true);
    setTimeout(() => {
      setModalVisible(false);
      navigation.navigate('TabStack'); // Replace with the next screen name
    }, 200);
  };

  return (
    <GradientBackground>
      {/* Header Section */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}
      
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} >
        </TouchableOpacity>
        <Image source={images.notify} style={styles.illustration} resizeMode="contain" />
      </View>

      {/* Text Section */}
      <View style={styles.textSection}>
        <Text style={styles.mainText}>
          Don’t forget to check details and account activity, it’s important stuff!
        </Text>
        <Text style={styles.subText}>
          Get car booking updates, personalized recommendations, and more with a user-friendly{' '}
          <Text style={styles.brandName}>RideFuze</Text> platform
        </Text>
      </View>

      {/* Footer Buttons */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.notifyButton} onPress={handleNavigate}>
          <Text style={styles.notifyButtonText}>Yes, notify me</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.skipButton} onPress={handleNavigate}>
          <Text style={styles.skipButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Modal for Loading */}
      <Modal visible={modalVisible} transparent animationType="fade">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalText}>Welcome back to</Text>
            <Text style={styles.modalTitle}>RIDEFUZE</Text>
            <ActivityIndicator size="large" color={COLORS.primary} />
          </View>
        </View>
      </Modal>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 10,
    left: 10,
  },
  icon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  illustration: {
    width: '100%',
    height: 300,
    marginTop: 70,
  },
  textSection: {
    marginHorizontal: 20,
    flex: 1,
    alignItems: 'center',
    padding: 20,
    marginTop: 50,
  },
  mainText: {
    ...FONTS.h3,
    textAlign: 'center',
    color: '#343A40',
    marginBottom: 10,
  },
  subText: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
  },
  brandName: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal: 20,
  },
  notifyButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    marginRight: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  notifyButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  skipButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  skipButtonText: {
    ...FONTS.h3,
    color: COLORS.primary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    width: '80%',
  },
  modalText: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.primary,
    marginBottom: 10,
  },
  modalTitle: {
    ...FONTS.h1,
    color: COLORS.primary,
    marginBottom: 20,
  },
});

export default SNotification;
