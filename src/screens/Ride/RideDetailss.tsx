
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Share,
  TextInput,
  Platform,
} from 'react-native';
import MapView, { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'react-native-maps';
import { COLORS, FONTS, images, SIZES } from '../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import { io } from 'socket.io-client';
import GooglePlacesAutocompleteComponent from '../../components/shared/GooglePlaceAutoComplete';
import { BASE_URL } from '../../../Baseurl';

const SOCKET_URL = `${BASE_URL}/passenger`;
const socket = io(SOCKET_URL, {
  transports: ["websocket"],
  withCredentials: true,
});
const RideDetailss = ({ route, navigation }) => {
  const { rideId } = route.params;
  const [rideDetails, setRideDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);
  const [isDisclaimerVisible, setDisclaimerVisible] = useState(false);
  const [isReasonSheetVisible, setReasonSheetVisible] = useState(false);
  const [isCancellationReasonVisible, setCancellationReasonVisible] = useState(false);
  const [isConnectDriverVisible, setConnectDriverVisible] = useState(false);
  const [isSafetyVisible, setSafetyVisible] = useState(false);
  const [isEmergencyVisible, setEmergencyVisible] = useState(false);
  const [isReportSafetyVisible, setReportSafetyVisible] = useState(false);
  const [isThankYouVisible, setThankYouVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');
  const [newDestination, setNewDestination] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [destinations, setDestinations] = useState(rideDetails?.to || []);
  const [driverDetails, setDriverDetails] = useState(null);
  const [isLoadingDriver, setIsLoadingDriver] = useState(false);
  const [show, setShow] = useState(false);
  const [isShareModalVisible, setShareModalVisible] = useState(false);

  const shareRide = async () => {
    try {
      const result = await Share.share({
        message: 'I am sharing my ride details with you! 🚗\nPickup: AMLI 7th Street Station.\nDropoff: Fisherman’s Wharf.\nArrival: 13th July, 10:00 PM.\nPrice: $54',
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Shared successfully');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error) {
      console.log('Error sharing:', error.message);
    }
  };

  const cancelRide = () => {
    if (!cancellationReason.trim()) return alert("Please enter a reason");

    setIsLoading(true); // Show loader

    const cancellationData = {
      rideId: rideId,
      reason: cancellationReason,
    };

    console.log('Sending cancellation request:', cancellationData);

    // Emit cancellation request to the server
    socket.emit('cancelRide', cancellationData);

    // The response will now be handled by `socket.on('cancelRide')`
  };

  const handleToSelect = (index, location) => {
    if (!location) return;

    setDestinations((prevDestinations) => {
      const updatedDestinations = [...prevDestinations];

      updatedDestinations[index] = {
        place: location.description,
        placeId: location.place_id,
        locationCoordinates: {
          type: "Point",
          coordinates: [location?.location?.lng, location?.location?.lat],
        },
      };

      console.log("Updated Destinations:", updatedDestinations);
      return updatedDestinations;
    });
  };


  const editRide = () => {
    if (destinations.length === 0) {
      alert("Please select at least one destination.");
      return;
    }

    const editData = {
      rideId: rideDetails?.rideId,
      to: destinations.filter(dest => dest.place), // Remove empty destinations
    };

    console.log("Edit Ride Data:", JSON.stringify(editData, null, 2));

    // Emit the edit request
    socket.emit('editRide', editData);
  };





  useEffect(() => {
    socket.on('cancelRide', (data) => {
      console.log('Ride Cancellation Received:', data);
      setIsLoading(false);

      if (data?.success) {
        alert('Your ride has been successfully canceled.');
        setCancellationReasonVisible(false);
        setConnectDriverVisible(true);
      } else {
        alert(data?.message || 'Failed to cancel ride');
      }
    });

    return () => {
      socket.off('cancelRide'); // Clean up listener when component unmounts
    };
  }, []);
  // console.log(JSON.stringify(rideDetails, null, 2),'rideDetails');

  // Fetch Ride Details
  const fetchRideDetails = async () => {
    try {
      console.log('Fetching ride details...', rideId);

      const response = await fetch(
        `${BASE_URL}/api/rides/getPassengerRide/${rideId}`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      const data = await response.json();
      console.log('Ride Details:', JSON.stringify(data, null, 2));
      if (response.ok) {
        setRideDetails(data?.data);

        // Fetch driver details if driverId is available
        if (data?.data?.driverId) {
          fetchDriverDetails(data.data.driverId);
        }
      } else {
        alert('Failed to fetch ride details');
      }
    } catch (error) {
      console.error('Error fetching ride details:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDriverDetails = async (driverId) => {
    try {
      setIsLoadingDriver(true);
      console.log('Fetching driver details for driverId:', driverId);

      const response = await fetch(
        `${BASE_URL}/api/driver/profile/getDriverDetail/${driverId}`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await response.json();
      console.log('=== DRIVER DETAILS API RESPONSE (RideDetailss) ===');
      console.log(JSON.stringify(data, null, 2));
      console.log('=== END DRIVER DETAILS RESPONSE ===');

      if (response.ok) {
        setDriverDetails(data?.data || data);
        console.log('Driver details set successfully:', data?.data || data);
      } else {
        console.error('Failed to fetch driver details:', data);
        alert('Could not fetch driver information');
      }
    } catch (error) {
      console.error('Error fetching driver details:', error);
      alert('Could not fetch driver information');
    } finally {
      setIsLoadingDriver(false);
    }
  };

  useEffect(() => {
    socket.on('editRide', (response) => {
      console.log('Edit Ride Response:', response);

      if (response?.success) {
        alert(response?.message || 'Ride updated successfully');
        setIsEditing(false);
      } else {
        alert(response?.message || 'Failed to update ride');
      }
    });

    return () => {
      socket.off('editRide');
    };
  }, []);


  useEffect(() => {
    fetchRideDetails();
  }, []);

  if (loading) {
    return <ActivityIndicator size="large" color="blue" />;
  }

  if (!rideDetails) {
    return <Text style={styles.errorText}>No Ride Details Available</Text>;
  }
  const region = {
    latitude: rideDetails?.fromCoordinates?.coordinates[1] || 33.6844, // Default latitude
    longitude: rideDetails?.fromCoordinates?.coordinates[0] || -117.7521, // Default longitude
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };
  return (
    <View style={styles.container}>
      {/* Map Section */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}
      
      {isReasonSheetVisible && (
        <View style={styles.overlay} />
      )}
      {isShareModalVisible && (
        <View style={styles.overlay} />
      )}
      {isReportSafetyVisible && (
        <View style={styles.overlay} />
      )}


      {isDisclaimerVisible && (
        <View style={styles.overlay} />
      )}

      {isThankYouVisible && (
        <View style={styles.overlay} />
      )}

      {isEmergencyVisible && (
        <View style={styles.overlay} />
      )}
      {isSafetyVisible && (
        <View style={styles.overlay} />
      )}

      {isConnectDriverVisible && (
        <View style={styles.overlay} />
      )}
      {isCancellationReasonVisible && (
        <View style={styles.overlay} />
      )}
      {/* Map Section */}
      <MapView style={styles.map} initialRegion={region}>
        {rideDetails?.fromCoordinates?.coordinates && (
          <Marker
            coordinate={{
              latitude: rideDetails?.fromCoordinates?.coordinates[1],
              longitude: rideDetails?.fromCoordinates?.coordinates[0],
            }}
          >
            <Image source={images?.locate} style={{ width: 15, height: 15, resizeMode: 'contain' }} />
          </Marker>
        )}
        {rideDetails?.to?.[0]?.locationCoordinates?.coordinates && (
          <Marker
            coordinate={{
              latitude: rideDetails.to[0].locationCoordinates.coordinates[1],
              longitude: rideDetails.to[0].locationCoordinates.coordinates[0],
            }}
          >
            <Image source={images.locate1} style={{ width: 15, height: 15, resizeMode: 'contain' }} />
          </Marker>
        )}
        {rideDetails?.fromCoordinates?.coordinates &&
          rideDetails?.to?.[0]?.locationCoordinates?.coordinates && (
            <Polyline
              coordinates={[
                {
                  latitude: rideDetails.fromCoordinates.coordinates[1],
                  longitude: rideDetails.fromCoordinates.coordinates[0],
                },
                {
                  latitude: rideDetails.to[0].locationCoordinates.coordinates[1],
                  longitude: rideDetails.to[0].locationCoordinates.coordinates[0],
                },
              ]}
              strokeColor={COLORS.primary}
              strokeWidth={3}
            />
          )}
      </MapView>

      {/* Ride Information Section */}
      <ScrollView style={styles.bottomSheet}>

        {/* Ride Info */}
        <Text style={styles.arrivalTime}>{new Date(rideDetails.createdAt).toLocaleString()}</Text>
        <Text style={styles.carInfo}>{rideDetails?.carDetails?.model || 'Car'} | {rideDetails?.carDetails?.color || 'N/A'} |{' '}
          {rideDetails?.carDetails?.registrationNumber || 'N/A'}</Text>


        <TouchableOpacity style={styles.rideDetailsContainer} onPress={() => navigation.navigate('RideTrackingScreen')}>
          <View style={styles.rideDetailsHeader}>
            <Text style={styles.rideDetailsTitle}>Ride details</Text>
            <TouchableOpacity onPress={() => setMenuVisible(!menuVisible)}>
              <Image source={images.more} style={styles.moreIcon} />
            </TouchableOpacity>
          </View>
          <Text style={styles.meetDriverText}>Meet driver at pick up spot: {rideDetails?.pickupPoint || ''}</Text>
          <Text style={styles.pickupDescription}>
            The ride distance is {rideDetails?.kmDistance || 'N/A'} miles,
            the payment method is {rideDetails?.paymentMethod || 'N/A'},
            the pickup point is {rideDetails?.from || 'N/A'},
            the number of passengers is {rideDetails?.noOffPassengers || 'N/A'},
            and the status of the ride is {rideDetails?.status || 'N/A'}.
          </Text>

          {menuVisible && (
            <View style={styles.menuContainer}>
              <TouchableOpacity style={styles.menuItem} onPress={() => setShareModalVisible(true)}>
                <Image source={images.share} style={styles.moreIcon} />

                <Text style={styles.menuText}>Share</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('SplitRideScreen')}>
                <Image source={images.splitride} style={styles.moreIcon} />

                <Text style={styles.menuText}>Split ride</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.menuItem} onPress={() => setDisclaimerVisible(true)}>
                <Image source={images.endride} style={styles.moreIcon} />

                <Text style={styles.menuTextRed}>End ride</Text>
              </TouchableOpacity>
            </View>
          )}
        </TouchableOpacity>

        {/* Driver Info */}
        <View style={styles.driverInfoContainer}>
          <Image source={images.driver1} style={styles.driverImage} />
          <Image source={images.CarSingle} style={[styles.driverImage, { position: 'relative', left: -20 }]} />
          <View style={styles.driverDetails}>
            <Text style={styles.driverName}>
              {driverDetails?.firstName && driverDetails?.lastName
                ? `${driverDetails.firstName} ${driverDetails.lastName}`
                : driverDetails?.name
                || rideDetails?.driverName
                || rideDetails?.driver?.name
                || 'Driver'}
            </Text>
            {isLoadingDriver && (
              <ActivityIndicator size="small" color={COLORS.primary} />
            )}
          </View>
          <View style={{ position: 'absolute', right: 0, }}>
            <Text style={styles.driverRating}>4.4 ★ | {rideDetails?.totalRides || 'N/A'} </Text>
            <Text style={styles.driverPrice}>${rideDetails?.charge || 'N/A'}</Text>
          </View>
        </View>


        <View style={{ flexDirection: 'row', flex: 1, marginVertical: 15 }}>
          <Image source={images.routeIndicator} style={styles.routeIndicator} />
          <View>
            <View style={styles.locationRow}>

              <Text style={styles.locationText}>From: {rideDetails?.from || 'N/A'}</Text>
              <TouchableOpacity>
                <Image source={images.edit} style={styles.editIcon} />
              </TouchableOpacity>
            </View>
            <View style={styles.locationRow}>
              <Text style={styles.locationText}>To: {rideDetails?.to?.[0]?.place || 'N/A'}</Text>
              <TouchableOpacity onPress={() => setShow(!show)}>
                <Image source={images.add} style={styles.editIcon} />
              </TouchableOpacity>
            </View>

             {show &&   <View style={styles.locationRow}>
              {/* <Text style={styles.locationText}>To: {rideDetails?.to?.[0]?.place || 'N/A'}</Text> */}

          

                <View style={styles.locationText}>
                  <GooglePlacesAutocompleteComponent

                    placeholder="Enter destination"
                    onSelect={location => handleToSelect(0, location)}
                  // value={destinations[0]?.place}
                  />
                </View>

                  <TouchableOpacity onPress={() => editRide()}>
                    <Image source={images.send} style={{ width: 50, height: 50, resizeMode: 'contain' }} />
                  </TouchableOpacity>

                </View>
            }

          </View>
        </View>




        {/* Contact Driver */}
        <View style={styles.contactContainer}>
          <TouchableOpacity
            onPress={() => navigation.navigate('ChatScreen', { rideId: rideDetails?.rideId })}
            style={[styles.messageButton, { width: '70%' }]}
          >

            <Text style={styles.messageText}>
              Message {driverDetails?.firstName && driverDetails?.lastName
                ? `${driverDetails.firstName} ${driverDetails.lastName}`
                : driverDetails?.name
                || rideDetails?.driverName
                || rideDetails?.driver?.name
                || 'Driver'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => navigation.navigate('CallScreen')} style={[styles.messageButton, { backgroundColor: COLORS.white, flexDirection: 'row', alignItems: 'center' }]}>
            <Image source={images.call} style={styles.callIcon} />
            <Text style={styles.messageText}>Call</Text>

          </TouchableOpacity>
        </View>

        {/* Safety Section */}
        <TouchableOpacity style={styles.safetyButton}
          // onPress={() => navigation.navigate('RideTrackingScreen')}
          onPress={() => setSafetyVisible(true)}
        >
          <Text style={styles.safetyButtonText}>Safety</Text>
          <Image source={images.safety} style={styles.callIcon} />

        </TouchableOpacity>
      </ScrollView>
      {isShareModalVisible && (
        <View style={styles.shareModal}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShareModalVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.shareModalTitle}>
            Share your location to help driver find you faster
          </Text>

          <Image source={images.locationBeep} style={styles.locationImage} />

          <Text style={styles.shareDescription}>
            Location shared while the app is open
          </Text>
          <Text style={styles.shareDescription}>
            Get picked at the exact location
          </Text>
          <Text style={styles.shareDescription}>
            Make you and the driver safer
          </Text>
          <Text style={styles.shareDisclaimer}>
            You can disable location sharing anytime by tapping the share icon.
            Sharing only happens when drivers are 5 mins or 200 meters away.
          </Text>

          <TouchableOpacity style={styles.shareButton} onPress={() => navigation.navigate('ShareRideScreen')}>
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.dontShareButton}
            onPress={() => setShareModalVisible(false)}
          >
            <Text style={styles.dontShareButtonText}>Don’t share</Text>
          </TouchableOpacity>
        </View>
      )}

      {isDisclaimerVisible && (
        <View style={styles.disclaimerSheet}>
          <TouchableOpacity
            style={styles.disclaimerCloseButton}
            onPress={() => setDisclaimerVisible(false)}
          >
            <Text style={styles.disclaimerCloseText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.disclaimerTitle}>Disclaimer</Text>
          <Image source={images.disclaimerImage} style={styles.disclaimerImage} />
          <Text style={styles.disclaimerText}>
            <Text style={styles.disclaimerBold}>1. Free cancellation:</Text> Within 2–5 minutes of requesting a ride (varies by location).
          </Text>
          <Text style={styles.disclaimerText}>
            <Text style={styles.disclaimerBold}>2. Cancellation fee:</Text> Applies if you cancel after the free cancellation window (typically $5–$10).
          </Text>
          <Text style={styles.disclaimerText}>
            <Text style={styles.disclaimerBold}>3. Driver arrival:</Text> If the driver has already arrived, you may be charged a cancellation fee.
          </Text>

          <TouchableOpacity style={styles.keepRideButton} onPress={() => setDisclaimerVisible(false)}>
            <Text style={styles.keepRideButtonText}>Keep my ride</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.cancelRideButton}
            onPress={() => {
              setDisclaimerVisible(false);
              setReasonSheetVisible(true);
            }}
          >
            <Text style={styles.cancelRideButtonText}>Yes, cancel</Text>
          </TouchableOpacity>
        </View>
      )}

      {isReasonSheetVisible && (
        <View style={styles.reasonSheet}>
          <TouchableOpacity
            style={styles.reasonCloseButton}
            onPress={() => setReasonSheetVisible(false)}
          >
            <Text style={styles.reasonCloseText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.reasonTitle}>Why do you want end the ride?</Text>

          {[
            "Driver not getting closer",
            "Driver arrived early",
            "Driver didn't get here in time",
            "Driver asked me to cancel",
            "Could not find the driver",
            "Other reason",
          ].map((reason, index) => (
            <TouchableOpacity
              key={index}
              style={styles.reasonOption}
              onPress={() => {
                setReasonSheetVisible(false);
                // Add reason selection logic here
              }}
            >
              <Text style={styles.reasonOptionText}>{reason}</Text>
            </TouchableOpacity>
          ))}

          <TouchableOpacity
            style={styles.keepRideButton}
            onPress={() => {
              setReasonSheetVisible(false);
              setCancellationReasonVisible(true);

            }}
          >
            <Text style={styles.keepRideButtonText}> Submit your reason</Text>
          </TouchableOpacity>
        </View>
      )}

      {isCancellationReasonVisible && (
        <View style={styles.cancellationReasonSheet}>
          <TouchableOpacity
            style={styles.reasonCloseButton}
            onPress={() => setCancellationReasonVisible(false)}
          >
            <Text style={styles.reasonCloseText}>×</Text>
          </TouchableOpacity>


          <Text style={styles.reasonTitle}>Cancellation reason</Text>

          <View style={styles.textAreaContainer}>
            <TextInput
              style={styles.textArea}
              placeholder="Please state your cancellation reason"
              placeholderTextColor={COLORS.grey}
              multiline={true}
              numberOfLines={5}
              value={cancellationReason}
              onChangeText={setCancellationReason}
            />
          </View>



          <TouchableOpacity
            style={styles.sendButton}
            onPress={cancelRide}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.sendButtonText}>Send</Text>
            )}
          </TouchableOpacity>

        </View>
      )}

      {isConnectDriverVisible && (
        <View style={styles.connectDriverSheet}>
          <TouchableOpacity
            style={styles.reasonCloseButton}
            onPress={() => setConnectDriverVisible(false)}
          >
            <Text style={styles.reasonCloseText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.connectDriverTitle}>Are you sure you want to end this ride?</Text>
          <Text style={styles.connectDriverSubtitle}>Cancel your ride with Samad?</Text>
          <Text style={styles.connectDriverInfo}>Instead, we can connect you with another nearby driver</Text>

          <View style={styles.driverInfo}>
            <Image source={images.driver1} style={styles.driverAvatar} />

            <Image source={images.CarSingle} style={styles.driverCarImage} />
            <View style={styles.driverDetails}>
              <Text style={styles.driverName}>
                {driverDetails?.firstName && driverDetails?.lastName
                  ? `${driverDetails.firstName} ${driverDetails.lastName}`
                  : driverDetails?.name
                  || rideDetails?.driverName
                  || rideDetails?.driver?.name
                  || 'Driver'}
              </Text>
              <Text style={styles.driverStats}>
                {rideDetails?.driverRating || '4.4'} ★ | {rideDetails?.totalRides || '53'} rides
              </Text>
            </View>
            <Text style={styles.driverPrice}>$12.5</Text>
          </View>

          <TouchableOpacity
            style={styles.connectNewDriverButton}
            onPress={() => {
              // Add your "Connect New Driver" logic here
              setConnectDriverVisible(false);
              navigation.navigate('ChooseADriver')
            }}
          >
            <Text style={styles.connectNewDriverText}>Connect new driver</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.cancelRideButton}
            onPress={() => {
              // Add your "Cancel Ride" logic here
              setConnectDriverVisible(false);
            }}
          >
            <Text style={styles.cancelRideText}>Yes, cancel</Text>
          </TouchableOpacity>
        </View>
      )}
      {isSafetyVisible && (
        <View style={styles.safetySheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setSafetyVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.safetyTitle}>Safety</Text>
          <Text style={styles.safetySubtitle}>
            This is in place to help you feel safe and secure
          </Text>

          <TouchableOpacity style={styles.safetyOption} onPress={() => {
            setSafetyVisible(false)
            setEmergencyVisible(true)
          }}>
            <Image source={images.emergency} style={styles.safetyIcon} />
            <Text style={styles.safetyOptionText}>Emergency Assistance</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.safetyOption} onPress={() => { setReportSafetyVisible(true) }}>
            <Image source={images.report} style={styles.safetyIcon} />
            <Text style={styles.safetyOptionText}>Report safety issues</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.safetyOption} onPress={shareRide}>
            <Image source={images.sharerideinfo} style={styles.safetyIcon} />
            <Text style={styles.safetyOptionText}>Share ride info</Text>
          </TouchableOpacity>
        </View>
      )}
      {isEmergencyVisible && (
        <View style={styles.emergencySheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setEmergencyVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.emergencyTitle}>Emergency Assistance</Text>
          <Text style={styles.emergencySubtitle}>
            Share your location information and car details with admin
          </Text>

          <View style={styles.rideDetails}>
            <Text style={styles.rideDetailsTitle}>Ride details</Text>
            <Text style={styles.pickupText}>Meet driver at pick up spot</Text>
            <Text style={styles.pickupDescription}>
              AMLI 7th Street Station..., Driver will arrive here to pick you up
            </Text>
          </View>

          <View style={styles.driverDetails}>
            <View style={{ flexDirection: 'row' }}>
              <Image source={images.CarSingle} style={styles.carImage} />
              <Image source={images.driver1} style={[styles.carImage, { position: 'relative', left: -20 }]} />
            </View>
            <View>
              <Text style={styles.driverName}>
                {driverDetails?.firstName && driverDetails?.lastName
                  ? `${driverDetails.firstName} ${driverDetails.lastName}`
                  : driverDetails?.name
                  || rideDetails?.driverName
                  || rideDetails?.driver?.name
                  || 'Driver'}
              </Text>
            </View>
            <View>
              <Text style={styles.driverPrice}>$12.5</Text>
              <Text style={styles.driverRating}>4.4 ★ | 53 rides</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.panicButton} onPress={() => {
            setEmergencyVisible(false)
            setReportSafetyVisible(true)
          }}>
            <Text style={styles.panicButtonText}>Panic alarm</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.callButton}>
            <Text style={styles.callButtonText}>Call 911</Text>
          </TouchableOpacity>
        </View>
      )}
      {isReportSafetyVisible && (
        <View style={styles.reportSafetySheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setReportSafetyVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.reportSafetyTitle}>Report safety issues</Text>
          <Text style={styles.reportSafetySubtitle}>
            Tell us about any safety concern on this trip...it's confidential
          </Text>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Describe the situation"
              placeholderTextColor={COLORS.grey}
              multiline
            />
          </View>

          <TouchableOpacity style={styles.shareButton} onPress={() => {
            setReportSafetyVisible(false)
            setThankYouVisible(true)
          }}>
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
        </View>
      )}

      {isThankYouVisible && (
        <View style={styles.thankYouSheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setThankYouVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.thankYouText}>Thank you for this important information</Text>

          <TouchableOpacity
            style={styles.doneButton}
            onPress={() => setThankYouVisible(false)}
          >
            <Text style={styles.doneButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      )}



    </View>
  );

};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.25,
  },
  thankYouSheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  thankYouText: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginVertical: 20,
  },
  doneButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  doneButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },

  reportSafetySheet: {
    position: 'absolute',
    bottom: 0,
    zIndex: 1,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  reportSafetyTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 10,
  },
  reportSafetySubtitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.grey,
    marginBottom: 20,
  },
  inputContainer: {
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    ...FONTS.body4,
    padding: 10,
    marginBottom: 20,
  },
  input: {
    ...FONTS.body3,
    color: COLORS.black,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  shareButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  shareButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },

  emergencySheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  emergencyTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 10,
  },
  emergencySubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    marginHorizontal: 40,
    color: COLORS.grey,
    marginBottom: 20,
  },
  rideDetails: {
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  rideDetailsTitle: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  pickupText: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 5,
  },
  pickupDescription: {
    ...FONTS.body4,
    color: COLORS.red,
  },
  driverDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  carImage: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    marginRight: 10,
  },
  driverName: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'right'
  },
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'right'
  },
  panicButton: {
    backgroundColor: COLORS.red,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },
  panicButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  callButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.red,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  callButtonText: {
    ...FONTS.h3,
    color: COLORS.red,
  },

  safetySheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  safetyTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    marginVertical: 10,
    // marginHorizontal:60
  },
  safetySubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginHorizontal: 30,
    marginBottom: 20,
    marginVertical: 10,

  },
  safetyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  safetyIcon: {
    width: 30,
    height: 30,
    marginRight: 15,
  },
  safetyOptionText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: 5,
  },

  connectDriverSheet: {
    position: 'absolute',
    height: SIZES.width * 0.9,
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  connectDriverTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  connectDriverSubtitle: {
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  connectDriverInfo: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 20,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  driverCarImage: {
    width: 40,
    height: 40,
    marginRight: 10,
    resizeMode: 'contain'
  },
  driverAvatar: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    // marginRight:20,
    borderRadius: 20,
    marginRight: 10,
  },
  // driverDetails: {
  //   // flex: 1,
  // },
  driverName: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  driverStats: {
    ...FONTS.body4,
    color: COLORS.grey,
  },

  connectNewDriverButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    marginVertical: 20,
    alignItems: 'center',
    marginBottom: 10,
  },
  connectNewDriverText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  cancelRideButton: {
    backgroundColor: COLORS.white,
    borderColor: COLORS.red,
    borderWidth: 1,
    borderRadius: SIZES.radius,
    padding: 15,
    marginTOp: 30,

    alignItems: 'center',
  },
  cancelRideText: {
    ...FONTS.h3,
    color: COLORS.red,
  },

  cancellationReasonSheet: {
    zIndex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  textAreaContainer: {
    borderColor: COLORS.border,
    borderWidth: 1,
    borderRadius: SIZES.radius,
    marginVertical: 20,
    padding: 10,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    color: COLORS.black,
    ...FONTS.body3,
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  sendButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },

  reasonSheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  reasonCloseButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  reasonCloseText: {
    fontSize: 24,
    color: COLORS.black,
  },
  reasonTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 20,
  },
  reasonOption: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    paddingVertical: 15,
  },
  reasonOptionText: {
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
  },

  disclaimerSheet: {
    zIndex: 1,

    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  disclaimerCloseButton: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  disclaimerCloseText: {
    fontSize: 24,
    color: COLORS.black,
  },
  disclaimerTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 20,
  },
  disclaimerImage: {
    width: 120,
    height: 120,
    resizeMode: 'contain',
    alignSelf: 'center',
    marginBottom: 20,
  },
  disclaimerText: {
    ...FONTS.body4,
    color: COLORS.grey,
    lineHeight: 20,
    marginHorizontal: 20,
    marginBottom: 10,
    textAlign: 'center'
  },
  disclaimerBold: {
    // fontWeight: 'bold',
    color: COLORS.black,
  },
  keepRideButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
    marginTop: 10,

  },
  keepRideButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  cancelRideButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.red,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  cancelRideButtonText: {
    ...FONTS.h3,
    color: COLORS.red,
  },

  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1,
  },

  rideDetailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  moreIcon: {
    marginHorizontal: 10,
    width: 15,
    height: 15,
    resizeMode: 'contain',
  },
  menuContainer: {
    position: 'absolute',
    top: 50,
    right: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
    paddingVertical: 10,
    paddingHorizontal: 15,
    zIndex: 1,
  },
  shareModal: {
    zIndex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  shareModalTitle: {
    ...FONTS.body3,
    marginHorizontal: 50,
    color: COLORS.black,
    textAlign: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  locationImage: {
    width: 60,
    height: 60,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginBottom: 20,
  },
  shareDescription: {
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  shareDisclaimer: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 20,
    fontSize: 12,
  },
  shareButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },

  dontShareButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  dontShareButtonText: {
    ...FONTS.body3,
    color: COLORS.black,
  },

  menuItem: {
    flexDirection: 'row',
    paddingVertical: 10,
    // borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  menuText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  menuTextRed: {
    ...FONTS.body4,
    color: COLORS.red,
  },

  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    marginTop: -30,
  },
  arrivalTime: {
    ...FONTS.body3,
    fontSize: 15,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 5,
  },
  carInfo: {
    ...FONTS.body4,
    fontSize: 12,

    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    paddingBottom: 15,
  },
  rideDetailsContainer: {
    marginBottom: 20,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 15,
    borderRadius: SIZES.radius,
  },
  rideDetailsTitle: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.black,
    marginBottom: 5,
  },
  meetDriverText: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 5,
  },
  pickupDescription: {
    ...FONTS.body4,
    fontSize: 10,
    color: COLORS.black,
  },
  driverInfoContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 20,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    resizeMode: 'contain',
    marginRight: 10,
  },
  // driverDetails: {
  //   // flex: 1,
  // },
  driverName: {
    ...FONTS.h3,
    fontSize: 15,
    color: COLORS.black,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
    fontSize: 12,
  },

  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  locationIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.green,
    marginRight: 10,
  },
  dropoffIndicator: {
    backgroundColor: COLORS.red,
  },
  locationText: {
    ...FONTS.body3,
    fontSize: 13,
    width: '78%',

    color: COLORS.black,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 10,
    borderRadius: 7,
  },
  editIcon: {
    marginHorizontal: 10,
    width: 25,
    height: 25,
  },
  routeIndicator: {
    marginTop: 15,
    width: 60,
    height: 70,
    resizeMode: 'contain',
  },
  contactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    // justifyContent: 'space-between',
    marginBottom: 20,

  },
  messageButton: {
    marginVertical: 15,
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    padding: 10,
  },
  messageText: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  callIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginHorizontal: 15
  },
  safetyButton: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    flexDirection: 'row',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: COLORS.primary,
    padding: 10,
    alignItems: 'center',
  },
  safetyButtonText: {
    ...FONTS.h3,
    color: COLORS.primary,
  },
});

export default RideDetailss;
