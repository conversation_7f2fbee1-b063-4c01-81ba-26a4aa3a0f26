import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';

import {
  View,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
  Dimensions,
  StyleSheet,
  Modal,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, images, SIZES } from '../../constants';
import { BASE_URL } from '../../../Baseurl';

const { width } = Dimensions.get('window');

const RideHistoryScreen = () => {
  const [upcomingPassengerRides, setUpcomingPassengerRides] = useState([]);
  const [pastPassengerRides, setPastPassengerRides] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [selectedTab, setSelectedTab] = useState('Upcoming');

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    
    // Format date like "13 July"
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'long' });
    
    // Format time like "9:50pm"
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
    
    return {
      date: `${day} ${month}`,
      time: `${hours}:${formattedMinutes}${ampm}`
    };
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case "active":
        return "green";
      case "pending":
        return "orange";
      case "in progress":
        return "blue";
      case "requested":
        return "purple";
      default:
        return "gray";
    }
  };

  const fetchUpcomingPassengerRides = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${BASE_URL}/api/rides/getUpcomingPassengerRides`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      const data = await response.json();
      console.log('Upcoming Passenger Rides Response:', data);

      if (response.ok && data?.message?.rides) {
        dispatch(
          overwriteStore({
            name: 'UpcomingPassengerRides',
            value: data.message.rides,
          })
        );
        setUpcomingPassengerRides(data.message.rides);
      }
    } catch (error) {
      console.error('Error fetching upcoming passenger rides:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPastPassengerRides = async (page = 1) => {
    if (page > totalPages) return;
    
    page === 1 ? setIsFetching(true) : setIsFetchingMore(true);
    
    try {
      const response = await fetch(
        `${BASE_URL}/api/rides/getPassengerRides?page=${page}`,
        {
          method: "GET",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
        }
      );
    
      const data = await response.json();
      console.log(`Fetched Past Rides Page ${page}:`, data);
    
      if (response.ok && data.success) {
        setPastPassengerRides((prevRides) => [...prevRides, ...data.message.rides]);
        setTotalPages(data.message.totalPages);
        setCurrentPage(page);
      } else {
        console.error("Failed to fetch rides:", data);
      }
    } catch (error) {
      console.error("Error fetching rides:", error);
    } finally {
      setIsFetching(false);
      setIsFetchingMore(false);
    }
  };

  const fetchPassengerRideDetails = async (rideId) => {
    try {
      navigation.navigate('RideDetailScreen', { rideId });
    } catch (error) {
      console.error('Error navigating to ride details:', error);
    }
  };

  useEffect(() => {
    fetchUpcomingPassengerRides();
    fetchPastPassengerRides(1);
  }, []);

  const handleLoadMore = () => {
    if (!isFetchingMore && currentPage < totalPages) {
      fetchPastPassengerRides(currentPage + 1);
    }
  };

  const UpcomingRides = ({ rides, fetchPassengerRideDetails }) => (
    <FlatList
      data={rides}
      keyExtractor={(item) => item.rideId}
      renderItem={({ item }) => {
        const formattedDateTime = formatDateTime(item.createdAt);
        
        return (
          <TouchableOpacity 
            style={styles.rideItem} 
            onPress={() => fetchPassengerRideDetails(item.rideId)}
          >
            <Image
              source={{ uri: item.carDetails?.carImgUrl || images.CarSingle }}
              style={styles.rideImage}
            />
            <View style={styles.rideInfo}>
              <Text style={styles.rideTitle}>
                {item.rideType.charAt(0).toUpperCase() + item.rideType.slice(1)}
              </Text>
              <Text style={styles.rideDate}>
                {item.from} → {item.to[0]?.place}
              </Text>
              <Text style={styles.rideDate}>KM: {item.kmDistance}</Text>
              <Text style={[styles.rideDate, { color: getStatusColor(item.status) }]}>
                Status: {item.status}
              </Text>
              <View style={styles.bottomRow}>
                <Text style={styles.dateTimeText}>
                  {formattedDateTime.date} • {formattedDateTime.time}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        );
      }}
      ListEmptyComponent={
        <Text style={{paddingHorizontal:20}}>No upcoming rides available</Text>
      }
    />
  );

  const PastRides = ({ rides, fetchPassengerRideDetails }) => (
    <FlatList
      data={rides}
      keyExtractor={(item) => item?.rideId}
      renderItem={({ item }) => {
        const formattedDateTime = formatDateTime(item.createdAt);
        
        return (
          <TouchableOpacity 
            style={styles.rideItem} 
            onPress={() => fetchPassengerRideDetails(item.rideId)}
          >
            <Image source={images.CarSingle} style={styles.rideImage} />
            <View style={styles.rideInfo}>
              <Text style={styles.rideTitle}>
                {item.rideType.charAt(0).toUpperCase() + item.rideType.slice(1)}
              </Text>
              <Text style={styles.rideDate}>
                {item.from} → {item.to[0]?.place}
              </Text>
              <Text style={[styles.rideDate, { color: getStatusColor(item.status) }]}>
                Status: {item.status}
              </Text>
              <View style={styles.bottomRow}>
                <Text style={styles.dateTimeText}>
                  {formattedDateTime.date} • {formattedDateTime.time}
                </Text>
                <View style={styles.reRideContainer}>
                  <Image 
                    source={images.refresh}
                    style={styles.reRideIcon} 
                  />
                  <Text style={styles.reRideText}>Re-ride</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        );
      }}
      ListFooterComponent={() => isFetchingMore ? <ActivityIndicator size="small" color="blue" /> : null}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
    />
  );

  return (
    <View style={{ flex: 1, backgroundColor: COLORS.light_blue }}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />} 
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Rides</Text>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          onPress={() => setSelectedTab('Upcoming')}
          style={[styles.tab, selectedTab === 'Upcoming' && styles.activeTab]}
        >
          <Text
            style={[
              styles.tabText,
              selectedTab === 'Upcoming' && styles.activeTabText,
            ]}
          >
            Upcoming
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setSelectedTab('Past')}
          style={[styles.tab, selectedTab === 'Past' && styles.activeTab]}
        >
          <Text
            style={[
              styles.tabText,
              selectedTab === 'Past' && styles.activeTabText,
            ]}
          >
            Past
          </Text>
        </TouchableOpacity>
      </View>

      {selectedTab === 'Upcoming' ? (
        <UpcomingRides 
          rides={upcomingPassengerRides} 
          fetchPassengerRideDetails={fetchPassengerRideDetails} 
        />
      ) : (
        <View style={{ marginTop: 10 }}>
          <PastRides
            rides={pastPassengerRides} 
            fetchPassengerRideDetails={fetchPassengerRideDetails} 
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  rideItem: {
    backgroundColor: "#fff",
    padding: 15,
    marginVertical: 5,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20
  },
  rideText: {
    fontSize: 14,
    color: "#333",
  },
  rideDate: {
    fontSize: 12,
 
  },
  container: {
    backgroundColor: COLORS.light_blue,
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SIZES.base * 3,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 24,
    height: 24
  },
  headerTitle: {
    ...FONTS.body3,
    marginLeft: SIZES.base * 2
  },
  tabContainer: {
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: COLORS.white,
    paddingVertical: SIZES.base
  },
  tab: {
    paddingVertical: SIZES.base,
    width: '50%',
    alignItems: 'center'
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: COLORS.primary
  },
  tabText: {
    fontSize: SIZES.body3,
    color: COLORS.grey
  },
  activeTabText: {
    color: COLORS.primary
  },
  rideImage: {
    width: 50,
    height: 50,
    marginRight: SIZES.base * 2,
    resizeMode: 'contain'
  },
  rideInfo: {
    flex: 1,

  },
  rideTitle: {
    fontSize: 16,
    fontFamily: FONTS.h3.fontFamily
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  dateTimeText: {
    fontSize: 12,
    color: COLORS.tertiaryText,
  },
  reRideContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reRideIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  reRideText: {
    fontSize: 12,
    color: COLORS.primary,
    ...FONTS.body4,
  },
});

export default RideHistoryScreen;