import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  FlatList,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const SplitRideScreen = () => {
  const [search, setSearch] = useState('');
  const friends = [
    { id: 1, name: '<PERSON>', phone: '(*************', image: images.driver2 },
    { id: 2, name: '<PERSON><PERSON>', phone: '(*************', image: images.driver1 },
    { id: 3, name: '<PERSON><PERSON><PERSON> Opabode', phone: '(*************', image: images.driver3 },
    { id: 4, name: '<PERSON><PERSON><PERSON>', phone: '(*************', image: images.driver2 },
  ];

  const renderFriend = ({ item }) => (
    <TouchableOpacity style={styles.friendItem}>
      <Image source={item.image} style={styles.friendImage} />
      <View style={styles.friendDetails}>
        <Text style={styles.friendName}>{item.name}</Text>
        <Text style={styles.friendPhone}>{item.phone}</Text>
      </View>
    </TouchableOpacity>
  );
const navigation = useNavigation();
  return (
    <View style={styles.container}>
        {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}     
        {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />}
      
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
        <Image source={images.goback} style={styles.backButton}/>
        </TouchableOpacity>
        <Text style={styles.title}>Split ride</Text>
        <Text></Text>
      </View>

<View style={{paddingHorizontal:20}}>


      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search for a friend"
          placeholderTextColor={COLORS.grey}
          value={search}
          onChangeText={setSearch}
        />
      </View>

      {/* Friend List */}
      <FlatList
        data={friends}
        renderItem={renderFriend}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.friendList}
      />

      {/* Footer */}
      <Text style={styles.footerText}>Each rider will pay equal amount</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: COLORS.white,
  },
  backButton: {
    width: 30,
    height: 30,
    ...FONTS.body3,
    color: COLORS.primary,
    marginRight: 10,
  },
  title: {
    ...FONTS.body3,
    fontSize: 14,
    // marginRight: 30,

    textAlign: 'center',
    color: COLORS.black,
  },
  searchContainer: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    paddingHorizontal: 15,
    paddingVertical: 4,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    ...FONTS.body3,
    color: COLORS.black,
  },
  friendList: {
    marginBottom: 20,
  },
  friendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  friendImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
  },
  friendDetails: {
    flex: 1,
  },
  friendName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  friendPhone: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
  },
  footerText: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding:10,
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
    marginTop: 10,
  },
});

export default SplitRideScreen;
