import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BASE_URL } from '../../../Baseurl';
import AuthUtils from '../../utils/AuthUtils';

const ViewProfileScreen = () => {
  const navigation = useNavigation(); 

  const user = useSelector(state => state?.store?.user) || {};
console.log('user:', user);
useEffect(() => {}, [user]);

// Destructure user details
const {
  profileImg,
  firstName,
  lastName,
  email,
} = user;

 

  const [isLogoutSheetVisible, setLogoutSheetVisible] = useState(false);

  const [isDeleteSheetVisible, setDeleteSheetVisible] = useState(false);
const [selectedReasons, setSelectedReasons] = useState([]);
const [isLoggingOut, setIsLoggingOut] = useState(false);

const toggleReasonSelection = (reason) => {
  if (selectedReasons.includes(reason)) {
    setSelectedReasons(selectedReasons.filter((item) => item !== reason));
  } else {
    setSelectedReasons([...selectedReasons, reason]);
  }
};
const logoutUser = async () => {
  setIsLoggingOut(true); // Show loader

  try {
    // Call logout API
    const response = await fetch(`${BASE_URL}/api/auth/signout`, {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
    });

    console.log("Logout response:", JSON.stringify(response,null,2));

    // Clear all authentication data using AuthUtils
    await AuthUtils.clearAuthData();

    // Reset navigation to login screen
    navigation.reset({
      index: 0,
      routes: [{ name: "WelcomeBack" }],
    });

  } catch (error) {
    console.error("Error logging out:", error);

    // Even if API fails, clear local data and redirect to login
    await AuthUtils.clearAuthData();

    // Always redirect to login screen
    navigation.reset({
      index: 0,
      routes: [{ name: "WelcomeBack" }],
    });
  } finally {
    setIsLoggingOut(false); // Hide loader
  }
};



const handleDeleteAccount = () => {
  console.log('Selected Reasons:', selectedReasons);
  // Add logic to handle account deletion here
  setDeleteSheetVisible(false);
};

const cancelDeleteAccount = () => {
  setDeleteSheetVisible(false);
};

  const handleLogout = () => {
    setLogoutSheetVisible(true);
  };
  
  
  
  const confirmLogout = () => {
    setLogoutSheetVisible(false);
    logoutUser(); // Call logout function
  };
  
  const cancelLogout = () => {
    setLogoutSheetVisible(false);
  };
  

  const sections = [
    {
      title: 'Favorite location',
      items: [
        { id: 1, label: 'Add home address', icon: images.house, onPress: () => navigation.navigate('AddHomeAddress'),line:true },
        { id: 2, label: 'Add work address', icon: images.briefcase, onPress: () => navigation.navigate('AddWorkAddress') },
      ],
    },
    {
      title: 'Preference',
      items: [
        { id: 3, label: 'Language', icon: images.flag, onPress: () => navigation.navigate('LanguageScreen') ,line:true },
        { id: 4, label: 'Notification', icon: images.loc, onPress: () => navigation.navigate('NotificationSettings') },
      ],
    },
    {
      title: 'Account',
      items: [
        { id: 5, label: 'Log out', icon: images.logout, onPress: handleLogout ,line:true },
        { id: 6, label: 'Delete account', icon: images.delete, onPress: () => setDeleteSheetVisible(true), isDanger: true },
      ],
    },
  ];

  const renderSection = ({ title, items }) => (
    <View style={styles.section} key={title}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {items.map((item) => (
        <TouchableOpacity
          key={item.id}
          style={[styles.sectionItem, item?.line && { borderBottomWidth: 0.5 }]}
          onPress={item.onPress}
        >
          <View style={styles.sectionItemContent} >
            <Image source={item.icon} style={styles.sectionIcon} />
            <Text
              style={[styles.sectionLabel, item.isDanger && styles.dangerText]}
            >
              {item.label}
            </Text>
          </View>

          <Image source={images.next} style={{width:12,height:12,resizeMode:'contain'}} />

        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={{paddingTop:60}}/>
      
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={()=> navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton}  />
        </TouchableOpacity>
        <Text style={styles.header}>Profile</Text>
        <TouchableOpacity onPress={()=> navigation.navigate('EditProfileScreen')}>
          <Image source={images.edit} style={styles.editButton} />
        </TouchableOpacity>
      </View>

    {/* Profile Card */}
<View style={styles.profileCard}>
  <Image 
    source={profileImg ? { uri: profileImg } : images.profile} 
    style={styles.profileImage} 
  />
  <View style={styles.profileDetails}>
    {/* User Full Name */}
    <Text style={styles.profileName}>{firstName} {lastName}</Text>

    {/* User Email with Verify Button */}
    <View style={styles.emailContainer}>
      <Text style={styles.profileEmail}>{email}</Text>
      <TouchableOpacity onPress={() => navigation.navigate('VerifyEmailScreen')}>
        <Text style={styles.verifyText}>Verify</Text>
      </TouchableOpacity>
    </View>
  </View>
</View>

      {/* Sections */}
      <FlatList
        data={sections}
        renderItem={({ item }) => renderSection(item)}
        keyExtractor={(item) => item.title}
        contentContainerStyle={styles.sectionList}
      />
      {isLogoutSheetVisible && (
  <View style={styles.bottomSheetOverlay}>
    
    <View style={styles.logoutBottomSheet}>
    <TouchableOpacity onPress={cancelLogout} style={{alignSelf:'flex-start'}}>
          <Image source={images.goback} style={styles.backButton}  />
        </TouchableOpacity>
      <Text style={styles.logoutTitle}>Are you sure you want to log out?</Text>
      <TouchableOpacity
        style={styles.backButtonn}
        onPress={cancelLogout}
      >
        <Text style={styles.backButtonText}>Back</Text>
      </TouchableOpacity>
      <TouchableOpacity
  style={[styles.logoutButton, isLoggingOut && styles.disabledButton]}
  onPress={confirmLogout}
  disabled={isLoggingOut}
>
  {isLoggingOut ? (
    <ActivityIndicator size="small" color="#fff" />
  ) : (
    <Text style={styles.logoutButtonText}>Yes, log out</Text>
  )}
</TouchableOpacity>

    </View>
  </View>
)}

{isDeleteSheetVisible && (
  <View style={styles.bottomSheetOverlay}>
     
    <View style={styles.deleteBottomSheet}>
    <TouchableOpacity onPress={()=>setDeleteSheetVisible(false)} style={{alignSelf:'flex-start'}}>
          <Image source={images.goback} style={styles.backButton}  />
        </TouchableOpacity>
      <Text style={styles.deleteTitle}>Delete account</Text>
      <Text style={styles.deleteDescription}>
        We are really sorry to see you go here, do you really want to delete your account? 
        If yes, do you mind telling us why so we could attend to it.
      </Text>
      <View style={styles.reasonsContainer}>
        {[
          'I am no longer using my account',
          'The services is friendly',
          'I want to change my phone',
          "Inride isn't available in my city",
          'Other reasons',
        ].map((reason, index) => (
          <TouchableOpacity
            key={index}
            style={styles.reasonItem}
            onPress={() => toggleReasonSelection(reason)}
          >
            <Text style={styles.reasonText}>{reason}</Text>
            <View
              style={[
                styles.checkbox,
                selectedReasons.includes(reason) && styles.checkboxSelected,
              ]}
            />
          </TouchableOpacity>
        ))}
      </View>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={handleDeleteAccount}
      >
        <Text style={styles.deleteButtonText}>Delete account</Text>
      </TouchableOpacity>
    </View>
  </View>
)}



    </View>
  );
};

const styles = StyleSheet.create({
  logoutButton: {
    backgroundColor: "red",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 15,
  },
  disabledButton: {
    backgroundColor: "#fcfcfc", // Grey out button when logging out
  },
  logoutButtonText: {
    fontSize: 16,
    color: "#fff",
    fontWeight: "bold",
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  bottomSheetOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  deleteBottomSheet: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: SIZES.radius * 2,
    borderTopRightRadius: SIZES.radius * 2,
    padding: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  deleteTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
    textAlign: 'center',
  },
  deleteDescription: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 20,
  },
  reasonsContainer: {
    marginBottom: 20,
  },
  reasonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  reasonText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 5,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
  },
  deleteButton: {
    backgroundColor: COLORS.red,
    borderRadius: SIZES.radius,
    paddingVertical: 15,
    alignItems: 'center',
  },
  deleteButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  logoutBottomSheet: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: SIZES.radius * 2,
    borderTopRightRadius: SIZES.radius * 2,
    padding: 20,
    height: SIZES.height * 0.3,
    alignItems: 'center',
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  logoutTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 20,
    textAlign: 'center',
  },
  backButtonn: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingVertical: 12,
    width: '100%',
    alignItems: 'center',
    marginVertical: 10,
  },
  backButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  logoutButton: {
    backgroundColor: COLORS.white,
    marginVertical: 10,

    borderRadius: SIZES.radius,
    paddingVertical: 12,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.red,
  },
  logoutButtonText: {
    ...FONTS.h3,
    color: COLORS.red,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  header: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  editButton: {
    width: 25,
    height: 25,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius:  7,
    padding: SIZES.padding,
    marginBottom: 20,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 15,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    ...FONTS.h3,
    color: COLORS.black,
    marginBottom: 5,
  },
  emailContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileEmail: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginRight: 5,
  },
  verifyText: {
    ...FONTS.body4,
    color: COLORS.primary,
  },
  section: {
    backgroundColor: COLORS.white,
    borderRadius: 7,
    marginBottom: 20,
    paddingTop: 15,
    paddingHorizontal: SIZES.padding,
   
  },
  sectionTitle: {
    ...FONTS.h4,
    color: COLORS.black,
    paddingBottom: 12,
    marginBottom: 15,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  sectionItem: {
    flexDirection: 'row',
    justifyContent:'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderColor: COLORS.border,
  },
  sectionItemContent: {
    flexDirection: 'row',
    alignItems: 'center',

  },
  sectionIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  sectionLabel: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,

  },
  dangerText: {
    color: COLORS.red,
  },
});

export default ViewProfileScreen;
