import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  InteractionManager,
  ScrollView,
  TextInput,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { useStripe } from '@stripe/stripe-react-native';
import { useDispatch, useSelector } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';
import { BASE_URL } from '../../../Baseurl';

const Payment = () => {
  const navigation = useNavigation();
const [profile, setProfile] = useState();
   const user = useSelector(state => state?.store?.user) || {};
  
  // State for managing the selected fund amount and bottom sheet visibility
const [selectedAmount, setSelectedAmount] = useState(null);
const [showBottomSheet, setShowBottomSheet] = useState(false);
const [savedCards, setSavedCards] = useState([]);
const [loading, setLoading] = useState(false);
const [fundingHistory, setFundingHistory] = useState([]);
const [historyLoading, setHistoryLoading] = useState(false);
 const { presentPaymentSheet, initPaymentSheet } = useStripe();

  // 🟠 Loader State
  const [funding, setFunding] = useState(false);
// const [selectedAmount, setSelectedAmount] = useState(null);

// Function to toggle bottom sheet visibility
const toggleBottomSheet = () => {
  setShowBottomSheet(!showBottomSheet);
};
  const fetchPaymentIntentClientSecret = async () => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/passenger/profile/fundwallet`, 
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount: selectedAmount, 
            currency: 'usd',
          }),
        }
      );

      const data = await response.json();
      console.log('Payment Intent Response:', data);

      // ✅ Correct extraction of clientSecret
      const clientSecret = data?.data || data?.data;
      if (!clientSecret) {
        Alert.alert('Error', 'No client secret returned from backend');
        return null;
      }

      return clientSecret;
    } catch (error) {
      console.error('Error fetching payment intent:', error);
      Alert.alert('Error', 'Failed to create payment intent');
      return null;
    }
  };
 

const handlePayPress = async () => {
    try {
      setFunding(true);
  
      // 🟡 STEP 1: Fetch Client Secret
      const clientSecret = await fetchPaymentIntentClientSecret();
      if (!clientSecret) {
        setFunding(false);
        Alert.alert('Error', 'Could not retrieve payment intent');
        return;
      }
  
      // 🟠 STEP 2: Initialize Payment Sheet
      const { error: initError } = await initPaymentSheet({
        paymentIntentClientSecret: clientSecret,
        merchantDisplayName: 'RideFuze Wallet',
        allowsDelayedPaymentMethods: true,
      });
  
      if (initError) {
        setFunding(false);
        console.error('PaymentSheet Init Error:', initError.message);
        Alert.alert('Error', `Initialization failed: ${initError.message}`);
        return;
      }
  
      // 🟢 STEP 3: Present Payment Sheet (Wait for completion)
      const { error } = await presentPaymentSheet();
  
      if (error) {
        setFunding(false);
        console.error('PaymentSheet Error:', error.message);
        Alert.alert('Payment Failed', error.message);
        return;
      }
  
      // ✅ STEP 4: Show Success Alert (WAIT using InteractionManager)
      Alert.alert('Success', 'Wallet funded successfully!', [
        {
          text: 'OK',
          onPress: () => {
            InteractionManager.runAfterInteractions(() => {
              setTimeout(() => {
                navigation.replace('Payment');
              }, 300);
            });
          },
        },
      ]);
    } catch (error) {
      console.error('Unexpected Payment Error:', error);
      Alert.alert('Error', 'An unexpected error occurred during payment.');
    } finally {
      setFunding(false); // Stop loader
    }
  };  

  
  const fetchPaymentCards = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${BASE_URL}/api/passenger/card/getCardDetails`, 
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      const data = await response.json();
      console.log('Saved Payment Cards:', JSON.stringify(data, null, 2));
  
      if (response.ok && data?.data?.cards) {
        setSavedCards(data.data.cards);
      } else {
        // alert('Failed to fetch payment cards');
      }
    } catch (error) {
      console.error('Error fetching payment cards:', error);
      // alert('Error fetching payment cards');
    } finally {
      setLoading(false);
    }
  };

  const renderSavedCard = ({ item }) => (
    <TouchableOpacity 
      style={styles.paymentMethod}
      onPress={() => navigation.navigate('CardDetails', { cardId: item._id })}
    >
      <View style={styles.paymentMethodDetails}>
        <Image source={images.masterCard} style={styles.paymentIcon} />
        <Text style={styles.paymentText}>
        {item.cardType}  {item.cardNumber}       {item.cardHolderName}
        </Text>
      </View>
      <Image source={images.next} style={styles.arrowIcon} />
    </TouchableOpacity>
  );
  
  const fetchFundingHistory = async () => {
    setHistoryLoading(true);
    try {
      const response = await fetch(
        `${BASE_URL}/api/passenger/profile/getFundingHistroy`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      const data = await response.json();
      console.log('Funding History:', JSON.stringify(data, null, 2));
  
      if (response.ok && data?.data?.data) {
        setFundingHistory(data.data.data); // Accessing data.data.data based on log
      } else {
        alert('Failed to fetch funding history');
      }
    } catch (error) {
      console.error('Error fetching funding history:', error);
      alert('Error fetching funding history');
    } finally {
      setHistoryLoading(false);
    }
  };
  
  
  useEffect(() => {
    fetchPaymentCards();
    fetchFundingHistory(); // Add this line
  }, []);


    const dispatch = useDispatch();
    
    const fetchProfile = async () => {
      try {
        const response = await fetch(`${BASE_URL}/api/passenger/profile/getProfile`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });
    
        const data = await response.json();
        console.log('Profile Response:', data?.data);
    
        if (response.ok) {
          dispatch(overwriteStore({ name: 'user', value: data?.data || {} }));
          setProfile(data?.data || {}); // Save user details in state
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };
    useEffect(() => {
      fetchProfile();
      fetchPaymentCards();
      fetchFundingHistory();
    }, [navigation]);

    useEffect(() => {}, [profile]);
  const funds = profile?.wallet ? new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  }).format(profile?.wallet) : "$0.00"
 


  // console.log('profile:', profile);

  const renderFundingItem = ({ item }) => (
    <View style={styles.fundingItem}>
      <Text style={styles.fundingDate}>
        {new Date(item.createdAt).toLocaleDateString()}
      </Text>
      <Text style={styles.fundingAmount}>${(item.amount).toFixed(2)}</Text>
      <Text style={[styles.fundingStatus, { color: item.status === 'Pending' ? 'orange' : 'green' }]}>
        {item.status}
      </Text>
    </View>
  );

  return (
      <>

    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={{paddingTop:30}}/>
      
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Payment</Text>
        <Text></Text>
      </View>

      {/* Funds Card */}
      <View style={styles.fundsCard}>
        <View>
          <Text style={styles.fundsLabel}>RIDEFUZE Funds</Text>
          <Text style={styles.fundsAmount}>{funds}</Text>
        </View>
        <TouchableOpacity style={styles.addFundsButton}   onPress={
          toggleBottomSheet}>
          <Text style={styles.addFundsText}>+ Add funds</Text>
        </TouchableOpacity>
      </View>



      {/* Saved Payment Methods */}
<Text style={styles.sectionTitle}>Saved Payment Cards</Text>
{loading ? (
  <ActivityIndicator size="small" color={COLORS.primary} />
) : (
  <FlatList
    data={savedCards}
    renderItem={renderSavedCard}
    keyExtractor={(item) => item._id}
    contentContainerStyle={styles.paymentMethodsList}
    ListEmptyComponent={
      <Text style={{ textAlign: 'center', color: COLORS.grey }}>No saved cards</Text>
    }
  />
)}


      {/* Add Payment Method Button */}
      <TouchableOpacity style={styles.addPaymentButton}  onPress={() => navigation.navigate('AddCard')}>
        <Text style={styles.addPaymentText}>+ Add payment method</Text>
      </TouchableOpacity>

 {/* Funding History */}
 <Text style={styles.sectionTitle}>Funding History</Text>
{historyLoading ? (
  <ActivityIndicator size="small" color={COLORS.primary} />
) : (
  <FlatList
    data={fundingHistory}
    renderItem={renderFundingItem}
    keyExtractor={(item) => item._id}
    contentContainerStyle={styles.fundingHistoryList}
    ListEmptyComponent={
      <Text style={{ textAlign: 'center', color: COLORS.grey }}>No funding history available</Text>
    }
  />
)}



      {/* Bottom Sheet */}


    </ScrollView>
    {/* Bottom Sheet */}
    {showBottomSheet && (
  <View style={styles.bottomSheetContainer}>
    <View style={styles.bottomSheet}>
      {/* Header */}
      <View style={styles.bottomSheetHeader}>
        <Text></Text>
        <Text style={styles.bottomSheetTitle}>Add Funds</Text>
        <TouchableOpacity onPress={toggleBottomSheet}>
          <Image source={images.cancel} style={styles.closeIcon} />
        </TouchableOpacity>
      </View>

      <Text style={styles.bottomSheetDescription}>
        How much do you want to add to your RideFuze funds?
      </Text>

      {/* Custom Amount Input */}
      <TextInput
        style={styles.input}
        placeholder="Enter your amount"
        placeholderTextColor={COLORS.grey}
        keyboardType="numeric"
        onChangeText={setSelectedAmount}
        value={selectedAmount}  // Optional: style for the placeholder text
      />

      {/* Amount Options */}
      {['250', '500', '750', '1000', '1500'].map((amount) => (
        <TouchableOpacity
          key={amount}
          style={[
            styles.amountOption,
            selectedAmount === amount && styles.amountOptionSelected,
          ]}
          onPress={() => setSelectedAmount(amount)}
        >
          <Text
            style={[
              styles.amountText,
              selectedAmount === amount && styles.amountTextSelected,
            ]}
          >
            ${amount}
          </Text>
          {selectedAmount === amount && (
            <View style={styles.checkmark}>
              <Text style={styles.checkmarkText}>✔</Text>
            </View>
          )}
        </TouchableOpacity>
      ))}

      {/* Add Funds Button */}
      <TouchableOpacity
        style={styles.addFundsButtonBottomSheet}
        onPress={() => {
          if (!selectedAmount) {
            alert('Please select an amount to fund');
            return;
          }
          toggleBottomSheet(); // Close Bottom Sheet
          handlePayPress();
        }}
      >
        <Text style={styles.addFundsText}>Fund Wallet</Text>
      </TouchableOpacity>
    </View>
  </View>
)}

    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // padding: SIZES.padding,
  },
  input: {
    height: 40,
    margin: 12,
    borderWidth: 1,
    padding: 10,
    borderColor: '#ddd',  // Light grey border
    borderRadius: 5,  // Rounded corners for aesthetics
  },
  fundingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: COLORS.white,
    marginHorizontal: 20,

    marginVertical: 4,
    borderRadius: 8,
  },
  fundingDate: {
    fontSize: 14,
    color: COLORS.black,
  },
  fundingAmount: {
    fontSize: 16,
    color: COLORS.primary,
  },
  fundingStatus: {
    fontSize: 14,
    color: COLORS.light_green,
  },
  
  bottomSheetContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
  },
  closeIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.black,
  },
  bottomSheetDescription: {
    ...FONTS.body3,
    fontSize: 13,
    marginHorizontal: 40,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
  },
  amountOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  amountText: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 4,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
  },
  addFundsButtonBottomSheet: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  addFundsText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  
  headerContainer: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    backgroundColor: COLORS.white,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
  fundsCard: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    // alignItems: 'center',
    backgroundColor: COLORS.white,
    padding: 15,
    marginHorizontal: 20,
    borderRadius: 7,
    marginBottom: 20,
  },
  fundsLabel: {
    ...FONTS.body3,
    color: COLORS.black,
    // marginBottom: 15,
  },
  fundsAmount: {
    ...FONTS.h2,
    color: COLORS.primary,
    marginVertical:25
  },
  addFundsButton: {
    backgroundColor: COLORS.primary,
    width: 130,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,

  
  },
  addFundsText: {
    ...FONTS.h4,
    color: COLORS.white,
  },
  sectionTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    marginHorizontal: 20,

    margin: 10,
  },
  paymentMethodsList: {
    marginVertical: 20,
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    marginHorizontal: 20,

  },
  paymentMethodDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentIcon: {
    width: 40,
    height: 30,
    resizeMode: 'contain',
    marginRight: 10,
  },
  paymentText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  arrowIcon: {
    width: 10,
    height: 10,
    resizeMode: 'contain',
  },
  addPaymentButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    marginHorizontal: 20,

    paddingVertical: 15,
    alignItems: 'center',
  },
  addPaymentText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default Payment;
