import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { BASE_URL } from '../../../Baseurl';

const Message = () => {
  const [search, setSearch] = useState('');
  const [messages, setMessages] = useState([]); // State to hold messages
  const [isLoading, setIsLoading] = useState(false); // State to handle loading

  const navigation = useNavigation();

  // Fetch messages from the API
  useEffect(() => {
    const fetchMessages = async () => {
      setIsLoading(true); // Start loading
      try {
        console.log("Attempting to fetch messages...");
        const response = await fetch(`${BASE_URL}/api/rideChat/getCustomerChat`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        const responseData = await response.json();
        console.log("API Response:", responseData);
        
        if (response.ok && responseData.success) {
          // Extract the rides array from the nested structure
          let extractedMessages = [];
          
          // Check if message.rides exists and is an array
          if (responseData.message && responseData.message.rides) {
            console.log("Found rides in response:", responseData.message.rides);
            
            // Handle array of rides
            if (Array.isArray(responseData.message.rides)) {
              // Map each ride to extract its chat messages
              extractedMessages = responseData.message.rides.flatMap(ride => {
                // If each ride has a 'messages' property that's an array, use that
                // Otherwise if the ride itself contains messages, use the ride object
                if (ride.messages && Array.isArray(ride.messages)) {
                  return ride.messages;
                } else if (ride.content) {
                  return [ride]; // The ride itself is a message
                }
                return []; // No messages found in this ride
              });
            } else {
              console.log("Rides is not an array, checking if it's a single object");
              // Handle single ride object
              const singleRide = responseData.message.rides;
              if (singleRide.messages && Array.isArray(singleRide.messages)) {
                extractedMessages = singleRide.messages;
              } else if (singleRide.content) {
                extractedMessages = [singleRide];
              }
            }
          }
          
          console.log("Extracted messages:", extractedMessages);
          console.log("Messages count:", extractedMessages.length);
          setMessages(extractedMessages);
        } else {
          console.error('Failed to fetch messages:', responseData);
          setMessages([]);
        }
      } catch (error) {
        console.error('Error fetching messages:', error);
        setMessages([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, []);

  const renderMessage = ({ item }) => {
    console.log("Rendering message item:", item);
    // Ensure content exists before trying to render it
    const messageContent = item && item.content ? item.content : "No content";
    
    return (
      <View style={styles.messageItem}>
        <Text style={styles.messageContent}>{messageContent}</Text>
        {item.timestamp && (
          <Text style={styles.messageTimestamp}>
            {new Date(item.timestamp).toLocaleString()}
          </Text>
        )}
      </View>
    );
  };

  // Safe filter function
  const filterMessages = () => {
    if (!Array.isArray(messages)) {
      console.log("Messages is not an array during filtering");
      return [];
    }
    
    const filtered = messages.filter(msg => 
      msg && msg.content && typeof msg.content === 'string' && 
      msg.content.toLowerCase().includes(search.toLowerCase())
    );
    
    console.log("Filtered messages count:", filtered.length);
    return filtered;
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 40 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />}
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton}/>
        </TouchableOpacity>
        <Text style={styles.title}>Message</Text>
        <Text></Text>
      </View>

 

      {/* Loading Indicator */}
      {isLoading ? (
        <ActivityIndicator size="large" color={COLORS.primary} />
      ) : (
        <FlatList
          data={filterMessages()}
          renderItem={renderMessage}
          keyExtractor={(item, index) => (item && item.id ? item.id.toString() : `msg-${index}`)}
          contentContainerStyle={styles.messageList}
          ListEmptyComponent={() => (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyMessage}>No messages found</Text>
              <Text style={styles.emptyDetails}>
                {search 
                  ? `No messages match your search: "${search}"` 
                  : 'You have no messages yet'}
              </Text>
            </View>
          )}
          ListHeaderComponent={
            <View style={styles.searchContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Search messages"
                placeholderTextColor={COLORS.grey}
                value={search}
                onChangeText={setSearch}
              />
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    padding:20,
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
  },

  backButton: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    ...FONTS.body3,
    color: COLORS.primary,
    marginRight: 10,
  },
  title: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  searchContainer: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    ...FONTS.body3,
    color: COLORS.black,
  },
  messageItem: {
    padding: SIZES.padding,
    borderBottomWidth: 1,
    borderColor: COLORS.light_grey,
    backgroundColor: COLORS.white,
    borderRadius: 8,
    marginBottom: 8,
  },
  messageContent: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  messageTimestamp: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginTop: 5,
    fontSize: 12,
    textAlign: 'right',
  },
  messageList: {
    padding: SIZES.padding,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyMessage: {
    ...FONTS.h4,
    color: COLORS.grey,
    marginBottom: 8,
  },
  emptyDetails: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
  },
});

export default Message;