 

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { BASE_URL } from '../../../Baseurl';

const NotificationScreen = () => {
  const navigation = useNavigation();
  const [notifications, setNotifications] = useState([]);
const [loading, setLoading] = useState(false);


const fetchNotifications = async () => {
  setLoading(true);
  try {
    const response = await fetch(
      `${BASE_URL}/api/passenger/profile/getNotifications`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    const data = await response.json();
    console.log('Passenger Notifications:', JSON.stringify(data, null, 2));

    if (response.ok && Array.isArray(data?.data)) {
      setNotifications(data.data);
    } else {
      alert('Failed to fetch notifications');
    }
  } catch (error) {
    console.error('Error fetching notifications:', error);
    alert('Error fetching notifications');
  } finally {
    setLoading(false);
  }
};

useEffect(() => {
  fetchNotifications();
}, []);


const renderNotification = ({ item }) => (
  <View style={styles.notificationCard}>
    <View style={styles.notificationHeader}>
      <Image source={images.notif} style={styles.notificationIcon} />
      <Text style={styles.notificationTitle}>{item.message}</Text>
    </View>
    <Text style={styles.notificationTimestamp}>
      {new Date(item.createdAt).toLocaleString()}
    </Text>
  </View>
);



  return (
    <View style={styles.container}>
      <View style={{paddingTop:0}}/>
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.goBackIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <Text></Text>
      </View>

      {/* Notification List */}
      <View style={{paddingHorizontal:20}}>

   
      <FlatList
  data={notifications}
  renderItem={renderNotification}
  keyExtractor={(item) => item._id?.toString() || Math.random().toString()}
  contentContainerStyle={styles.notificationList}
  showsVerticalScrollIndicator={false}
  ListEmptyComponent={
    <Text style={{ textAlign: 'center', color: COLORS.grey }}>
      No notifications available
    </Text>
  }
/>
</View>

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    paddingTop: 30,
  },
  header: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: "white",
    paddingVertical: 20,  
    paddingHorizontal: 20,
  },
  goBackIcon: {
    width: 25,
    height: 25,
    marginRight: 15,
  },
  headerTitle: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  notificationList: {
    paddingBottom: 20,
  },
  notificationCard: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  notificationIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  notificationTitle: {
    ...FONTS.body4,
    color: COLORS.black,
    marginRight:20

  },
  notificationDescription: {
    marginVertical: 10,
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
    marginBottom: 10,
  },
  notificationFooter: {
    borderColor: COLORS.border,
    borderTopWidth: 0.5,
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationTimestamp: {
    ...FONTS.body5,
    color: COLORS.grey,
    fontSize: 10,
  },
  viewAllText: {
    ...FONTS.body4,
    color: COLORS.grey,
    fontSize: 10,

  },
  promoCard: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    // flexDirection: 'row',
    // alignItems: 'center',
  },
  promoTitle: {
    ...FONTS.h3,
    color: COLORS.red,
    fontWeight: 'bold',
  },
  promoDescription: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
  },
  bookNowButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  bookNowText: {
    ...FONTS.body4,
    color: COLORS.white,
  },
  promoImage: {
    width: '100%',
    height: 150,
    resizeMode: 'contain',
    marginLeft: 10,
  },
});

export default NotificationScreen;
