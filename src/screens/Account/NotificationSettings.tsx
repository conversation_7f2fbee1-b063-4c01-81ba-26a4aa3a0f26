


import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { get } from 'react-native/Libraries/TurboModule/TurboModuleRegistry';
import { BASE_URL } from '../../../Baseurl';

const NotificationSettings = () => {


  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [userDetails, setUserDetails] = useState(null);

  const [pushNotification, setPushNotification] = useState(false);
  const [emailNotification, setEmailNotification] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailLoading, setIsEmailLoading] = useState(false);

  const subscribeToPushNotifications = async (isEnabled) => {
    if (!isEnabled) return; // Do nothing if notifications are turned off

    setIsLoading(true);
    try {
      let userData = userDetails;

      if (!userData) {
        const storedUserData = await AsyncStorage.getItem("UserProfile");
        userData = JSON.parse(storedUserData);
      }

      if (!userData) {
        alert("User data not found.");
        return;
      }

      // Retrieve the device token
      const deviceToken = await getDeviceToken();

      if (!deviceToken) {
        alert("Failed to retrieve device token.");
        return;
      }

      const requestBody = {
        email: userData?.email,
        accountId: userData?.passengerId,
        accountType: "passenger",
        data: {
          deviceToken: deviceToken, // Send device token
        },
      };

      console.log("Push Notification Subscription Request:", requestBody);

      const response = await fetch(
        `${BASE_URL}/api/pushNotification/saveSubscription`,
        {
          method: "POST",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      const data = await response.json();
      console.log("Push Notification Subscription Response:", data);

      if (response.ok) {
        Alert.alert(data?.data, "Subscribed to push notifications successfully!");
        setPushNotification(true);
      } else {
        alert("Subscription failed.");
      }
    } catch (error) {
      console.error("Error subscribing to push notifications:", error);
      alert("An error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  const subscribeToEmailNotifications = async (isEnabled) => {
    if (!isEnabled) return; 
   

    try {
      let userData = userDetails;

      if (!userData) {
        const storedUserData = await AsyncStorage.getItem("UserProfile");
        userData = JSON.parse(storedUserData);
      }
  
      if (!userData) {
        alert("User data not found.");
        return;
      }
  
      // Retrieve the device token
      const deviceToken = await getDeviceToken();
  
      if (!deviceToken) {
        alert("Failed to retrieve device token.");
        return;
      }
      const requestBody = {
        email: userData?.email,
        accountId: userData?.passengerId,
        accountType: "passenger",
        data: {
          deviceToken: deviceToken, // Send device token
        },
      };
      setIsLoading(true);
      console.log("Email Notification Subscription Request:", requestBody);

      const response = await fetch(
        `${BASE_URL}/api/pushNotification/subscribeEmail`,
        {
          method: "POST",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      const data = await response.json();
      console.log("Email Notification Subscription Response:", data);

      if (response.ok) {
       Alert.alert(data?.data, "Subscribed to email notifications successfully!");
        setEmailNotification(true);
      } else {
        alert("Email subscription failed.");
      }
    } catch (error) {
      console.error("Error subscribing to email notifications:", error);
      alert("An error occurred.");
    } finally {
      setIsLoading(false);
    }
  };


  const getDeviceToken = async () => {
    try {
      await messaging().requestPermission();  
      const token = await messaging().getToken();
      console.log("Device Token:", token);
      return token;
    } catch (error) {
      console.error("Error getting device token:", error);
      return null; // Return null in case of an error
    }
  };
  useEffect(() => {
    fetchUserSettings();
    getDeviceToken();
  }, []);

  const fetchUserSettings = async () => {
    try {
      const response = await fetch(`${BASE_URL}/api/passenger/profile/getProfile`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('Profile Settings Response:', data?.data);

      if (response.ok) {
        dispatch(overwriteStore({ name: 'UserProfile', value: data?.data || {} }));
        setUserDetails(data?.data || {}); // Save user details in state
        await AsyncStorage.setItem("UserProfile", JSON.stringify(data?.data || {})); // Persist in AsyncStorage

        setPushNotification(data?.data?.pushNotification);
        setEmailNotification(data?.data?.emailNotification);
      }
    } catch (error) {
      console.error('Error fetching profile settings:', error);
    }
  };




  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={{paddingTop:0}}/>
      
      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}

      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Notification</Text>
        <Text></Text>
      </View>

      {/* Notification Settings */}
      <View style={styles.settingRow}>
        <View style={styles.settingLabelContainer}>
          <Image source={images.noti1} style={styles.icon} />
          <Text style={styles.settingLabel}>Push Notification</Text>
        </View>
        <Switch
          style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
          value={pushNotification}
          onValueChange={(value) => {
            setPushNotification(value);
            subscribeToPushNotifications(value);
          }}
          trackColor={{ false: COLORS.white, true: COLORS.primary }}
          thumbColor={pushNotification ? COLORS.white : COLORS.white}
        />
      </View>

      <View style={styles.settingRow}>
        <View style={styles.settingLabelContainer}>
          <Image source={images.notificationtext} style={styles.icon} />
          <Text style={styles.settingLabel}>Email Notification</Text>
        </View>
        <Switch
          style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
          value={emailNotification}
          onValueChange={(value) => {
            setEmailNotification(value);
            subscribeToEmailNotifications(value);
          }}
          trackColor={{ false: COLORS.white, true: COLORS.primary }}
          thumbColor={emailNotification ? COLORS.white : COLORS.white}
        />

      </View>



    </View>
  );
};

const styles = StyleSheet.create({
  saveButton: {
    marginTop: 20,
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  saveButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    paddingTop: 30,
    // padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    backgroundColor: COLORS.white,
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  header: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    paddingHorizontal: 20,
  },
  settingLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  settingLabel: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },

});

export default NotificationSettings;
