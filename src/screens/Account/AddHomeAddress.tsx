import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import Spinner from 'react-native-loading-spinner-overlay';
import { useDispatch } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BASE_URL } from '../../../Baseurl';

const GOOGLE_API_KEY = 'AIzaSyC0VJu9ttMNPOWP-vxTuXtzAaR932hdKUc';

const AddHomeAddress = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState('');
  // const user = useSelector(state => state?.store?.user) || [];
  // console.log('User:', user);
  const handleInputChange = async (text) => {
    setQuery(text);
    if (text.length > 2) {
      setLoading(true);
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&types=(cities)&key=${GOOGLE_API_KEY}`
        );
        const data = await response.json();
        console.log('Google Places API Response:', data);

        if (data.status === 'OK') {
          const predictions = data.predictions.map((prediction) => ({
            id: prediction.place_id,
            description: prediction.description,
          }));
          setSuggestions(predictions);
        } else {
          console.error('Google Places API Error:', data.status);
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Google Places API Error:', error);
      } finally {
        setLoading(false);
      }
    } else {
      setSuggestions([]);
    }
  };

  const handleSelectAddress = (address) => {
    setSelectedAddress(address);
    setQuery(address);
    setSuggestions([]);
  };
    const [recentSearches, setRecentSearches] = useState([]);

  const [loadingSubmit, setLoadingSubmit] = useState(false);

  const handleSaveAddress = async () => {
    if (!selectedAddress) {
      alert('Please select a valid address.');
      return;
    }
  
    setLoadingSubmit(true); // Show loader
  
    try {
      const response = await fetch(`${BASE_URL}/api/passenger/profile/updateProfile`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ homeAddress: selectedAddress }),
      });
  
      const data = await response.json();

      console.log('Update Address Response:', data);
  
      if (response.ok) {
        alert('Address updated successfully!');
        dispatch(overwriteStore({ name: 'user', value: data?.message || [] }));
        navigation.goBack();
      } else {
        alert('Failed to update address.');
      }
    } catch (error) {
      console.error('Error updating address:', error);
    } finally {
      setLoadingSubmit(false); // Hide loader
    }
  };
  const loadRecentSearches = async () => {
    try {
      const locations = await AsyncStorage.getItem('recentLocations');
      console.log('Recent locations:', locations);
      
      if (locations) {
        setRecentSearches(JSON.parse(locations));
      }
    } catch (error) {
      console.error("Error loading recent searches from AsyncStorage:", error);
    }
  };

  useEffect(() => {
    loadRecentSearches();
  }, []);
  const handleSelectLocation = (address) => {
    console.log("Selected:", address);
    setSelectedAddress(address);
    setQuery(address?.description || '');
    setSuggestions([]);
   
  };


  
  return (
    <View style={{ flex: 1, backgroundColor: COLORS.light_blue }}>
      <View style={{paddingTop:60}}/>
      
 <Spinner visible={loadingSubmit} textContent={'Loading...'} textStyle={{ color: COLORS.white }} />
      {/* Header */}
      <View style={{ flexDirection: 'row', alignItems: 'center', padding: 10, backgroundColor: COLORS.light_blue }}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={{ width: 30, height: 30, marginRight: 10 }} />
        </TouchableOpacity>
        <Text style={{ ...FONTS.h3, fontSize: 14, color: COLORS.black }}>                         Add Home Address</Text>
      </View>

      <ScrollView contentContainerStyle={{ padding: SIZES.padding }}>
        {/* Address Input */}
        <View>
          <TextInput
            placeholder="Search for your home address"
            placeholderTextColor={COLORS.grey}
            style={{
              ...FONTS.body3,
              fontSize: 15,
              backgroundColor: COLORS.white,
              padding: 15,
              borderWidth: 0.5,
              borderColor: COLORS.border,
              borderRadius: 7,
              marginVertical: 5,
              color: COLORS.black,
            }}
            value={query}
            onChangeText={handleInputChange}
          />

          {loading && <ActivityIndicator size="small" color={COLORS.primary} style={{ marginTop: 10 }} />}

          {/* Address Suggestions */}
          <FlatList
            data={suggestions}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={{
                  padding: 10,
                  borderBottomWidth: 0.5,
                  borderColor: COLORS.border,
                  backgroundColor: COLORS.white,
                }}
                onPress={() => handleSelectAddress(item.description)}
              >
                <Text style={{ ...FONTS.body3, color: COLORS.black }}>{item.description}</Text>
              </TouchableOpacity>
            )}
          />
        </View>

        <TouchableOpacity
  style={{
    backgroundColor: loadingSubmit ? COLORS.grey : COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  }}
  onPress={handleSaveAddress}
 
>
  
    <Text style={{ ...FONTS.body3, color: COLORS.white }}>Save Address</Text>
 
</TouchableOpacity>
<Text style={styles.sectionTitle}>Recent Search</Text>
        
              {/* Location Suggestions */}
        {recentSearches.length > 0 &&
              <ScrollView style={styles.locationCardContainer}>
                {recentSearches.map((location, index) => (
                  <TouchableOpacity key={index} style={styles.locationCard}  onPress={() => handleSelectLocation(location)}>
                    <Image source={images.transcat} style={styles.locationIcon} />
                    <View style={styles.locationTextContainer}>
                      <Text style={styles.locationTitle}>{location.description}</Text>
                      <Text style={styles.locationSubtitle}>
                        Latitude: {location.location.lat}, Longitude: {location.location.lng}
                      </Text>
                    </View>
                    <Image source={images.next} style={styles.nextIcon} />
                  </TouchableOpacity>
                ))}
              </ScrollView>
              }
      </ScrollView>
    </View>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: COLORS.white,
  },
  backIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.h3,
    fontSize: 14,
    color: COLORS.black,
  },
  scrollContent: {
    padding: SIZES.padding,
  },
  rideTypeContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    marginBottom: 10,
  },
  rideTypeButton: {
    // flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: SIZES.radius,
    backgroundColor: COLORS.white,
    marginHorizontal: 5,
    width: 100,
  },
  activeButton: {
    backgroundColor: COLORS.primary,
    width: 100,

  },
  buttonText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginLeft: 5,
  },
  activeText: {
    color: COLORS.white,
  },
  icon: {
    width: 20,
    height: 20,
  },
  inputContainer: {
    // alignItems:'center',
    marginBottom: 20,
    flexDirection: 'row',
    // justifyContent: 'center',
  },
  inputRow: {
    // flexDirection: 'column',
    // alignItems: 'center',
    // marginBottom: 10,
    // borderRadius: SIZES.radius,
    // padding: 10,
  },
  routeIndicator: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
    marginTop: 20,
    marginLeft:-20
  },
  input: {
    ...FONTS.body3,
    fontSize: 13,
    backgroundColor: COLORS.white,
    padding: 15,
    width: 350,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: 7,
    marginVertical: 5,
    color: COLORS.black,
  },
  addIcon: {
    position: 'absolute',
    bottom: 40,
    left: 10,
    width: 30,
    height: 30,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  optionIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  optionText: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  sectionTitle: {
    ...FONTS.h4,
    color: COLORS.primary,
    fontSize: 14,
    marginVertical: 10,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    // backgroundColor: COLORS.white,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius: SIZES.radius,
    marginVertical: 5,
  },
  locationIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationTitle: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  locationSubtitle: {
    ...FONTS.body4,
    fontSize: 10,

    color: COLORS.grey,
  },
  nextIcon: {
    width: 10,
    height: 10,
  },
});

export default AddHomeAddress;
