 

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Image,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const LanguageScreen = () => {
  const navigation = useNavigation();
  const [selectedLanguage, setSelectedLanguage] = useState('English');

  const languages = ['Arabic', 'Russian', 'Mandarin', 'English'];

  const toggleLanguage = (language) => {
    setSelectedLanguage(language);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={{paddingTop:60}}/>
      
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Language</Text>
        <Text></Text>
      </View>

      {/* Language Options */}
      {languages.map((language) => (
        <View key={language} style={styles.languageRow}>
          <Text style={styles.languageText}>{language}</Text>
          <Switch
          style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
            value={selectedLanguage === language}
            onValueChange={() => toggleLanguage(language)}
            trackColor={{ false: COLORS.grey, true: COLORS.white }}
            thumbColor={selectedLanguage === language ? COLORS.primary : COLORS.white}
          />
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  header: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  languageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  languageText: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default LanguageScreen;
