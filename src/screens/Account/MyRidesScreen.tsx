import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';
import { BASE_URL } from '../../../Baseurl';

const MyRidesScreen = () => {
  const navigation = useNavigation();
  const [rides, setRides] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(null);
  const [profile, setProfile] = useState({});
   const user = useSelector(state => state?.store?.user) || {};
  console.log('user:', user);
  useEffect(() => {}, [user]);
  const funds = user?.wallet ? new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
  }).format(user.wallet) : "$0.00"
 
  // 🟢 Fetch Passenger Rides (Paginated)
  const fetchPassengerRides = async (page) => {
    if (loadingMore || (totalPages && page > totalPages)) return; // Prevent multiple API calls

    const isFirstLoad = page === 1;
    isFirstLoad ? setLoading(true) : setLoadingMore(true);

    try {
      const response = await fetch(
        `${BASE_URL}/api/rides/getPassengerRides?page=${page}`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await response.json();
      console.log(`Passenger Rides Page ${page} Response:`, JSON.stringify(data, null, 2));

      if (response.ok && data?.message?.rides) {
        setRides((prevRides) => [...prevRides, ...data.message.rides]); // Append new rides
        setTotalPages(data.message.totalPages);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error fetching passenger rides:', error);
    } finally {
      isFirstLoad ? setLoading(false) : setLoadingMore(false);
    }
  };
      
  // 🟣 Fetch First Page on Mount
  useEffect(() => {
    fetchPassengerRides(1);
  }, []);

 

  // 🟡 Load More Rides when Scrolling
  const loadMoreRides = () => {
    if (!loadingMore && currentPage < totalPages) {
      fetchPassengerRides(currentPage + 1);
    }
  };

  // 🔹 Format Wallet Amount
  const formattedFunds = funds.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  // 🔹 Renders Ride Item
  const renderRideItem = ({ item }) => (
    <View style={styles.rideItem}>
      <View>
        <Text style={styles.rideTitle}>{item.rideType.toUpperCase()}</Text>
        <Text style={styles.rideDate}>{new Date(item.createdAt).toLocaleString()}</Text>
        <Text style={styles.rideDate}>{item.from} → {item.to?.[0]?.place}</Text>
      </View>
      <View style={styles.rideStatusContainer}>
        <Text style={styles.rideAmount}>${item.charge || '0.00'}</Text>
        <Text
          style={[
            styles.rideStatus,
            item.status === 'Successful' && { color: 'green' },
            item.status === 'Canceled' && { color: COLORS.red },
            item.status === 'Pending' && { color: 'orange' },
          ]}
        >
          {item.status}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* 🟢 Full-Screen Loader for Initial Load */}
      <View style={{paddingTop:30}}/>
      
      {loading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>RIDEFUZE Funds</Text>
        <View style={{ width: 25 }} />
      </View>

      {/* Funds Card */}
      <View style={{paddingHorizontal:20}}>

     
      <View style={styles.fundsCard}>
        <View>
          <Text style={styles.fundsLabel}>RIDEFUZE Funds</Text>
          <Text style={styles.fundsValue}>{formattedFunds}</Text>
          <Text style={styles.fundsSubtitle}>Top balance</Text>
        </View>
        <TouchableOpacity>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>
      </View>

      {/* Recent Rides List */}
      {rides.length > 0 ? (
        <>
          <Text style={styles.sectionTitle}>Recent Rides</Text>
          <FlatList
            data={rides}
            renderItem={renderRideItem}
            keyExtractor={(item, index) => item.rideId || index.toString()}
            contentContainerStyle={styles.rideList}
            onEndReached={loadMoreRides} // Load more when reaching bottom
            onEndReachedThreshold={0.5} // Trigger when halfway through last item
            ListFooterComponent={loadingMore && <ActivityIndicator size="small" color={COLORS.primary} />}
          />
        </>
      ) : (
        // Empty State
        <View style={styles.emptyStateContainer}>
          <Text style={styles.emptyStateText}>No Recent Rides</Text>
          <Image source={images.emptyride} style={styles.emptyStateImage} />
          <TouchableOpacity style={styles.getRideButton}>
            <Text style={styles.getRideButtonText}>Get a ride</Text>
          </TouchableOpacity>
        </View>
      )}
 </View>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  fundsCard: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 20,
    elevation: 5,
    shadowColor: COLORS.grey,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

  },
  fundsLabel: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  fundsValue: {
    ...FONTS.h2,
    color: COLORS.primary,
    marginVertical: 15,
  },
  fundsSubtitle: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  nextIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
  },
  sectionTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
    marginVertical: 20,
  },
  rideList: {
    paddingBottom: 20,
  },
  rideItem: {
    flexDirection: 'row',
    width:'91%',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  rideTitle: {
    ...FONTS.body3,
    fontSize: 12,

    color: COLORS.black,
  },
  rideDate: {
    ...FONTS.body4,
    fontSize: 11,
    color: COLORS.grey,
  },
  rideAmount: {
    ...FONTS.body4,
    textAlign: 'right',
    color: COLORS.black,
  },
  rideStatus: {
    ...FONTS.body4,
    fontSize: 11,
  },
  emptyStateContainer: {
    flex: 1,
    marginTop: 50,
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  emptyStateImage: {
    width: 100,
    height: 100,
    marginBottom: 20,
    alignSelf: 'center',
    marginVertical: 30,
  },
  emptyStateText: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 20,
    textAlign: 'center',
  },
  getRideButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingVertical: 15,
    paddingHorizontal: 40,
    alignItems: 'center',
  },
  getRideButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default MyRidesScreen;
