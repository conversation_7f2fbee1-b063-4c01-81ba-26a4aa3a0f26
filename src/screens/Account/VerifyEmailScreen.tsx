import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';
import { BASE_URL } from '../../../Baseurl';

const VerifyEmailScreen = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false); // Loader state
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const handleUpdateEmail = async () => {
    if (!email.trim()) {
      alert('Please enter a valid email address.');
      return;
    }

    setLoading(true); // Show loader

    try {
      const response = await fetch(
        `${BASE_URL}/api/passenger/profile/updateProfile`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        }
      );

      const data = await response.json();
      console.log('Update Email Response:', data);

      if (response.ok) {
        alert('Email updated successfully!');
        dispatch(overwriteStore({ name: 'user', value: data?.message || {} }));
        navigation.goBack(); // Navigate back after success
      } else {
        alert('Failed to update email.');
      }
    } catch (error) {
      console.error('Error updating email:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setLoading(false); // Hide loader
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={{paddingTop:60}}/>
      
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Verify Email</Text>
        <Text></Text>
      </View>

      {/* Email Input */}
      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Email*</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your email"
          placeholderTextColor={COLORS.black}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
        />
      </View>

      {/* Info Text */}
      <Text style={styles.infoText}>
        An email was sent to this email, check your inbox
      </Text>
      <View style={styles.resendContainer}>
        <Text style={styles.infoText}>Didn’t receive an email?</Text>
        <TouchableOpacity onPress={handleUpdateEmail}>
          <Text style={styles.resendText}> Resend</Text>
        </TouchableOpacity>
      </View>

      {/* Verify Button */}
      <TouchableOpacity
        style={styles.saveButton}
        onPress={handleUpdateEmail}
        disabled={loading} // Disable button when loading
      >
        {loading ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.saveButtonText}>Verify Identity</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  saveButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  saveButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 5,
  },
  input: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: 15,
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
    borderColor: COLORS.border,
    borderWidth: 0.5,
  },
  infoText: {
    ...FONTS.body3,
    color: COLORS.grey,
    marginTop: 10,
  },
  resendContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  resendText: {
    ...FONTS.body3,
    color: COLORS.grey,
    marginTop: 10,
  },
});

export default VerifyEmailScreen;
