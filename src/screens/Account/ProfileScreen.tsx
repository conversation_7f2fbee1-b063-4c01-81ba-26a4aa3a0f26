import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ScrollView,
  Share,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';
import { BASE_URL } from '../../../Baseurl';


const ProfileScreen = () => {
  const user = useSelector(state => state?.store?.user) || {};

  console.log('🔍 ProfileScreen user data:', user);
  console.log('🔍 User data is empty:', Object.keys(user).length === 0);

  useEffect(() => {}, [user]);

// Destructure user details
const {
  profileImg,
  firstName,
  lastName,
  email,
} = user;



  const [profile, setProfile] = useState({ name: '', image: '' });

  const dispatch = useDispatch();
  
  const fetchProfile = async () => {
    try {
      const response = await fetch(`${BASE_URL}/api/passenger/profile/getProfile`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
  
      const data = await response.json();
      console.log('Profile Response:', data?.data);
  
      if (response.ok) {
        dispatch(overwriteStore({ name: 'GetProfile', value: data?.data || {} }));
        setProfile({
          firstName: data?.data?.firstName || '',
          lastName: data?.data?.lastName || '',
          profileImg: data?.data?.profileImg || images.profile,
          email: data?.data?.email || '',
          mobileNumber: data?.data?.mobileNumber || '',
          totalRides: data?.data?.totalRides || 0,
          status: data?.data?.status || '',
          earnings: data?.data?.earnings || 0,
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };
  useEffect(() => {
    fetchProfile();
  }, []);
  // const user = useSelector(state => state?.store?.user) || {};
  // console.log('user:', user);
  
  // Destructure user details
  // const {
  //   profileImg,
  //   firstName,
  //   lastName,
  // } = user;
  const navigation = useNavigation();
   

  const shareRide = async () => {
    try {
      const result = await Share.share({
        message: 'Hey! I am using RideFuze. Download the app now and enjoy your ride!',
      });
  
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Shared successfully');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error) {
      console.log('Error sharing:', error.message);
    }
  };
  const menuGroups = [
    [
      { id: 1, label: 'Payment', icon: images.creditcard, onPress: ()=> navigation.navigate('Payment'), line: true },
      { id: 2, label: 'My Rides', icon: images.carr, onPress: () => navigation.navigate('MyRidesScreen'), line: true },
      { id: 3, label: 'Notifications', icon: images.noti2, onPress: () => navigation.navigate('NotificationScreen'), hasNotification: true },
    ],
    [
      { id: 4, label: 'Help', icon: images.help, onPress: () => navigation.navigate('FAQ'), line: true },
      { id: 5, label: 'Settings', icon: images.setting, onPress: () => navigation.navigate('NotificationSettings'), line: true },
      { id: 6, label: 'About us', icon: images.annotation, onPress: () => navigation.navigate('AboutUs') },
    ],
    [
      { id: 7, label: 'Messages', icon: images.chat, onPress: () => navigation.navigate('Message'), hasNotification: true, line: true },
      { id: 8, label: 'Tell a friend', icon: images.tellafriend, onPress: () =>  shareRide() },
    ],
  ];
  
  const renderMenuItem = (item) => (
    <TouchableOpacity style={[styles.menuItem, item?.line && { borderBottomWidth: 0.5 }]} onPress={item.onPress} key={item.id}>
      <View style={styles.menuItemContent}>
        <Image source={item.icon} style={styles.menuIcon} />
        <Text style={styles.menuLabel}>{item.label}</Text>
      </View>
      <Image source={images.next} style={[styles.menuIcon, { width: 12, height: 12, resizeMode: 'contain' }]} />
    </TouchableOpacity>
  );

  const renderMenuGroup = (group, index) => (
    <View style={styles.menuGroup} key={index}>
      {group.map(renderMenuItem)}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
    
        {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />} 
      
      <Text style={styles.header}>Profile</Text>
{/* Profile Card */}
<View style={styles.profileCard}>
 
      <Image 
         source={profileImg ? { uri: profileImg } : images.profile} 
         style={styles.profileImage} 
       />
  <View style={styles.profileDetails}>
    <Text style={styles.profileName}>{firstName} {lastName}</Text>
    <TouchableOpacity onPress={() => navigation.navigate('ViewProfileScreen')}>
      <Text style={styles.viewProfileText}>View profile</Text>
    </TouchableOpacity>
  </View>
</View>

      {/* Menu Items */}
      {menuGroups.map(renderMenuGroup)}

      {/* Footer */}
      <TouchableOpacity style={styles.footerCard} onPress={() => console.log('Become a driver')}>
       
        <Image source={images.becomeadriver} style={styles.footerImage} />
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 20,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 7,
    padding: 10,
    marginBottom: 20,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 15,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  viewProfileText: {
    ...FONTS.body4,
    color: COLORS.primary,
  },
  menuGroup: {
    backgroundColor: COLORS.white,
    borderRadius: 5,
    paddingHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 10,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 0,
    borderColor:COLORS.border,
    paddingVertical:13
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    width: 20,
    height: 20,
    marginRight: 15,
  },
  menuLabel: {
    ...FONTS.body3,
    fontSize:14,
    color: COLORS.black,
  },
  notificationDot: {
    width: 10,
    height: 10,
    backgroundColor: COLORS.red,
    borderRadius: 5,
  },
  // footerCard: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: COLORS.primary,
  //   borderRadius: SIZES.radius,
  //   padding: SIZES.padding,
  // },
  footerTitle: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  footerSubtitle: {
    ...FONTS.body4,
    color: COLORS.white,
  },
  footerImage: {
    width: '100%',
    height: 120,
    borderRadius: 5,
    resizeMode: 'contain',
    // marginLeft: 10,
  },
});

export default ProfileScreen;
