import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, ActivityIndicator, Platform } from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { overwriteStore } from '../../../redux/ActionCreator';
import { useDispatch } from 'react-redux';
import BackToHome from '../../components/Backbtn';
import { BASE_URL } from '../../../Baseurl';

const AboutUs = () => {
  const navigation = useNavigation();
  const [aboutContent, setAboutContent] = useState('');
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();

  useEffect(() => {
    fetchAboutContent();
  }, []);

  const fetchAboutContent = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${BASE_URL}/api/appSettings/getAbout`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const contentType = response.headers.get("content-type");

      if (contentType && contentType.includes("application/json")) {
        const data = await response.json();
        console.log('About Us Response:', JSON.stringify(data));

        if (response.ok) {
          setAboutContent(data?.message || '');
          dispatch(overwriteStore({ name: 'GetAboutContent', value: data?.message || '' }));
        }
      } else {
        const textResponse = await response.text();
        console.log('About Us Response (RAW TEXT):', textResponse);
      }
    } catch (error) {
      console.error('Error fetching About Us content:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
   {Platform.OS === 'ios' && <View style={{ marginTop: 30 }} />}     
       {Platform.OS === 'android' && <View style={{ marginTop: 30 }} />} 
      
      <View style={styles.headerContainer}>

        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>About us</Text>
        <Text></Text>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color={COLORS.primary} style={styles.loader} />
      ) : (
        <ScrollView contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.contentContainer}>
            <Text style={styles.paragraph}>{aboutContent?.about}</Text>
          </View>
        </ScrollView>
      )}
    </View>
  );
};

export default AboutUs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    justifyContent: 'space-between',
    backgroundColor: COLORS.white,
    padding:30
  },
  backButton: {
    width: 25,
    height: 25,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  paragraph: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 15,
    lineHeight: 20,
    // textAlign: 'justify',
  },
  bulletedList: {
    marginBottom: 10,
  },
  bulletItem: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
    lineHeight: 22,
  },
});
