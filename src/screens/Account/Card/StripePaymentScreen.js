import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  StyleSheet,InteractionManager
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
    CardField,
  useStripe,
} from '@stripe/stripe-react-native';
import { COLORS } from '../../../constants/theme';
import { BASE_URL } from '../../../../Baseurl';

const StripePaymentScreen = ({ route }) => {
  // ✅ Use Stripe Hook Properly (NO DUPLICATES)
  const { presentPaymentSheet, initPaymentSheet } = useStripe();
  const navigation = useNavigation();
  const { selectedAmount } = route.params;

  // 🟠 Loader State
  const [funding, setFunding] = useState(false);

  // 🟢 STEP 1: Create Payment Intent from Backend
  const fetchPaymentIntentClientSecret = async () => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/passenger/profile/fundwallet`, 
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount: selectedAmount * 1, // Amount in cents
            currency: 'usd',
          }),
        }
      );

      const data = await response.json();
      console.log('Payment Intent Response:', data);

      // ✅ Correct extraction of clientSecret
      const clientSecret = data?.data || data?.data;
      if (!clientSecret) {
        Alert.alert('Error', 'No client secret returned from backend');
        return null;
      }

      return clientSecret;
    } catch (error) {
      console.error('Error fetching payment intent:', error);
      Alert.alert('Error', 'Failed to create payment intent');
      return null;
    }
  };
 
// const handlePayPress = async () => {
//     try {
//       setFunding(true);
  
//       // 🟢 STEP 1: Get Client Secret
//       const clientSecret = await fetchPaymentIntentClientSecret();
//       if (!clientSecret) {
//         setFunding(false);
//         Alert.alert('Error', 'Could not retrieve payment intent');
//         return;
//       }
  
//       // 🟡 STEP 2: Initialize Stripe Payment Sheet
//       const { error: initError } = await initPaymentSheet({
//         paymentIntentClientSecret: clientSecret,
//         merchantDisplayName: 'InRide Wallet',
//         allowsDelayedPaymentMethods: true,
//       });
  
//       if (initError) {
//         setFunding(false);
//         console.error('PaymentSheet Init Error:', initError.message);
//         Alert.alert('Error', `Initialization failed: ${initError.message}`);
//         return;
//       }
  
//       // 🟠 STEP 3: Present Stripe Payment Sheet (AWAIT FULLY)
//       const { error, paymentOption } = await presentPaymentSheet();
  
//       if (error) {
//         setFunding(false);
//         console.error('PaymentSheet Error:', error.message);
//         Alert.alert('Payment Failed', error.message);
//         return;
//       }
  
//       // ✅ STEP 4: Handle Success
//       Alert.alert('Success', 'Wallet funded successfully!');
  
//       // 🚀 STEP 5: Delay Navigation to Prevent App Crash
//       setTimeout(() => {
//         if (navigation && navigation.replace) {
//           navigation.replace('Payment'); // Use replace for stability
//         }
//       }, 500); // 500ms delay to ensure Stripe fully closes
//     } catch (error) {
//       console.error('Unexpected Payment Error:', error);
//       Alert.alert('Error', 'An unexpected error occurred during payment.');
//     } finally {
//       setFunding(false); // Stop loader
//     }
//   };
const handlePayPress = async () => {
    try {
      setFunding(true);
  
      // 🟡 STEP 1: Fetch Client Secret
      const clientSecret = await fetchPaymentIntentClientSecret();
      if (!clientSecret) {
        setFunding(false);
        Alert.alert('Error', 'Could not retrieve payment intent');
        return;
      }
  
      // 🟠 STEP 2: Initialize Payment Sheet
      const { error: initError } = await initPaymentSheet({
        paymentIntentClientSecret: clientSecret,
        merchantDisplayName: 'InRide Wallet',
        allowsDelayedPaymentMethods: true,
      });
  
      if (initError) {
        setFunding(false);
        console.error('PaymentSheet Init Error:', initError.message);
        Alert.alert('Error', `Initialization failed: ${initError.message}`);
        return;
      }
  
      // 🟢 STEP 3: Present Payment Sheet (Wait for completion)
      const { error } = await presentPaymentSheet();
  
      if (error) {
        setFunding(false);
        console.error('PaymentSheet Error:', error.message);
        Alert.alert('Payment Failed', error.message);
        return;
      }
  
      // ✅ STEP 4: Show Success Alert (WAIT using InteractionManager)
      Alert.alert('Success', 'Wallet funded successfully!', [
        {
          text: 'OK',
          onPress: () => {
            InteractionManager.runAfterInteractions(() => {
              setTimeout(() => {
                navigation.replace('Payment');
              }, 300);
            });
          },
        },
      ]);
    } catch (error) {
      console.error('Unexpected Payment Error:', error);
      Alert.alert('Error', 'An unexpected error occurred during payment.');
    } finally {
      setFunding(false); // Stop loader
    }
  };  


  return (
    <ScrollView style={styles.container}>
<View style={{paddingTop:60}}/>

      <Text style={styles.title}>Fund Wallet</Text>
      <Text style={styles.amountText}>Amount: ${selectedAmount}</Text>

      {/* 🟡 STANDARD STRIPE CARD INPUT */}
      <CardField
  postalCodeEnabled={false}
  placeholder={{ number: '4242 4242 4242 4242' }}
  cardStyle={styles.card}
  style={styles.cardContainer}
  onFocus={() => console.log('CardField Focused')}
  onBlur={() => console.log('CardField Blurred')}
  onCardChange={(cardDetails) => {
    console.log('Card Details:', cardDetails);
  }}
/>

      {/* 🟠 PAY BUTTON */}
      <TouchableOpacity
        style={styles.payButton}
        onPress={handlePayPress}
        disabled={funding}
      >
        {funding ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.payButtonText}>Pay ${selectedAmount}</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: COLORS.light_blue,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  input: {
    height: 45,
    borderColor: COLORS.gray,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    fontSize: 16,
  },
  cardContainer: {
    height: 50,
    marginVertical: 20,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
  },
  payButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  payButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default StripePaymentScreen;
