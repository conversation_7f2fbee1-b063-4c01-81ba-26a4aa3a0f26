import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  ScrollView,
  FlatList,
  Modal,
} from 'react-native';

import { COLORS, FONTS, SIZES } from '../../../constants/theme';
import { images } from '../../../constants';
import { useNavigation } from '@react-navigation/native';
import { BASE_URL } from '../../../../Baseurl';

const AddCard = () => {
  const navigation = useNavigation();
  // const [cardType, setCardType] = useState("");
  const [modalVisible, setModalVisible] = useState(false);

  const cardTypes = ["Visa", "Mastercard", "American Express", "Discover"];

  // State for Card Form
  const [cardNumber, setCardNumber] = useState('');
  const [cardHolderName, setCardHolderName] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [cardType, setCardType] = useState('');
  const [loading, setLoading] = useState(false);

  // Function to Validate Form
  const validateForm = () => {
    if (!cardNumber || !cardHolderName || !expiryDate || !cvv || !cardType) {
      Alert.alert('Error', 'All fields are required');
      return false;
    }
    if (cardNumber.length !== 16) {
      Alert.alert('Error', 'Card number must be 16 digits');
      return false;
    }
    if (!/^\d{2}\/\d{2}$/.test(expiryDate)) {
      Alert.alert('Error', 'Expiry date must be in MM/YY format');
      return false;
    }
    if (cvv.length !== 3) {
      Alert.alert('Error', 'CVV must be 3 digits');
      return false;
    }
    return true;
  };

  // Function to Submit Card Details
  const handleAddCard = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      console.log('boby, ', JSON.stringify({
        cardNumber,
        cardHolderName,
        expiryDate,
        cvv,
        cardType,
      }),);

      const response = await fetch(
        `${BASE_URL}/api/passenger/card/newCardDetails`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            cardNumber,
            cardHolderName,
            expiryDate,
            cvv,
            cardType,
          }),
        }
      );


      const data = await response.json();
      console.log('Add Card Response:', JSON.stringify(data, null, 2));

      if (response.ok) {
        Alert.alert('Success', 'Card added successfully');

        navigation.navigate('Payment');
      } else {
        Alert.alert('Error', data?.message || 'Failed to add card');
      }
    } catch (error) {
      console.error('Error adding card:', error);
      Alert.alert('Error', 'An error occurred while adding the card');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={{paddingTop:60}}/>
      
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Add Card</Text>
        <Text></Text>
      </View>

      {/* Card Form */}
      <View style={styles.formContainer}>
        <Text style={styles.label}>Card Number</Text>
        <TextInput
          style={styles.input}
          placeholder="1234 5678 9101 1121"
          placeholderTextColor={COLORS.grey}
          keyboardType="numeric"
          maxLength={16}
          value={cardNumber}
          onChangeText={setCardNumber}
        />

        <Text style={styles.label}>Card Holder Name</Text>
        <TextInput
          style={styles.input}
          placeholder="John Doe"
          placeholderTextColor={COLORS.grey}
          value={cardHolderName}
          onChangeText={setCardHolderName}
        />

        <Text style={styles.label}>Expiry Date (MM/YY)</Text>
        <TextInput
          style={styles.input}
          placeholder="MM/YY"
          placeholderTextColor={COLORS.grey}
          keyboardType="numeric"
          maxLength={5}
          value={expiryDate}
          onChangeText={setExpiryDate}
        />

        <Text style={styles.label}>CVV</Text>
        <TextInput
          style={styles.input}
          placeholder="123"
          placeholderTextColor={COLORS.grey}
          keyboardType="numeric"
          secureTextEntry={true}
          maxLength={3}
          value={cvv}
          onChangeText={setCvv}
        />

        {/* <Text style={styles.label}>Card Type</Text>
        <TextInput
          style={styles.input}
          placeholder="Visa, Mastercard, etc."
          placeholderTextColor={COLORS.grey}
          value={cardType}
          onChangeText={setCardType}
        /> */}

      
          <Text style={styles.label}>Card Type</Text>

          {/* Dropdown Button */}
          <TouchableOpacity
            style={styles.input}
            onPress={() => setModalVisible(true)}
          >
            <Text style={styles.inputText}>
              {cardType || "Select Card Type"}
            </Text>
          </TouchableOpacity>

          {/* Modal for Dropdown */}
          <Modal
            visible={modalVisible}
            transparent
            animationType="slide"
            onRequestClose={() => setModalVisible(false)}
          >
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <FlatList
                  data={cardTypes}
                  keyExtractor={(item) => item}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => {
                        setCardType(item);
                        setModalVisible(false);
                      }}
                    >
                      <Text style={styles.optionText}>{item}</Text>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </View>
          </Modal>
        </View>

    


      {/* Submit Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={handleAddCard}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color={COLORS.white} />
        ) : (
          <Text style={styles.addButtonText}>Add Card</Text>
        )}
      </TouchableOpacity>
    
    </ScrollView >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
  formContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
  label: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginVertical: 10,
  },
  input: {
    height: 45,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    color: COLORS.black,
  },
  addButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
    fontWeight: 'bold',
  },
   
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    height: 50,
    justifyContent: "center",
    paddingHorizontal: 10,
    backgroundColor: "#fff",
  },
  inputText: {
    fontSize: 16,
    color: "#666",
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderRadius: 8,
    width: "80%",
    paddingVertical: 10,
  },
  option: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
    alignItems: "center",
  },
  optionText: {
    fontSize: 16,
    color: "#333",
  },
});

export default AddCard;
