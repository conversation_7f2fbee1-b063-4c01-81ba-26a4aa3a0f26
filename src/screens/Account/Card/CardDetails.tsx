import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, SIZES } from '../../../constants/theme';
import { BASE_URL } from '../../../../Baseurl';

const CardDetails = ({ route }) => {
  const { cardId } = route.params;
  const navigation = useNavigation();
console.log('cardId',cardId);

  // State Management
  const [cardDetails, setCardDetails] = useState({
    bankId:cardId,
    cardNumber: '',
    cardHolderName: '',
    expiryDate: '',
    cvv: '',
    cardType: '',
  });

  const [isCardTypeModalVisible, setIsCardTypeModalVisible] = useState(false);

  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [isEditable, setIsEditable] = useState(false);

 
 
  
  // Store bankId from fetched data (But don't display)
  const fetchCardDetails = async () => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/passenger/card/getCardDetail/${cardId}`,
        {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          }
      );
      const data = await response.json();
      console.log('Fetched Card Details:', JSON.stringify(data, null, 2));
  
      if (response.ok && data?.data) {
        setCardDetails({
          bankId: data?.data?.bankId, // Store bankId internally
          cardNumber: data?.data?.cardNumber,
          cardHolderName: data?.data?.cardHolderName,
          expiryDate: data?.data?.expiryDate,
          cvv: data?.data?.cvv,
          cardType: data?.data?.cardType,
        });
      } else {
        Alert.alert('Error', 'Failed to fetch card details');
      }
    } catch (error) {
      console.error('Error fetching card details:', error);
      Alert.alert('Error', 'Failed to load card details');
    }
  };
  
 
// 🟡 Update Card Details (Using Stored bankId)
const updateCardDetails = async () => {
    // Validate Required Fields (except bankId, which is hidden)
    if (!cardDetails.cardNumber || cardDetails.cardNumber.length !== 16) {
      Alert.alert('Error', 'Valid card number is required (16 digits)');
      return;
    }
    if (!/^\d{2}\/\d{2}$/.test(cardDetails.expiryDate)) {
      Alert.alert('Error', 'Expiry date must be in MM/YY format');
      return;
    }
    if (!cardDetails.cvv || cardDetails.cvv.length !== 3) {
      Alert.alert('Error', 'Valid CVV is required (3 digits)');
      return;
    }
  
    setUpdating(true);
    try {
      const response = await fetch(
        `${BASE_URL}/api/passenger/card/updateCardDetails`,
        
        {
            
                method: 'POST',
                credentials: 'include',
                headers: {
                  'Content-Type': 'application/json',
                },
              
          body: JSON.stringify({
            cardId, // Required Card ID
            bankId: cardDetails.bankId, // Hidden from user
            cardNumber: cardDetails.cardNumber,
            cardHolderName: cardDetails.cardHolderName,
            expiryDate: cardDetails.expiryDate,
            cvv: cardDetails.cvv,
            cardType: cardDetails.cardType,
          }),
        }
      );
      const data = await response.json();
      console.log('Update Card Response:', JSON.stringify(data, null, 2));
  
      if (response.ok) {
        Alert.alert('Success', 'Card details updated successfully');
        navigation.navigate('Payment');
        setIsEditable(false); // Switch back to view mode
      } else {
        Alert.alert('Error', data?.message || 'Failed to update card details');
      }
    } catch (error) {
      console.error('Error updating card details:', error);
      Alert.alert('Error', 'Failed to update card details');
    } finally {
      setUpdating(false);
    }
  };
  
// 🗑️ Delete Card Function
const deleteCard = async () => {
    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete this card?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          onPress: async () => {
            setDeleting(true);
            try {
              const response = await fetch(
                `${BASE_URL}/api/passenger/card/deleteCardDetails`,
                {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({cardId}),
                }
              );
              const data = await response.json();
              console.log('Delete Card Response:', JSON.stringify(data, null, 2));
  
              if (response.ok) {
                Alert.alert('Success', 'Card deleted successfully');
                navigation.navigate('Payment');  
              } else {
                Alert.alert('Error', data?.message || 'Failed to delete card');
              }
            } catch (error) {
              console.error('Error deleting card:', error);
              Alert.alert('Error', 'Failed to delete card');
            } finally {
              setDeleting(false);
            }
          },
        },
      ]
    );
  };
  
  // Load Card Details on Mount
  useEffect(() => {
    fetchCardDetails();
  }, []);

  // Handler for Input Changes
  const handleInputChange = (field, value) => {
    setCardDetails((prev) => ({ ...prev, [field]: value }));
  };

  // Render Loader
  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Loading Card Details...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={{paddingTop:60}}/>
      
      <Text style={styles.title}>Card Details</Text>

      {/* Card Form */}
      <View style={styles.formContainer}>
        <Text style={styles.label}>Card Number</Text>
        <TextInput
          style={styles.input}
          placeholder="1234 5678 9101 1121"
          placeholderTextColor={COLORS.grey}
          keyboardType="numeric"
          maxLength={16}
          editable={isEditable}
          value={cardDetails.cardNumber}
          onChangeText={(text) => handleInputChange('cardNumber', text)}
        />

        <Text style={styles.label}>Card Holder Name</Text>
        <TextInput
          style={styles.input}
          placeholder="John Doe"
          placeholderTextColor={COLORS.grey}
          editable={isEditable}
          value={cardDetails.cardHolderName}
          onChangeText={(text) => handleInputChange('cardHolderName', text)}
        />

        <Text style={styles.label}>Expiry Date (MM/YY)</Text>
        <TextInput
          style={styles.input}
          placeholder="MM/YY"
          placeholderTextColor={COLORS.grey}
          keyboardType="numeric"
          maxLength={5}
          editable={isEditable}
          value={cardDetails.expiryDate}
          onChangeText={(text) => handleInputChange('expiryDate', text)}
        />

        <Text style={styles.label}>CVV</Text>
        <TextInput
          style={styles.input}
          placeholder="123"
          placeholderTextColor={COLORS.grey}
          keyboardType="numeric"
          maxLength={50}
          editable={isEditable}
        //   secureTextEntry={!isEditable}
          value={cardDetails.cvv}
          onChangeText={(text) => handleInputChange('cvv', text)}
        />

        <Text style={styles.label}>Card Type</Text>
        {/* <TextInput
          style={styles.input}
          placeholder="Visa, Mastercard"
          placeholderTextColor={COLORS.gray}
          editable={isEditable}
          value={cardDetails.cardType}
          onChangeText={(text) => handleInputChange('cardType', text)}
        /> */}
<TouchableOpacity
  style={styles.input}
  onPress={() => setIsCardTypeModalVisible(true)}
  disabled={!isEditable} // Only editable in edit mode
>
  <Text style={styles.inputText}>
    {cardDetails.cardType || "Select Card Type"}
  </Text>
</TouchableOpacity>
      </View>

      <Modal
  visible={isCardTypeModalVisible}
  transparent
  animationType="slide"
  onRequestClose={() => setIsCardTypeModalVisible(false)}
>
  <View style={styles.modalContainer}>
    <View style={styles.modalContent}>
      {["Visa", "Mastercard", "American Express", "Discover"].map((type) => (
        <TouchableOpacity
          key={type}
          style={styles.option}
          onPress={() => {
            handleInputChange("cardType", type);
            setIsCardTypeModalVisible(false);
          }}
        >
          <Text style={styles.optionText}>{type}</Text>
        </TouchableOpacity>
      ))}
    </View>
  </View>
</Modal>


      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        {isEditable ? (
          <TouchableOpacity
            style={[styles.button, styles.updateButton]}
            onPress={updateCardDetails}
            disabled={updating}
          >
            {updating ? (
              <ActivityIndicator size="small" color={COLORS.white} />
            ) : (
              <Text style={styles.buttonText}>Update Details</Text>
            )}
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.button, styles.editButton]}
            onPress={() => setIsEditable(true)}
          >
            <Text style={styles.buttonText}>Edit</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.button, styles.deleteButton]}
          onPress={deleteCard}
          disabled={deleting}
        >
          {deleting ? (
            <ActivityIndicator size="small" color={COLORS.white} />
          ) : (
            <Text style={styles.buttonText}>Delete</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: COLORS.light_blue,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    height: 50,
    alignItems: 'center',
    justifyContent: "center",
    paddingHorizontal: 10,
    backgroundColor: "#fff",
  },
  inputText: {
    fontSize: 16,
    justifyContent: 'center',
    color: "#666",
    paddingTop: 10,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderRadius: 8,
    width: "80%",
    paddingVertical: 10,
  },
  option: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
    alignItems: "center",
  },
  optionText: {
    fontSize: 16,
    color: "#333",
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    ...FONTS.body3,
    color: COLORS.grey,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
    color: COLORS.black,
  },
  formContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    // fontWeight: 'bold',
    color: COLORS.gray,
    marginBottom: 5,
  },
  input: {
    height: 45,
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    fontSize: 14,
    color: COLORS.black,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  editButton: {
    backgroundColor: COLORS.primary,
  },
  updateButton: {
    backgroundColor: COLORS.secondary,
  },
  deleteButton: {
    backgroundColor: COLORS.red,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.white,
  },
});

export default CardDetails;
