import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../../constants/theme';
import { images } from '../../../constants';
import { useNavigation } from '@react-navigation/native';

const CardList = () => {
  const navigation = useNavigation();
  
  
  return (
    <View style={styles.container}>
      <View style={{paddingTop:60}}/>
      
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Card List</Text>
        <Text></Text>
      </View>
 
 

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
 
  
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
})

export default CardList;
