 

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  FlatList,
} from 'react-native';
import { COLORS, FONTS, images, icons } from '../../constants';
import MapView, { Circle, Marker } from 'react-native-maps';

const PayOutMethods = ({ navigation }) => {
  // Example data for payout methods
  const payoutMethods = [
    {
      id: '1',
      accountNumber: '**********',
      accountName: 'Abdussamad Opabode',
      bankName: 'StanChart',
      bankLogo: images.stanChart,
    },
    {
      id: '2',
      accountNumber: '**********',
      accountName: 'Soliat Olabiran',
      bankName: 'Access',
      bankLogo: images.access,
    },
    {
      id: '3',
      accountNumber: '**********',
      accountName: 'Soliat Olabiran',
      bankName: 'Wells Fargo',
      bankLogo: images.wells,
    },
  ];

  const renderPayoutItem = ({ item }) => (
    <TouchableOpacity style={styles.payoutItem} onPress={() => navigation.navigate('PayOutHistory')}>
      <View style={styles.payoutDetails}>
        <Text style={styles.accountNumber}>{item.accountNumber}</Text>
        <Text style={styles.accountName}>{item.accountName}</Text>
      </View>
      <View style={styles.bankDetails}>
        <Image source={item.bankLogo} style={styles.bankLogo} />
        <Text style={styles.bankName}>{item.bankName}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
<View style={{paddingTop:60}}/>

       {/* Map Section */}
       <View style={styles.mapContainer}>
            <MapView
              style={styles.map}
              initialRegion={{
                latitude: 33.5186,
                longitude: -86.8104,
                latitudeDelta: 0.05,
                longitudeDelta: 0.05,
              }}
            >
              {/* Circle */}
              <Circle
                center={{ latitude: 33.5186, longitude: -86.8104 }}
                radius={3000}
                strokeColor={COLORS.primary}
                fillColor="rgba(0, 123, 255, 0.2)"
              />
              {/* Example Marker */}
              <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
                <Image source={images.CarSingle} style={styles.markerImage} />
              </Marker>
            </MapView>
            {/* Overlay Amount */}
            <View style={styles.earningsOverlay}>
              <Text style={styles.earningsText}>$ 5000</Text>
            </View>
          </View>

          <View style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 20,marginTop:20 }}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image
            source={icons.BackIcon}
            style={{ width: 18, height: 18, resizeMode: 'contain', marginRight: 100 }}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payout Methods</Text>
      </View>
      

      {/* Payout List */}
      <FlatList
        data={payoutMethods}
        renderItem={renderPayoutItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.payoutList}
        showsVerticalScrollIndicator={false}
      />

      {/* Add New Bank Button */}
      <TouchableOpacity style={styles.addBankButton} onPress={() => navigation.navigate('AddNewBank')}>
        <Text style={styles.addBankText}>Add new bank →</Text>
      </TouchableOpacity>
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  mapContainer: {
    height: width * 0.7,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  earningsText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  markerImage: {
    width: 30,
    height: 30,
  },
  earningsOverlay: {
    position: 'absolute',
    top: 10,
    left: width * 0.4,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
  },
  backButton: {
    marginTop: 10,
  },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.dark_blue,
    textAlign: 'center',
    marginVertical: 10,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
 
  backIcon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
 
  payoutList: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  payoutItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  payoutDetails: {
    flex: 1,
  },
  accountNumber: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  accountName: {
    ...FONTS.body4,
    fontSize: 11,
    color: COLORS.black,
    // marginTop: 5,
  },
  bankDetails: {
    alignItems: 'flex-end',
  },
  bankLogo: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
    marginBottom: 5,
  },
  bankName: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.black,
  },
  addBankButton: {
    backgroundColor: COLORS.primary,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 7,
    alignItems: 'center',
    paddingVertical: 15,
  },
  addBankText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default PayOutMethods;
