import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  FlatList,
} from 'react-native';
import MapView, { Circle, Marker } from 'react-native-maps';
import { COLORS, FONTS, images } from '../../constants';

const RecentRides = ({ navigation }) => {
  // Example data for recent rides
  const recentRides = [
    {
      id: '1',
      title: 'Delivery to California',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$200',
      status: 'Successful',
      statusColor: 'green',
    },
    {
      id: '2',
      title: 'Ride to Washington DC',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$20',
      status: 'Canceled',
      statusColor: COLORS.red,
    },
    {
      id: '3',
      title: 'Group ride to Washington DC',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$20',
      status: 'Pending',
      statusColor: COLORS.orange,
    },
    {
      id: '21',
      title: 'Ride to Washington DC',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$20',
      status: 'Canceled',
      statusColor: COLORS.red,
    },
    {
      id: '31',
      title: 'Group ride to Washington DC',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$20',
      status: 'Pending',
      statusColor: COLORS.orange,
    },
  ];

  const renderRide = ({ item }) => (
    <TouchableOpacity onPress={()=>navigation.navigate('RideDetails')} style={styles.rideItem}>
      <View>
        <Text style={styles.rideTitle}>{item.title}</Text>
        <Text style={styles.rideDate}>{item.date}</Text>
      </View>
      <View style={styles.rideDetails}>
        <Text style={styles.rideAmount}>{item.amount}</Text>
        <Text style={[styles.rideStatus, { color: item.statusColor }]}>
          {item.status}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Map Section */}
      <View style={{paddingTop:60}}/>
      
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: 33.5186,
            longitude: -86.8104,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          }}
        >
          {/* Circle */}
          <Circle
            center={{ latitude: 33.5186, longitude: -86.8104 }}
            radius={3000}
            strokeColor={COLORS.primary}
            fillColor="rgba(0, 123, 255, 0.2)"
          />
          {/* Example Marker */}
          <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
            <Image
              source={images.CarSingle}
              style={styles.markerImage}
            />
          </Marker>
        </MapView>
        {/* Overlay Amount */}
        <View style={styles.earningsOverlay}>
          <Text style={styles.earningsText}>$ 5000</Text>
        </View>
      </View>

      {/* Earnings Section */}
      <View style={styles.earningsSection}>
        <Text style={styles.sectionTitle}>Earnings</Text>
        <Text style={styles.earningsPeriod}>Mar 11 - Mar 17</Text>
        <Text style={styles.earningsAmount}>$458.88</Text>
        <Text style={styles.ridesInfo}>23 rides over the last 5 days</Text>

        {/* Cash Out Button */}
        <TouchableOpacity style={styles.cashOutButton} onPress={() => navigation.navigate('CashOut')}>
          <Text style={styles.cashOutText}>Cash out $458.88</Text>
        </TouchableOpacity>

        <Text style={styles.lastWeekInfo}>
          Last weekly payout was <Text style={styles.boldText}>$458.88</Text> on Mar 12 (25 rides)
        </Text>
        <TouchableOpacity
          onPress={() => navigation.navigate('WeeklyBreakDown')}
        >
          <Text style={styles.breakdownLink}>See weekly breakdown →</Text>
        </TouchableOpacity>
      </View>

      {/* Recent Rides Section */}
      <View style={styles.recentRidesSection}>
        <Text style={styles.recentRidesTitle}>Recent Rides</Text>
        {recentRides.length > 0 ? (
          <FlatList
          showsVerticalScrollIndicator={false}
            data={recentRides}
            renderItem={renderRide}
            keyExtractor={(item) => item.id}
            style={styles.ridesList}
          />
        ) : (
          <View style={styles.noRidesContainer}>
            <Text style={styles.noRidesMessage}>You have no rides this week</Text>
            <TouchableOpacity style={styles.getPassengerButton}>
              <Text style={styles.getPassengerText}>Get a passenger →</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  lastWeekInfo:{
...FONTS.body3,
fontSize: 12,
textAlign: 'center',
  },
  breakdownLink:{
    ...FONTS.body3,
    fontSize: 12,
    textAlign: 'center',
  },
  boldText:{
fontWeight: 'bold',
...FONTS.body3,
fontSize: 12,
textAlign: 'center',
  },
  mapContainer: {
    height: width * 0.7,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerImage: {
    width: 30,
    height: 30,
  },
  earningsOverlay: {
    position: 'absolute',
    top: 10,
    left: width * 0.4,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
  },
  earningsText: {
    color: COLORS.white,
    ...FONTS.h2,
  },
  earningsSection: {
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    marginHorizontal: 20,
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: COLORS.white,
    marginTop: -20,
  },
  sectionTitle: {
    ...FONTS.h2,
    fontSize: 15,
    color: COLORS.dark_blue,
    alignSelf: 'center',
  },
  earningsPeriod: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 13,
    alignSelf: 'center',
    marginVertical: 5,
  },
  earningsAmount: {
    ...FONTS.h1,
    fontWeight: '800',
    color: COLORS.primary,
    alignSelf: 'center',
    marginVertical: 10,
  },
  ridesInfo: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 12,
    alignSelf: 'center',
  },
  cashOutButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    borderRadius: 7,
    alignItems: 'center',
    marginVertical: 15,
    marginHorizontal: 30,
  },
  cashOutText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  recentRidesSection: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  recentRidesTitle: {
    ...FONTS.h3,
    fontSize: 15,
    color: COLORS.dark_blue,
    marginBottom: 10,
  },
  ridesList: {
    maxHeight: 200, // Adjust based on screen
  },
  rideItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  rideTitle: {
    ...FONTS.body3,
    fontSize:13,
    color: COLORS.dark_blue,
  },
  rideDate: {
    ...FONTS.body4,
    fontSize:11,

    color: COLORS.grey,
  },
  rideDetails: {
    alignItems: 'flex-end',
  },
  rideAmount: {
    ...FONTS.h3,
    fontSize:13,

    color: COLORS.dark_blue,
  },
  rideStatus: {
    ...FONTS.body4,
    fontSize:11,

  },
  noRidesContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  noRidesMessage: {
    ...FONTS.h4,
    color: COLORS.black,
    marginBottom: 10,
  },
  getPassengerButton: {
    color: COLORS.black,
    marginTop: 0,
  },
  getPassengerText: {
    ...FONTS.body3,
    fontSize: 12,
    color: COLORS.primary,
  },
});

export default RecentRides;
