 

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Dimensions,
  Image,
  Platform,
} from 'react-native';
import MapView, { Circle, Marker } from 'react-native-maps';
import { COLORS, FONTS, images, icons } from '../../constants';

const AddNewBank = ({ navigation }) => {
  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
       {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}
      
      {/* Map Section */}
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: 33.5186,
            longitude: -86.8104,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          }}
        >
          {/* Circle */}
          <Circle
            center={{ latitude: 33.5186, longitude: -86.8104 }}
            radius={3000}
            strokeColor={COLORS.primary}
            fillColor="rgba(0, 123, 255, 0.2)"
          />
          {/* Marker */}
          <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
            <Image source={images.CarSingle} style={styles.markerImage} />
          </Marker>
        </MapView>
        {/* Overlay Amount */}
        <View style={styles.earningsOverlay}>
          <Text style={styles.earningsText}>$ 5000</Text>
        </View>
      </View>

      {/* Header Section */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image source={icons.BackIcon} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Cash out</Text>
      </View>
      <View style={styles.earningsDetails}>
        <Text style={styles.dateText}>Mar 11 - Mar 17</Text>
        <Text style={styles.earningsAmount}>$458.88</Text>
        <Text style={styles.ridesText}>23 rides over the last 5 days</Text>
      </View>

      {/* Form Section */}
      <View style={styles.formContainer}>
        <Text style={styles.formTitle}>Add New Bank</Text>
        <Text style={styles.formSubtitle}>Input the details of the new category</Text>
        <Text style={styles.inputLabel}>Bank Name *</Text>
        
        <TextInput 
        style={styles.input} 
        placeholder="Bank of America" 
        placeholderTextColor={COLORS.grey} />

<Text style={styles.inputLabel}>Account Name *</Text>

        <TextInput
          style={styles.input}
          placeholder="Peter Parker"
          placeholderTextColor={COLORS.grey}
        />

<Text style={styles.inputLabel}>Account No *</Text>

        <TextInput
          style={styles.input}
          placeholder="***********"
          keyboardType="number-pad"
          placeholderTextColor={COLORS.grey}
        />
       
      </View>

      <TouchableOpacity style={styles.continueButton} onPress={() => navigation.navigate('PayOutMethods')}>
          <Text style={styles.continueButtonText}>Continue with this new account →</Text>
        </TouchableOpacity>
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  mapContainer: {
    height: width * 0.4,
    position: 'relative',
  },
  inputLabel:{
...FONTS.body3,
fontSize:13,
marginBottom:5,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerImage: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  earningsOverlay: {
    position: 'absolute',
    top: 10,
    left: width * 0.4,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
  },
  earningsText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  backButton: {
    marginRight: 10,
  },
  backIcon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.black,
    marginLeft:110
  },
  earningsDetails: {
    alignItems: 'center',
    marginTop: 10,
  },
  dateText: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 12,
  },
  earningsAmount: {
    ...FONTS.h1,
    color: COLORS.primary,
    marginVertical: 5,
    fontWeight: '800',
  },
  ridesText: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 12,
  },
  formContainer: {
    backgroundColor: COLORS.light_blue,
    borderRadius: 10,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 20,
  },
  formTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    marginBottom: 5,
    textAlign: 'center',
    fontSize:14
  },
  formSubtitle: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 15,
    textAlign: 'center',

  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 5,
    padding: 10,
    backgroundColor: COLORS.white,
    marginBottom: 20,
    color: COLORS.black,
    ...FONTS.body4,
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    paddingVertical: 10,
    alignItems: 'center',
    margin:20
  },
  continueButtonText: {
    ...FONTS.h3,
    fontSize:14,
    color: COLORS.white,
  },
});

export default AddNewBank;
