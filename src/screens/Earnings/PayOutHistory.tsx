// import React from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   TouchableOpacity,
//   FlatList,
//   Dimensions,
//   Image,
// } from 'react-native';
// import { COLORS, FONTS, icons, images } from '../../constants';
// import MapView, { Circle, Marker } from 'react-native-maps';

// const PayOutHistory = ({ navigation }) => {
//   // Example payout history data
//   const payoutHistory = [
//     {
//       id: '1',
//       card: '****1307',
//       date: 'Nov 14th, 2024 21:42:39',
//       amount: '$200',
//       status: 'Successful',
//       statusColor: 'green',
//     },
//     {
//       id: '2',
//       card: '****1307',
//       date: 'Nov 14th, 2024 21:42:39',
//       amount: '$20',
//       status: 'Canceled',
//       statusColor: COLORS.red,
//     },
//     {
//       id: '3',
//       card: '****1307',
//       date: 'Nov 14th, 2024 21:42:39',
//       amount: '$20',
//       status: 'Pending',
//       statusColor: COLORS.orange,
//     },
//     {
//       id: '4',
//       card: '****1307',
//       date: 'Nov 14th, 2024 21:42:39',
//       amount: '$20',
//       status: 'Canceled',
//       statusColor: COLORS.red,
//     },
//   ];

//   const renderPayoutItem = ({ item }) => (
//     <View style={styles.payoutItem}>
//       <View>
//         <Text style={styles.cardNumber}>{item.card}</Text>
//         <Text style={styles.payoutDate}>{item.date}</Text>
//       </View>
//       <View style={styles.payoutDetails}>
//         <Text style={styles.payoutAmount}>{item.amount}</Text>
//         <Text style={[styles.payoutStatus, { color: item.statusColor }]}>
//           {item.status}
//         </Text>
//       </View>
//     </View>
//   );

//   return (
//     <View style={styles.container}>
//       {/* Header */}
//        {/* Map Section */}
//        <View style={styles.mapContainer}>
//         <MapView
//           style={styles.map}
//           initialRegion={{
//             latitude: 33.5186,
//             longitude: -86.8104,
//             latitudeDelta: 0.05,
//             longitudeDelta: 0.05,
//           }}
//         >
//           {/* Circle */}
//           <Circle
//             center={{ latitude: 33.5186, longitude: -86.8104 }}
//             radius={3000}
//             strokeColor={COLORS.primary}
//             fillColor="rgba(0, 123, 255, 0.2)"
//           />
//           {/* Example Marker */}
//           <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
//             <Image
//               source={images.CarSingle}
//               style={styles.markerImage}
//             />
//           </Marker>
//         </MapView>
//         {/* Overlay Amount */}
//         <View style={styles.earningsOverlay}>
//           <Text style={styles.earningsText}>$ 5000</Text>
//         </View>
//       </View>

//       <View style={{flexDirection:'row',alignItems:'center',marginLeft:20}}>
//       <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
//       <Image
//             source={icons.BackIcon}
//             style={{ width: 18, height: 18,resizeMode:'contain',marginRight:100
//              }}/>
//       </TouchableOpacity>
//       <Text style={styles.headerTitle}>Payout History</Text>
//       </View>
//       {/* Payout List */}
//       <FlatList
//         data={payoutHistory}
//         renderItem={renderPayoutItem}
//         keyExtractor={(item) => item.id}
//         contentContainerStyle={styles.payoutList}
//         showsVerticalScrollIndicator={false}
//       />

//       {/* Total Payout */}
//       <View style={styles.totalPayout}>
//         <Text style={styles.totalPayoutText}>Total payout</Text>
//         <Text style={styles.totalPayoutAmount}>$458.88</Text>
//       </View>
//     </View>
//   );
// };

// const { width } = Dimensions.get('window');

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: COLORS.white,
//   },
//   mapContainer: {
//     height: width * 0.7,
//     position: 'relative',
//   },
//   map: {
//     ...StyleSheet.absoluteFillObject,
//   },
//   earningsText:{
// color:COLORS.white,
// ...FONTS.h3
//   },
//   markerImage: {
//     width: 30,
//     height: 30,
//   },
//   earningsOverlay: {
//     // paddingHorizontal: 20,
//     position: 'absolute',
//     top: 10,
//     left: width * 0.4,
//     backgroundColor: COLORS.primary,
//     borderRadius: 20,
//     padding: 10,
//     alignItems: 'center',
//   },
//   backButton: {
//     marginTop: 10,
//   },
//   backText: {
//     fontSize: 18,
//     color: COLORS.primary,
//   },
//   headerTitle: {
//     ...FONTS.h2,
//     fontSize: 16,
//     color: COLORS.dark_blue,
//     textAlign: 'center',
//     marginVertical: 10,
//   },
//   payoutList: {
//     paddingVertical: 20,
//   },
//   payoutItem: {
//     paddingHorizontal: 20,
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     marginBottom: 15,
//     paddingBottom: 10,
//     borderBottomWidth: 0.5,
//     borderBottomColor: COLORS.border,
//   },
//   cardNumber: {
//     ...FONTS.body3,
//     color: COLORS.dark_blue,
//     fontSize: 14,
//   },
//   payoutDate: {
//     ...FONTS.body4,
//     color: COLORS.grey,
//     fontSize: 12,
//   },
//   payoutDetails: {
//     alignItems: 'flex-end',
//   },
//   payoutAmount: {
//     ...FONTS.body3,
//     color: COLORS.dark_blue,
//     fontSize: 14,
//   },
//   payoutStatus: {
//     ...FONTS.body4,
//     fontSize: 12,
//   },
//   totalPayout: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     paddingHorizontal: 20,
//     borderTopWidth: 0.5,
//     borderTopColor: COLORS.border,
//     paddingTop: 10,
//     marginTop: 10,
//     alignItems: 'center',
//   },
//   totalPayoutText: {
//     ...FONTS.body4,
//     color: COLORS.black,
//     fontSize: 14,
//   },
//   totalPayoutAmount: {
//     ...FONTS.body3,
//     color: COLORS.primary,
//     fontSize: 16,
//     fontWeight: '800',
//     marginTop: 5,
//   },
// });

// export default PayOutHistory;


import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
  Image,
} from 'react-native';
import { COLORS, FONTS, icons, images } from '../../constants';
import MapView, { Circle, Marker } from 'react-native-maps';

const PayOutHistory = ({ navigation }) => {
  // Example payout history data
  // const payoutHistory = []; // Empty state for demonstration
  // Example payout history data
  const payoutHistory = [
    {
      id: '1',
      card: '****1307',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$200',
      status: 'Successful',
      statusColor: 'green',
    },
    {
      id: '2',
      card: '****1307',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$20',
      status: 'Canceled',
      statusColor: COLORS.red,
    },
    {
      id: '3',
      card: '****1307',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$20',
      status: 'Pending',
      statusColor: COLORS.orange,
    },
    {
      id: '4',
      card: '****1307',
      date: 'Nov 14th, 2024 21:42:39',
      amount: '$20',
      status: 'Canceled',
      statusColor: COLORS.red,
    },
  ];

  const renderPayoutItem = ({ item }) => (
    <View style={styles.payoutItem}>
      <View>
        <Text style={styles.cardNumber}>{item.card}</Text>
        <Text style={styles.payoutDate}>{item.date}</Text>
      </View>
      <View style={styles.payoutDetails}>
        <Text style={styles.payoutAmount}>{item.amount}</Text>
        <Text style={[styles.payoutStatus, { color: item.statusColor }]}>
          {item.status}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={{paddingTop:60}}/>
      

     
          {/* Map Section */}
          <View style={styles.mapContainer}>
            <MapView
              style={styles.map}
              initialRegion={{
                latitude: 33.5186,
                longitude: -86.8104,
                latitudeDelta: 0.05,
                longitudeDelta: 0.05,
              }}
            >
              {/* Circle */}
              <Circle
                center={{ latitude: 33.5186, longitude: -86.8104 }}
                radius={3000}
                strokeColor={COLORS.primary}
                fillColor="rgba(0, 123, 255, 0.2)"
              />
              {/* Example Marker */}
              <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
                <Image source={images.CarSingle} style={styles.markerImage} />
              </Marker>
            </MapView>
            {/* Overlay Amount */}
            <View style={styles.earningsOverlay}>
              <Text style={styles.earningsText}>$ 5000</Text>
            </View>
          </View>

          <View style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 20 }}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image
            source={icons.BackIcon}
            style={{ width: 18, height: 18, resizeMode: 'contain', marginRight: 100 }}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payout History</Text>
      </View>

      {payoutHistory.length > 0 ? (
        <>
          {/* Payout List */}
          <FlatList
            data={payoutHistory}
            renderItem={renderPayoutItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.payoutList}
            showsVerticalScrollIndicator={false}
          />

          {/* Total Payout */}
          <View style={styles.totalPayout}>
            <Text style={styles.totalPayoutText}>Total payout</Text>
            <Text style={styles.totalPayoutAmount}>$458.88</Text>
          </View>
        </>
      ) : (<>
        <Image source={icons.empty} style={styles.emptyImage} />

        <View style={styles.emptyStateContainer}>
          <Text style={styles.emptyTitle}>You want a payout?</Text>
          <TouchableOpacity style={styles.getPassengerButton}>
            <Text style={styles.getPassengerText}>Get a passenger →</Text>
          </TouchableOpacity>
        </View>
        </>
      )}
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  mapContainer: {
    height: width * 0.7,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  earningsText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  markerImage: {
    width: 30,
    height: 30,
  },
  earningsOverlay: {
    position: 'absolute',
    top: 10,
    left: width * 0.4,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
  },
  backButton: {
    marginTop: 10,
  },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.dark_blue,
    textAlign: 'center',
    marginVertical: 10,
  },
  payoutList: {
    paddingVertical: 20,
  },
  payoutItem: {
    marginHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  cardNumber: {
    ...FONTS.body3,
    color: COLORS.dark_blue,
    fontSize: 14,
  },
  payoutDate: {
    ...FONTS.body4,
    color: COLORS.grey,
    fontSize: 12,
  },
  payoutDetails: {
    alignItems: 'flex-end',
  },
  payoutAmount: {
    ...FONTS.body3,
    color: COLORS.dark_blue,
    fontSize: 14,
  },
  payoutStatus: {
    ...FONTS.body4,
    fontSize: 12,
  },
  totalPayout: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    borderTopWidth: 0.5,
    borderTopColor: COLORS.border,
    paddingTop: 10,
    marginTop: 10,
    alignItems: 'center',
  },
  totalPayoutText: {
    ...FONTS.body4,
    color: COLORS.black,
    fontSize: 14,
  },
  totalPayoutAmount: {
    ...FONTS.body3,
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: '800',
    marginTop: 5,
  },
  emptyStateContainer: {
    // flex: 1,
    marginTop: 20,
    marginHorizontal: 20,
    borderColor: COLORS.border,
    justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderWidth: 1,
  },
  emptyImage: {
    alignSelf: 'center',
    width: 150,
    height: 150,
    resizeMode: 'contain',
    marginTop: 20,
  },
  emptyTitle: {
    ...FONTS.h3,
    color: COLORS.dark_blue,
    marginBottom: 10,
    // textAlign: 'center',
  },
  getPassengerButton: {
    // marginTop: 10,
  },
  getPassengerText: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default PayOutHistory;
