 
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Image,
  Platform,
} from 'react-native';
import { COLORS, FONTS, icons } from '../../constants';

const CashOut = ({ navigation }) => {
  const [currentWeek, setCurrentWeek] = useState(0); // Week index
  const [weeklyRides, setWeeklyRides] = useState([
    // Example weekly data
    {
      week: 'Mar 11 - Mar 17',
      earnings: '$458.88',
      rides: [
        {
          id: '1',
          title: 'Delivery to California',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$200',
          status: 'Successful',
          statusColor: 'green',
        },
        {
          id: '2',
          title: 'Ride to Washington DC',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$20',
          status: 'Canceled',
          statusColor: COLORS.red,
        },
        {
          id: '12',
          title: 'Delivery to California',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$200',
          status: 'Successful',
          statusColor: 'green',
        },
        {
          id: '22w',
          title: 'Ride to Washington DC',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$20',
          status: 'Canceled',
          statusColor: COLORS.red,
        }, {
          id: '2s',
          title: 'Ride to Washington DC',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$20',
          status: 'Canceled',
          statusColor: COLORS.red,
        },
        {
          id: '2n',
          title: 'Ride to Washington DC',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$20',
          status: 'Canceled',
          statusColor: COLORS.red,
        },
        {
          id: '1m2',
          title: 'Delivery to California',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$200',
          status: 'Successful',
          statusColor: 'green',
        },
        {
          id: '2m2',
          title: 'Ride to Washington DC',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$20',
          status: 'Canceled',
          statusColor: COLORS.red,
        },
        {
          id: '11m2',
          title: 'Delivery to California',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$200',
          status: 'Successful',
          statusColor: 'green',
        },
        {
          id: '2s2',
          title: 'Ride to Washington DC',
          date: 'Nov 14th, 2024 21:42:39',
          amount: '$20',
          status: 'Canceled',
          statusColor: COLORS.red,
        },
      ],
    },
    {
      week: 'Mar 4 - Mar 10',
      earnings: '$0',
      rides: [],
    },
  ]);

  const navigateWeek = (direction) => {
    const newWeek = currentWeek + direction;
    if (newWeek >= 0 && newWeek < weeklyRides.length) {
      setCurrentWeek(newWeek);
    }
  };

  const renderRide = ({ item }) => (
    <TouchableOpacity style={styles.rideItem} onPress={()=>navigation.navigate('RideDetails')}>
      <View>
        <Text style={styles.rideTitle}>{item.title}</Text>
        <Text style={styles.rideDate}>{item.date}</Text>
      </View>
      <View style={styles.rideDetails}>
        <Text style={styles.rideAmount}>{item.amount}</Text>
        <Text style={[styles.rideStatus, { color: item.statusColor }]}>
          {item.status}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const currentData = weeklyRides[currentWeek];

  return (
    <View style={styles.container}>
      {/* Header Section */}
   {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
   {Platform.OS === 'android' && <View style={{ marginTop: 60 }} />}


                <View style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 40 }}>
              <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                <Image
                  source={icons.BackIcon}
                  style={{ width: 18, height: 18, resizeMode: 'contain', marginRight: 100 }}
                />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Cash Out</Text>
            </View>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigateWeek(-1)}>
          <Image
            source={icons.LeftIcon}
            style={{ width: 40, height: 40,marginLeft:50
             }}/>
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}></Text>
          <Text style={styles.weekText}>{currentData.week}</Text>
          <Text style={styles.earningsText}>{currentData.earnings}</Text>
          <Text style={styles.ridesInfo}>
            {currentData.rides.length} rides over the last 5 days
          </Text>
        </View>
        <TouchableOpacity onPress={() => navigateWeek(1)}>
        <Image
            source={icons.RightIcon}
            style={{ width: 40, height: 40 ,marginRight:50}}/>
        </TouchableOpacity>
      </View>

      {/* Earnings Summary */}
      <View style={styles.summary}>
        <Text style={styles.summaryTitle}>Earnings Summary</Text>
        {currentData.rides.length > 0 ? (
          <FlatList
            data={currentData.rides}
            renderItem={renderRide}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.noRidesContainer}>
            <Text style={styles.noRidesMessage}>No rides this week</Text>
            <TouchableOpacity style={styles.getPassengerButton}>
              <Text style={styles.getPassengerText}>Get a passenger →</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Total Earnings */}
      <View style={styles.totalEarningss}>
      <View style={styles.totalEarnings}>
        <Text style={styles.totalEarningsText}>Total Earnings</Text>
        <Text style={styles.totalEarningsAmount}>{currentData.earnings}</Text>
      </View>

      <TouchableOpacity onPress={()=>navigation.navigate('PayOutMethods')} style={styles.totalEarnings}>
        <Text style={styles.totalEarningsText}>Payment Method </Text>
        <Text style={styles.totalEarningsAmountt}>Access bank,oo75....</Text>
      </TouchableOpacity>

      {/* Payout History */}
      <TouchableOpacity style={styles.payoutHistory} onPress={() => navigation.navigate('PayOutHistory')}>
        <Text style={styles.payoutHistoryText}>Continue →</Text>
      </TouchableOpacity>
    </View>
    </View>
  );
};

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.grey,
  },
  navigationButton: {
    fontSize: 24,
    color: COLORS.primary,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.dark_blue,
  },
  weekText: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 12,
  },
  earningsText: {
    ...FONTS.h1,
    fontWeight: '800',
    color: COLORS.primary,
    marginVertical: 5,
  },
  ridesInfo: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  summary: {
    padding: 20,
  },
  summaryTitle: {
    ...FONTS.h3,
    color: COLORS.dark_blue,
    marginBottom: 10,
  },
  rideItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  rideTitle: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  rideDate: {
    ...FONTS.body4,
    fontSize: 11,
    color: COLORS.grey,
  },
  rideDetails: {
    alignItems: 'flex-end',
  },
  rideAmount: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.dark_blue,
  },
  rideStatus: {
    ...FONTS.body4,
    fontSize: 11,
  },
  noRidesContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  noRidesMessage: {
    ...FONTS.h4,
    color: COLORS.black,
    marginBottom: 10,
  },
  getPassengerButton: {
    marginTop: 10,
  },
  getPassengerText: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
  totalEarnings: {
    paddingHorizontal: 20,
    paddingVertical: 10,
   
    flexDirection: 'row',
    justifyContent: 'space-between',
    
  },  totalEarningss: {
    backgroundColor: COLORS.white,
    paddingHorizontal: 0,
    width: width,
    paddingVertical: 10,
    borderTopWidth: 0.5,
    borderTopColor: COLORS.grey,
    justifyContent: 'space-between',
    position: 'absolute',
    bottom: 0,
  },
totalEarningsText: {
    ...FONTS.h4,
    color: COLORS.black,
  },
  totalEarningsAmount: {
    ...FONTS.h3,
    fontWeight: '800',
    color: COLORS.primary,
  },
  totalEarningsAmountt: {
    ...FONTS.body5,
    color: COLORS.black,
  },
  payoutHistory: {
    backgroundColor:COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginVertical: 5,
    marginHorizontal: 20,
    borderRadius: 10,
    color: COLORS.black,

  },
  payoutHistoryText: {
    ...FONTS.h3,
    color: COLORS.white,
textAlign:'center'
  },
});

export default CashOut;
