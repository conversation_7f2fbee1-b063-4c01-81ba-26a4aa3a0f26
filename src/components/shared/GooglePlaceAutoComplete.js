 
import React, { useState } from "react";
import {
  View,
  TextInput,
  ActivityIndicator,
  FlatList,
  TouchableOpacity,
  Text,
  StyleSheet,
} from "react-native";
import { COLORS } from "../../constants";  
import AsyncStorage from '@react-native-async-storage/async-storage';

const GOOGLE_API_KEY = "AIzaSyC0VJu9ttMNPOWP-vxTuXtzAaR932hdKUc";  

const GooglePlacesAutocompleteComponent = ({ placeholder,onFocus, onSelect , value, onChangeText}) => {
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);

  const saveLocationToAsyncStorage = async (newLocation) => {
    try {
      const existingLocations = await AsyncStorage.getItem('recentLocations');
      let locations = existingLocations ? JSON.parse(existingLocations) : [];
      // Add the new location at the start of the array
      locations = [newLocation, ...locations];
      // Remove duplicates based on place_id
      locations = locations.filter((v, i, a) => a.findIndex(t => (t.place_id === v.place_id)) === i);
      // Keep only the last five items
      locations = locations.slice(0, 5);
      await AsyncStorage.setItem('recentLocations', JSON.stringify(locations));
    } catch (error) {
      console.error("Error saving location to AsyncStorage:", error);
    }
  };
  /** 📍 Fetch Place Predictions */
  const handleInputChange = async (text) => {
    onChangeText(text);  // Update input based on user typing
    console.log(`📍 Fetch Place Predictions ${text}`);
    if (text.length > 2) {
        setLoading(true);
        const url = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(text)}&key=${GOOGLE_API_KEY}`;
        console.log("Making request to:", url);
        try {
            const response = await fetch(url);
            const data = await response.json();
            console.log("🔍 Google Places API Response:", JSON.stringify(data, null, 2));

            if (data.status === "OK") {
                const predictions = data.predictions.map(prediction => ({
                    id: prediction.place_id,
                    description: prediction.description,
                    place_id: prediction.place_id,
                }));
                setSuggestions(predictions);
            } else {
                console.error("❌ Google Places API Error:", data.status);
                setSuggestions([]);
            }
        } catch (error) {
            console.error("❌ Network request failed:", error);
        } finally {
            setLoading(false);
        }
    } else {
        setSuggestions([]);
    }
};


  /** 📌 Fetch Place Details (Lat/Lng) */
  const fetchPlaceDetails = async (placeId) => {
    const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${GOOGLE_API_KEY}`;
    try {
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === "OK") {
        const location = data.result.geometry.location; // Extract lat/lng
        return { lat: location.lat, lng: location.lng };
      } else {
        // console.error("❌ Google Place Details API Error:", data.status);
        return null;
      }
    } catch (error) {
      // console.error("❌ Error fetching place details:", error);
      return null;
    }
  };

  /** ✅ Handle Place Selection */
  const handleSelect = async (item) => {
    setQuery(item.description);
    setSuggestions([]);
  
    const coordinates = await fetchPlaceDetails(item.place_id);
  
    if (coordinates) {
      const selectedLocation = {
        description: item.description,
        place_id: item.id, // Ensure place_id is stored correctly
        location: coordinates,
      };
  
      console.log("📍 Selected Location:", selectedLocation);
      onSelect(selectedLocation); // Pass selected location back to ArrangeYourRide.js
      saveLocationToAsyncStorage(selectedLocation);
    }
  };
  

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholderTextColor={COLORS.grey}
          placeholder={placeholder || "Enter location"}
          value={value}
          onFocus={onFocus} 
          onChangeText={handleInputChange}
        
        />
        {loading && <ActivityIndicator size="small" color={COLORS.primary} />}
      </View>

      {suggestions.length > 0 && (
        <FlatList
          data={suggestions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity style={styles.suggestionItem} onPress={() => handleSelect(item)}>
              <Text style={styles.suggestionText}>{item.description}</Text>
            </TouchableOpacity>
          )}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    paddingHorizontal: 10,
    borderRadius: 8,
    elevation: 2,
  },
  searchInput: {
    paddingVertical: 15,
    marginLeft: 10,
    color: COLORS.black,
    width: "75%",
  },
  suggestionItem: {
    padding: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.grey,
  },
  suggestionText: {
    color: COLORS.black,
  },
});

export default GooglePlacesAutocompleteComponent;
