import React from 'react';
import { Button, View, Alert } from 'react-native';
import PushNotification from 'react-native-push-notification';
import { checkPermission, requestNotificationPermission, registerSW } from './Helper/Script';

const PushNotifications = ({ email, accountType }) => {
    const enableNotifications = async () => {
        try {
            checkPermission();
            await requestNotificationPermission();
            await registerSW(email, accountType);
            Alert.alert('Notifications enabled!');
        } catch (error) {
            console.error("Error enabling notifications:", error);
        }
    };

    return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Button title="Enable Notifications" onPress={enableNotifications} />
        </View>
    );
};

export default PushNotifications;