import React from 'react';
import { TouchableOpacity, Image, Text, StyleSheet, ViewStyle, ImageStyle, TextStyle } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { images } from '../constants';

interface BackToHomeProps {
  style?: ViewStyle;
}

const BackToHome: React.FC<BackToHomeProps> = ({ style }) => {
  const navigation = useNavigation();

  return (
    <TouchableOpacity 
      onPress={() => navigation.navigate('TabStack', { screen: 'Home' })}
      style={[styles.container, style]}
    >
      {/* <Image 
        source={images.goback} 
        style={styles.backButton}
      /> */}
      <Text style={styles.label}>Home</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 5,
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,
  backButton: {
    width: 25,
    height: 25,
    marginRight: 8,
  } as ImageStyle,
  label: {
    fontSize: 16,
    color: '#000000',
  } as TextStyle,
});

export default BackToHome;