import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import InCallManager from 'react-native-incall-manager';
import { useCall } from '../contexts/CallContext';

const CallControls = ({ call, callType = 'voice' }) => {
  const { endCall } = useCall();
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(callType === 'video');

  // Toggle microphone mute
  const toggleMute = async () => {
    try {
      if (call && call.microphone) {
        if (isMuted) {
          await call.microphone.enable();
        } else {
          await call.microphone.disable();
        }
        setIsMuted(!isMuted);
      }
    } catch (error) {
      console.error('Error toggling mute:', error);
    }
  };

  // Toggle speaker
  const toggleSpeaker = () => {
    try {
      const newSpeakerState = !isSpeakerOn;
      InCallManager.setSpeakerphoneOn(newSpeakerState);
      setIsSpeakerOn(newSpeakerState);
    } catch (error) {
      console.error('Error toggling speaker:', error);
    }
  };

  // Toggle video (for video calls)
  const toggleVideo = async () => {
    try {
      if (call && call.camera && callType === 'video') {
        if (isVideoEnabled) {
          await call.camera.disable();
        } else {
          await call.camera.enable();
        }
        setIsVideoEnabled(!isVideoEnabled);
      }
    } catch (error) {
      console.error('Error toggling video:', error);
    }
  };

  // Switch camera (front/back)
  const switchCamera = async () => {
    try {
      if (call && call.camera && callType === 'video') {
        await call.camera.flip();
      }
    } catch (error) {
      console.error('Error switching camera:', error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.controlsRow}>
        {/* Mute Button */}
        <TouchableOpacity
          style={[styles.controlButton, isMuted && styles.activeButton]}
          onPress={toggleMute}
        >
          <Ionicons
            name={isMuted ? 'mic-off' : 'mic'}
            size={24}
            color={isMuted ? '#FF4444' : '#fff'}
          />
        </TouchableOpacity>

        {/* Speaker Button */}
        <TouchableOpacity
          style={[styles.controlButton, isSpeakerOn && styles.activeButton]}
          onPress={toggleSpeaker}
        >
          <Ionicons
            name={isSpeakerOn ? 'volume-high' : 'volume-low'}
            size={24}
            color={isSpeakerOn ? '#4CAF50' : '#fff'}
          />
        </TouchableOpacity>

        {/* Video Button (only for video calls) */}
        {callType === 'video' && (
          <TouchableOpacity
            style={[styles.controlButton, !isVideoEnabled && styles.activeButton]}
            onPress={toggleVideo}
          >
            <Ionicons
              name={isVideoEnabled ? 'videocam' : 'videocam-off'}
              size={24}
              color={!isVideoEnabled ? '#FF4444' : '#fff'}
            />
          </TouchableOpacity>
        )}

        {/* Camera Switch Button (only for video calls) */}
        {callType === 'video' && isVideoEnabled && (
          <TouchableOpacity
            style={styles.controlButton}
            onPress={switchCamera}
          >
            <Ionicons
              name="camera-reverse"
              size={24}
              color="#fff"
            />
          </TouchableOpacity>
        )}

        {/* End Call Button */}
        <TouchableOpacity
          style={[styles.controlButton, styles.endCallButton]}
          onPress={endCall}
        >
          <Ionicons
            name="call"
            size={24}
            color="#fff"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  activeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  endCallButton: {
    backgroundColor: '#FF4444',
    borderColor: '#FF4444',
  },
});

export default CallControls;
