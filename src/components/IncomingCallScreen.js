import React, { useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  StatusBar,
  Animated,
  Platform
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useCall, CALL_STATES } from '../contexts/CallContext';
import { useNavigation } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');

const IncomingCallScreen = () => {
  const { callState, acceptCall, rejectCall } = useCall();
  const { incomingCall, callStatus } = callState;
  const navigation = useNavigation();

  const pulseAnim = new Animated.Value(1);
  const shakeAnim = new Animated.Value(0);

  // Pulse animation for profile image
  useEffect(() => {
    if (callStatus === CALL_STATES.INCOMING) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();

      return () => pulse.stop();
    }
  }, [callStatus]);

  // Shake animation for accept button
  useEffect(() => {
    if (callStatus === CALL_STATES.INCOMING) {
      const shake = Animated.loop(
        Animated.sequence([
          Animated.timing(shakeAnim, {
            toValue: 10,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(shakeAnim, {
            toValue: -10,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(shakeAnim, {
            toValue: 10,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(shakeAnim, {
            toValue: 0,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.delay(1500), // Pause between shake cycles
        ])
      );
      shake.start();

      return () => shake.stop();
    }
  }, [callStatus]);

  // Navigate to CallScreen when call is accepted
  useEffect(() => {
    if (callStatus === CALL_STATES.CONNECTING || callStatus === CALL_STATES.CONNECTED) {
      console.log('🔵 Navigating to CallScreen with status:', callStatus);

      // Navigate to CallScreen with call data
      navigation.navigate('CallScreen', {
        rideDetails: {
          rideId: incomingCall?.rideId,
          driver: {
            name: incomingCall?.callerName,
            profileImg: incomingCall?.profileImg
          }
        },
        callData: {
          callId: incomingCall?.callId,
          callType: incomingCall?.callType,
          isIncoming: true
        }
      });
    }
  }, [callStatus, navigation, incomingCall]);

  if (!incomingCall || callStatus !== CALL_STATES.INCOMING) {
    return null;
  }

  const handleAcceptVoice = () => {
    console.log('🟢 Voice accept button pressed');
    acceptCall('voice');
  };

  const handleAcceptVideo = () => {
    console.log('🟢 Video accept button pressed');
    acceptCall('video');
  };

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="fullScreen"
      statusBarTranslucent={true}
    >
      <StatusBar backgroundColor="#1a1a1a" barStyle="light-content" />
      
      <View style={styles.container}>
        {/* Background overlay */}
        <View style={styles.backgroundOverlay} />
        
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.incomingText}>Incoming Call</Text>
        </View>

        {/* Caller Info */}
        <View style={styles.callerInfoContainer}>
          <Animated.View
            style={[
              styles.profileImageContainer,
              { transform: [{ scale: pulseAnim }] }
            ]}
          >
            <Image
              source={{
                uri: incomingCall.profileImg || 'https://i.pravatar.cc/300'
              }}
              style={styles.profileImage}
            />
          </Animated.View>
          
          <Text style={styles.callerName}>
            {incomingCall.callerName || 'Driver'}
          </Text>

          <View style={styles.callTypeContainer}>
            <Text style={styles.callTypeEmoji}>
              {incomingCall.callType === 'video' ? '📹' : '📞'}
            </Text>
            <Text style={styles.callTypeText}>
              {incomingCall.callType === 'video' ? 'Video Call' : 'Voice Call'}
            </Text>
          </View>
        </View>

        {/* Call Actions */}
        <View style={styles.actionsContainer}>
          {/* Reject Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={rejectCall}
            activeOpacity={0.8}
          >
            <Ionicons name="call" size={30} color="#fff" style={{ transform: [{ rotate: '135deg' }] }} />
          </TouchableOpacity>

          {/* Accept Voice Button with Shake Animation */}
          <Animated.View
            style={{
              transform: [{ translateX: shakeAnim }]
            }}
          >
            <TouchableOpacity
              style={[styles.actionButton, styles.acceptButton]}
              onPress={handleAcceptVoice}
              activeOpacity={0.8}
            >
              <Ionicons name="call" size={30} color="#fff" />
            </TouchableOpacity>
          </Animated.View>

          {/* Accept Video Button (if video call) */}
          {incomingCall.callType === 'video' && (
            <TouchableOpacity
              style={[styles.actionButton, styles.videoButton]}
              onPress={handleAcceptVideo}
              activeOpacity={0.8}
            >
              <Ionicons name="videocam" size={30} color="#fff" />
            </TouchableOpacity>
          )}
        </View>

        {/* Action Labels */}
        <View style={styles.labelsContainer}>
          <Text style={styles.actionLabel}>Decline</Text>
          <Text style={styles.actionLabel}>Accept</Text>
          {incomingCall.callType === 'video' && (
            <Text style={styles.actionLabel}>Video</Text>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 20,
    paddingBottom: 50,
  },
  backgroundOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(26, 26, 26, 0.95)',
  },
  header: {
    alignItems: 'center',
    paddingTop: 20,
  },
  incomingText: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
  },
  callerInfoContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  profileImageContainer: {
    marginBottom: 30,
  },
  profileImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  callerName: {
    fontSize: 28,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 15,
    textAlign: 'center',
  },
  callTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  callTypeEmoji: {
    fontSize: 20,
    marginRight: 8,
  },
  callTypeText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  callInfo: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 60,
    marginBottom: 20,
  },
  actionButton: {
    width: 75,
    height: 75,
    borderRadius: 37.5,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  rejectButton: {
    backgroundColor: '#FF4444',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  videoButton: {
    backgroundColor: '#2196F3',
  },
  labelsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 60,
    marginBottom: 30,
  },
  actionLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
});

export default IncomingCallScreen;
