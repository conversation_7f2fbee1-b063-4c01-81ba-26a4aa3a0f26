import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { StreamVideoClient } from '@stream-io/video-react-native-sdk';
import InCallManager from 'react-native-incall-manager';
import socketService from '../services/SocketService';
import AsyncStorage from '@react-native-async-storage/async-storage';

const CallContext = createContext();

export const useCall = () => {
  const context = useContext(CallContext);
  if (!context) {
    throw new Error('useCall must be used within CallProvider');
  }
  return context;
};

// Call event constants
export const CALL_EVENTS = {
  INCOMING_CALL: 'incomingCall',
  CALLER_TOKEN: 'callerToken',
  RECEIVER_TOKEN: 'receiverToken',
  CALL_ACCEPTED: 'callAccepted',
  CALL_REJECTED: 'callRejected',
  CALL_ENDED: 'callEnded',
  CALL_USER: 'callUser',
  ACCEPT_CALL: 'acceptCall',
  REJECT_CALL: 'rejectCall',
  END_CALL: 'endCall'
};

// Call states
export const CALL_STATES = {
  IDLE: 'idle',
  RINGING: 'ringing',
  INCOMING: 'incoming',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  ENDED: 'ended',
  REJECTED: 'rejected',
  NO_ANSWER: 'no_answer'
};

// Helper function to extract caller name from message
const extractCallerName = (message) => {
  if (!message) return null;

  console.log('🔍 Original message:', message);

  // Remove "Incoming call from " prefix if it exists
  const cleanedMessage = message.replace(/^Incoming call from\s*/i, '');

  console.log('🔍 Cleaned caller name:', cleanedMessage);

  // Return the cleaned name, or null if it's empty
  return cleanedMessage.trim() || null;
};

export const CallProvider = ({ children }) => {
  const [callState, setCallState] = useState({
    incomingCall: null,
    isCallActive: false,
    callStatus: CALL_STATES.IDLE,
    callType: 'voice', // 'voice' | 'video'
    participants: [],
    callId: null,
    rideId: null,
    streamClient: null,
    currentCall: null,
    isMuted: false,
    isSpeakerOn: false,
    isVideoEnabled: true,
    error: null
  });

  const callSocket = socketService.getCallSocket();

  // Get current user ID
  const getCurrentUserId = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const user = JSON.parse(userData);
        return user.id || user.userId || `passenger_${Date.now()}`;
      }
      return `passenger_${Date.now()}`;
    } catch (error) {
      console.error('Error getting user ID:', error);
      return `passenger_${Date.now()}`;
    }
  };

  // Setup socket event listeners
  useEffect(() => {
    const setupCallEventListeners = () => {
      // Handle incoming call
      callSocket.on(CALL_EVENTS.INCOMING_CALL, (data) => {
        console.log('Incoming call received:', data);
        setCallState(prev => ({
          ...prev,
          incomingCall: {
            callId: data.callId,
            rideId: data.rideId,
            callerName: extractCallerName(data.message) || 'Driver',
            profileImg: data.profileImg,
            callType: data.callType || 'voice'
          },
          callStatus: CALL_STATES.INCOMING,
          callType: data.callType || 'voice'
        }));
        
        // Start ringtone
        InCallManager.startRingtone('_BUNDLE_');
      });

      // Handle caller token (when initiating call)
      callSocket.on(CALL_EVENTS.CALLER_TOKEN, async ({ token, callId }) => {
        console.log('Caller token received:', callId);
        try {
          const userId = await getCurrentUserId();
          const client = new StreamVideoClient({
            apiKey: "vjw8jjkqz6z8", // TODO: Move to env
            token,
            user: { id: userId },
          });
          
          setCallState(prev => ({
            ...prev,
            streamClient: client,
            callId,
            callStatus: CALL_STATES.CONNECTING
          }));
          
          await joinCall(client, callId, prev => prev.callType);
        } catch (error) {
          console.error('Error handling caller token:', error);
          handleCallError('Failed to initialize call');
        }
      });

      // Handle receiver token (when receiving call)
      callSocket.on(CALL_EVENTS.RECEIVER_TOKEN, async ({ token, callId }) => {
        console.log('🔵 Receiver token received:', callId);
        try {
          const userId = await getCurrentUserId();
          const client = new StreamVideoClient({
            apiKey: "vjw8jjkqz6z8", // TODO: Move to env
            token,
            user: { id: userId },
          });

          setCallState(prev => {
            const newState = {
              ...prev,
              streamClient: client,
              callId
            };

            // If we're in CONNECTING state, join the call immediately
            if (prev.callStatus === CALL_STATES.CONNECTING) {
              console.log('🔵 Auto-joining call after receiving receiver token...');
              // Join call asynchronously
              joinCall(client, callId, prev.callType).then(() => {
                setCallState(prevState => ({
                  ...prevState,
                  callStatus: CALL_STATES.CONNECTED,
                  currentCall: client.call('default', callId)
                }));
              }).catch(error => {
                console.error('❌ Error auto-joining call:', error);
                handleCallError('Failed to join call');
              });
            }

            return newState;
          });
        } catch (error) {
          console.error('❌ Error handling receiver token:', error);
          handleCallError('Failed to initialize call');
        }
      });

      // Handle call accepted
      callSocket.on(CALL_EVENTS.CALL_ACCEPTED, (data) => {
        console.log('Call accepted:', data);
        InCallManager.stopRingtone();
        setCallState(prev => ({
          ...prev,
          callStatus: CALL_STATES.CONNECTED,
          isCallActive: true
        }));
      });

      // Handle call ended
      callSocket.on(CALL_EVENTS.CALL_ENDED, (data) => {
        console.log('Call ended:', data);
        handleCallEnd();
      });

      // Handle call rejected
      callSocket.on(CALL_EVENTS.CALL_REJECTED, (data) => {
        console.log('Call rejected:', data);
        setCallState(prev => ({
          ...prev,
          callStatus: CALL_STATES.REJECTED
        }));
        handleCallEnd();
      });
    };

    setupCallEventListeners();

    // Cleanup listeners on unmount
    return () => {
      callSocket.off(CALL_EVENTS.INCOMING_CALL);
      callSocket.off(CALL_EVENTS.CALLER_TOKEN);
      callSocket.off(CALL_EVENTS.RECEIVER_TOKEN);
      callSocket.off(CALL_EVENTS.CALL_ACCEPTED);
      callSocket.off(CALL_EVENTS.CALL_ENDED);
      callSocket.off(CALL_EVENTS.CALL_REJECTED);
    };
  }, []);

  // Join Stream.io call
  const joinCall = async (client, callId, callType) => {
    try {
      console.log('🔵 joinCall started with:', { callId, callType });

      const call = client.call('default', callId, {
        audio: true,
        video: callType === 'video'
      });

      console.log('🔵 Call object created, attempting to join...');
      await call.join();
      console.log('✅ Joined call successfully');

      setCallState(prev => ({
        ...prev,
        currentCall: call,
        callStatus: CALL_STATES.CONNECTED,
        isCallActive: true
      }));

      console.log('✅ Call state updated to CONNECTED');
      
      // Setup audio session
      InCallManager.start({
        media: callType === 'video' ? 'video' : 'audio',
        auto: true,
        ringback: '_BUNDLE_'
      });
      
      return call;
    } catch (error) {
      console.error('Failed to join call:', error);
      handleCallError('Failed to join call');
      throw error;
    }
  };

  // Start outgoing call
  const startCall = async (rideId, callType = 'voice') => {
    try {
      if (!rideId) {
        throw new Error('Ride ID is required');
      }
      
      setCallState(prev => ({
        ...prev,
        callStatus: CALL_STATES.RINGING,
        rideId,
        callType,
        error: null
      }));
      
      callSocket.emit(CALL_EVENTS.CALL_USER, { rideId, callType });
      console.log('Call initiated for ride:', rideId);
      
      // Start ringback tone
      InCallManager.startRingback('_BUNDLE_');
      
    } catch (error) {
      console.error('Error starting call:', error);
      handleCallError('Failed to start call');
    }
  };

  // Accept incoming call
  const acceptCall = async (callType = 'voice') => {
    try {
      console.log('🔵 Accepting call with type:', callType);

      const { incomingCall, streamClient } = callState;
      if (!incomingCall) {
        throw new Error('No incoming call available');
      }

      console.log('🔵 Incoming call data:', incomingCall);
      console.log('🔵 Stream client available:', !!streamClient);

      InCallManager.stopRingtone();

      // Emit accept event
      console.log('🔵 Emitting accept call event...');
      console.log('🔵 Socket connected:', callSocket.connected);
      console.log('🔵 Accept call data:', {
        callId: incomingCall.callId,
        rideId: incomingCall.rideId,
        callType: callType
      });

      callSocket.emit(CALL_EVENTS.ACCEPT_CALL, {
        callId: incomingCall.callId,
        rideId: incomingCall.rideId,
        callType: callType
      });

      // Update call state immediately
      setCallState(prev => ({
        ...prev,
        incomingCall: null,
        callStatus: CALL_STATES.CONNECTING,
        callType: callType,
        isCallActive: true
      }));

      // If we have a stream client, join the call
      if (streamClient) {
        console.log('🔵 Joining call...');
        await joinCall(streamClient, incomingCall.callId, callType);
      } else {
        console.log('🔵 No stream client available, waiting for token...');
      }

    } catch (error) {
      console.error('❌ Error accepting call:', error);
      handleCallError('Failed to accept call');
    }
  };

  // Reject incoming call
  const rejectCall = () => {
    try {
      const { incomingCall } = callState;
      if (!incomingCall) return;
      
      InCallManager.stopRingtone();
      
      callSocket.emit(CALL_EVENTS.REJECT_CALL, {
        callId: incomingCall.callId,
        rideId: incomingCall.rideId
      });
      
      setCallState(prev => ({
        ...prev,
        incomingCall: null,
        callStatus: CALL_STATES.REJECTED
      }));
      
      setTimeout(() => {
        setCallState(prev => ({
          ...prev,
          callStatus: CALL_STATES.IDLE
        }));
      }, 2000);
      
    } catch (error) {
      console.error('Error rejecting call:', error);
    }
  };

  // End active call
  const endCall = async () => {
    try {
      const { currentCall, rideId } = callState;
      
      if (rideId) {
        callSocket.emit(CALL_EVENTS.END_CALL, { rideId });
      }
      
      if (currentCall) {
        await currentCall.leave();
      }
      
      handleCallEnd();
      
    } catch (error) {
      console.error('Error ending call:', error);
      handleCallEnd(); // Still cleanup on error
    }
  };

  // Handle call end cleanup
  const handleCallEnd = () => {
    InCallManager.stop();
    InCallManager.stopRingtone();
    InCallManager.stopRingback();
    
    setCallState(prev => ({
      ...prev,
      incomingCall: null,
      isCallActive: false,
      callStatus: CALL_STATES.ENDED,
      currentCall: null,
      streamClient: null,
      callId: null,
      rideId: null,
      isMuted: false,
      isSpeakerOn: false,
      error: null
    }));
    
    // Reset to idle after brief delay
    setTimeout(() => {
      setCallState(prev => ({
        ...prev,
        callStatus: CALL_STATES.IDLE
      }));
    }, 1000);
  };

  // Handle call errors
  const handleCallError = (errorMessage) => {
    setCallState(prev => ({
      ...prev,
      error: errorMessage,
      callStatus: CALL_STATES.IDLE
    }));
    
    Alert.alert('Call Error', errorMessage);
    handleCallEnd();
  };

  const value = {
    callState,
    startCall,
    acceptCall,
    rejectCall,
    endCall,
    handleCallError
  };

  return (
    <CallContext.Provider value={value}>
      {children}
    </CallContext.Provider>
  );
};
