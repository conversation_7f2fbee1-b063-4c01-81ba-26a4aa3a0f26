/* eslint-disable prettier/prettier */
import {Dimensions} from 'react-native';
const {width, height} = Dimensions.get('window');

export const COLORS = {
  primary: '#007BFF',
  secondary: '#2F97C1',
  dark_blue: '#172A3A',
  light_green: '#E3FFE4',
  light_blue: '#E8F5FF',
  tertiaryText: '#3C3B42',
  status: '#0B4870',
  deepYellow: 'rgba(250, 189, 59, 1)',
  grey: '#989898',
  border: '#ccc',
  light_grey: '#ADADAD',
  orange: '#FF7A00',
  dark: '#169AEE',
  mid_grey: '#58575A',
  white: '#ffffff',
  black: '#000000',
  red: '#E00000',
  pinkRed: '#FF5555',
  logOutRed: '#FF7867',
  deep_grey: '#F2F2F2',
  activeIndicator: 'rgba(0, 0, 0, 0.25)',
  inActiveIndicator: 'rgba(242, 242, 242, 1)',
};
export const SIZES = {
  // global sizes
  base: 8,
  font: 14,
  radius: 12,
  padding: 24,

  // font sizes
  largeTitle: 40,
  h1: 24,
  h2: 22,
  h3: 17,
  h4: 15,
  body1: 20,
  body2: 18,
  body3: 16,
  body4: 13,
  body5: 12,
  body6: 10,
  button: 16,
  // app dimensions
  width,
  height,
};

export const FONTS = {
  largeTitle: {
    fontFamily: 'Poppins-Bold',
    fontSize: SIZES.largeTitle,
    lineHeight: 46.88,
    fontWeight: '600',
  },
  subTitle: {
    fontFamily: 'Poppins-Regular',
    fontSize: SIZES.h4,
    lineHeight: 22.92,
    fontWeight: '400',
  },
  h1: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.h1,
    lineHeight: 28,
    fontWeight: '600',
  },
  h2: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.h2,
    lineHeight: 23.44,
    fontWeight: '600',
  },
  h3: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.h3,
    lineHeight: 20,
    fontWeight: '600',
  },
  h4: {
    fontFamily: 'Poppins-Bold',
    fontSize: SIZES.h4,
    lineHeight: 18.75,
    fontWeight: '600',
  },
  body1: {
    fontFamily: 'Poppins-Regular',
    fontSize: SIZES.body1,
    lineHeight: 23.44,
    fontWeight: '400',
  },
  body2: {
    fontFamily: 'Poppins-Regular',
    fontSize: SIZES.body2,
    lineHeight: 22,
    fontWeight: '400',
  },
  body3: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.body3,
    lineHeight: 20,
    // fontWeight: '500',
  },
  body4: {
    fontFamily: 'Poppins-Regular',
    fontSize: SIZES.body4,
    lineHeight: 16.3,
    fontWeight: '400',
  },
  body5: {
    fontFamily: 'Poppins-Regular',
    fontSize: SIZES.body4,
    lineHeight: 15,
    fontWeight: '400',
  },
  body6: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.body4,
    lineHeight: 15,
    fontWeight: '400',
  },
  body7: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.body3,
    lineHeight: 19,
    fontWeight: '400',
  },
  button: {
    fontWeight: '600',
    fontFamily: 'Poppins-Bold',
    fontSize: 15,
  },
  body8: {
    fontFamily: '-Medium',
    fontSize: SIZES.body4,
    lineHeight: 16.3,
    fontWeight: '400',
  },
  //   button: {
  //     fontFamily: 'Poppins-Regular',
  //     fontSize: SIZES.button,
  //     lineHeight: 16,
  //   },
};

export const GLOBAL_STYLES = {
  googleAutoCompleteStyle: {
    container: {
      flex: 0,
      color: 'black',
    },
    textInput: {
      fontSize: 14,
      fontFamily: 'Poppins-Medium',
      borderRadius: 10,
      backgroundColor: '#F2F2F2',
      marginBottom: 0,
      color: 'black',
    },
    textInputContainer: {
      paddingHorizontal: 5,
      backgroundColor: '#F2F2F2',
      paddingVertical: 5,
      borderRadius: 10,
      margin: 0,
    },
    description: {color: 'black'},
    predefinedPlacesDescription: {
      color: '#1faadb',
    },
  },
};

const appTheme = {COLORS, SIZES, GLOBAL_STYLES, FONTS};

export default appTheme;
