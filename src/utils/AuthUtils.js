import AsyncStorage from '@react-native-async-storage/async-storage';
import { store } from '../../redux/store';
import { overwriteStore } from '../../redux/ActionCreator';

/**
 * Authentication utility functions
 */
export class AuthUtils {
  
  /**
   * Save authentication data after successful login
   */
  static async saveAuthData(token, userData) {
    try {
      console.log('💾 AuthUtils.saveAuthData called with:');
      console.log('- Token:', token);
      console.log('- UserData:', userData);

      // Detailed token analysis in AuthUtils
      console.log('💾 DETAILED TOKEN ANALYSIS IN AUTHUTILS:');
      console.log('- Token type:', typeof token);
      console.log('- Token value:', token);
      console.log('- Token length:', token?.length);
      console.log('- Token is truthy:', !!token);
      console.log('- Token stringified:', JSON.stringify(token));
      console.log('- Token after JSON.stringify:', JSON.stringify(token));

      const promises = [];

      if (token) {
        console.log('💾 Saving token...');
        promises.push(AsyncStorage.setItem('token', JSON.stringify(token)));
      } else {
        console.log('⚠️ No token provided to save');
      }

      if (userData) {
        console.log('💾 Saving user data...');
        promises.push(AsyncStorage.setItem('userData', JSON.stringify(userData)));
        promises.push(AsyncStorage.setItem('userProfile', JSON.stringify(userData)));

        // Save passenger ID if available
        if (userData.passengerId) {
          console.log('💾 Saving passenger ID:', userData.passengerId);
          promises.push(AsyncStorage.setItem('passengerId', userData.passengerId));
        }
      } else {
        console.log('⚠️ No user data provided to save');
      }

      console.log('💾 Setting isLoggedIn to true...');
      promises.push(AsyncStorage.setItem('isLoggedIn', 'true'));

      await Promise.all(promises);
      console.log('✅ Authentication data saved successfully');

      // Also save to Redux store (using same pattern as login screens)
      if (userData) {
        console.log('🔄 Dispatching user data to Redux store...');
        store.dispatch(overwriteStore({
          name: 'user',
          value: userData
        }));
        console.log('✅ User data dispatched to Redux store');
      }

      // Verify what was actually saved
      const savedToken = await AsyncStorage.getItem('token');
      const savedUserData = await AsyncStorage.getItem('userData');
      const savedIsLoggedIn = await AsyncStorage.getItem('isLoggedIn');

      console.log('🔍 Verification - What was actually saved:');
      console.log('- Token saved:', !!savedToken);
      console.log('- UserData saved:', !!savedUserData);
      console.log('- isLoggedIn saved:', savedIsLoggedIn);

      return true;
    } catch (error) {
      console.error('❌ Error saving authentication data:', error);
      return false;
    }
  }
  
  /**
   * Check if user is authenticated
   */
  static async isAuthenticated() {
    try {
      console.log('🔍 AuthUtils.isAuthenticated() called...');

      const [token, isLoggedIn, userData] = await Promise.all([
        AsyncStorage.getItem('token'),
        AsyncStorage.getItem('isLoggedIn'),
        AsyncStorage.getItem('userData')
      ]);

      console.log('🔍 Raw values from AsyncStorage:');
      console.log('- token:', token);
      console.log('- isLoggedIn:', isLoggedIn);
      console.log('- userData:', userData);

      // Detailed analysis of retrieved token
      console.log('🔍 RETRIEVED TOKEN ANALYSIS:');
      console.log('- Retrieved token type:', typeof token);
      console.log('- Retrieved token value:', token);
      console.log('- Retrieved token length:', token?.length);
      console.log('- Retrieved token parsed:', token ? JSON.parse(token) : 'NULL');
      console.log('- Retrieved token is truthy:', !!token);

      const hasToken = !!token;
      const isLoggedInBool = isLoggedIn === 'true';
      const hasUserData = !!userData;
      const isAuth = hasToken && isLoggedInBool && hasUserData;

      console.log('🔍 Authentication check breakdown:', {
        hasToken,
        isLoggedInBool,
        hasUserData,
        finalResult: isAuth
      });

      return isAuth;
    } catch (error) {
      console.error('❌ Error checking authentication:', error);
      return false;
    }
  }
  
  /**
   * Get stored user data
   */
  static async getUserData() {
    try {
      const userData = await AsyncStorage.getItem('userData');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('❌ Error getting user data:', error);
      return null;
    }
  }
  
  /**
   * Get stored token
   */
  static async getToken() {
    try {
      const token = await AsyncStorage.getItem('token');
      return token ? JSON.parse(token) : null;
    } catch (error) {
      console.error('❌ Error getting token:', error);
      return null;
    }
  }
  
  /**
   * Clear all authentication data
   */
  static async clearAuthData() {
    try {
      const keysToRemove = [
        'token',
        'userData', 
        'isLoggedIn',
        'UserProfile',
        'userProfile',
        'passengerId'
      ];
      
      await AsyncStorage.multiRemove(keysToRemove);

      // Also clear Redux store
      console.log('🔄 Clearing Redux store...');
      store.dispatch(overwriteStore({
        name: 'user',
        value: {}
      }));
      console.log('✅ Redux store cleared');

      console.log('✅ All authentication data cleared');

      return true;
    } catch (error) {
      console.error('❌ Error clearing authentication data:', error);
      return false;
    }
  }
  
  /**
   * Update user data
   */
  static async updateUserData(newUserData) {
    try {
      const currentUserData = await this.getUserData();
      const updatedUserData = { ...currentUserData, ...newUserData };
      
      await AsyncStorage.setItem('userData', JSON.stringify(updatedUserData));
      await AsyncStorage.setItem('userProfile', JSON.stringify(updatedUserData));
      
      console.log('✅ User data updated successfully');
      return true;
    } catch (error) {
      console.error('❌ Error updating user data:', error);
      return false;
    }
  }
  
  /**
   * Get current user from Redux store
   */
  static getCurrentUserFromRedux() {
    try {
      const state = store.getState();
      const user = state.store?.user || {};
      console.log('🔍 Current user from Redux (state.store.user):', user);
      return user;
    } catch (error) {
      console.error('❌ Error getting user from Redux:', error);
      return {};
    }
  }

  /**
   * Debug function to log all stored auth data
   */
  static async debugAuthData() {
    try {
      const [token, userData, isLoggedIn, userProfile, passengerId] = await Promise.all([
        AsyncStorage.getItem('token'),
        AsyncStorage.getItem('userData'),
        AsyncStorage.getItem('isLoggedIn'),
        AsyncStorage.getItem('userProfile'),
        AsyncStorage.getItem('passengerId')
      ]);

      // Also get Redux state
      const reduxUser = this.getCurrentUserFromRedux();

      console.log('🐛 DEBUG: Current Auth Data:', {
        asyncStorage: {
          token: token ? 'EXISTS' : 'NULL',
          userData: userData ? 'EXISTS' : 'NULL',
          isLoggedIn,
          userProfile: userProfile ? 'EXISTS' : 'NULL',
          passengerId
        },
        redux: {
          user: reduxUser,
          isEmpty: Object.keys(reduxUser).length === 0
        }
      });

      return {
        token,
        userData: userData ? JSON.parse(userData) : null,
        isLoggedIn,
        userProfile: userProfile ? JSON.parse(userProfile) : null,
        passengerId
      };
    } catch (error) {
      console.error('❌ Error debugging auth data:', error);
      return null;
    }
  }


}

export default AuthUtils;
