// Call Testing Utilities
import { Alert } from 'react-native';
import socketService from '../services/SocketService';

export class CallTestUtils {
  static async testSocketConnections() {
    console.log('=== Testing Socket Connections ===');
    
    try {
      const passengerSocket = socketService.getPassengerSocket();
      const callSocket = socketService.getCallSocket();
      
      console.log('Passenger Socket Connected:', passengerSocket.connected);
      console.log('Call Socket Connected:', callSocket.connected);
      
      if (!passengerSocket.connected) {
        console.warn('Passenger socket not connected');
      }
      
      if (!callSocket.connected) {
        console.warn('Call socket not connected');
      }
      
      return {
        passengerConnected: passengerSocket.connected,
        callConnected: callSocket.connected
      };
    } catch (error) {
      console.error('Error testing socket connections:', error);
      return { error: error.message };
    }
  }

  static simulateIncomingCall(testData = {}) {
    console.log('=== Simulating Incoming Call ===');
    
    const mockCallData = {
      callId: testData.callId || `test_call_${Date.now()}`,
      rideId: testData.rideId || `test_ride_${Date.now()}`,
      message: testData.callerName || 'Test Driver',
      profileImg: testData.profileImg || 'https://i.pravatar.cc/300',
      callType: testData.callType || 'voice',
      ...testData
    };
    
    try {
      const callSocket = socketService.getCallSocket();
      
      // Simulate server emitting incoming call event
      setTimeout(() => {
        callSocket.emit('incomingCall', mockCallData);
        console.log('Simulated incoming call:', mockCallData);
      }, 1000);
      
      return mockCallData;
    } catch (error) {
      console.error('Error simulating incoming call:', error);
      Alert.alert('Test Error', 'Failed to simulate incoming call');
    }
  }

  static simulateCallToken(type = 'caller', callId = null) {
    console.log(`=== Simulating ${type} Token ===`);
    
    const mockToken = `mock_token_${Date.now()}`;
    const testCallId = callId || `test_call_${Date.now()}`;
    
    try {
      const callSocket = socketService.getCallSocket();
      const eventName = type === 'caller' ? 'callerToken' : 'receiverToken';
      
      setTimeout(() => {
        callSocket.emit(eventName, {
          token: mockToken,
          callId: testCallId
        });
        console.log(`Simulated ${type} token:`, { token: mockToken, callId: testCallId });
      }, 500);
      
      return { token: mockToken, callId: testCallId };
    } catch (error) {
      console.error(`Error simulating ${type} token:`, error);
      Alert.alert('Test Error', `Failed to simulate ${type} token`);
    }
  }

  static testCallFlow(rideId = null) {
    console.log('=== Testing Complete Call Flow ===');
    
    const testRideId = rideId || `test_ride_${Date.now()}`;
    
    Alert.alert(
      'Call Flow Test',
      'This will simulate a complete call flow. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Test',
          onPress: () => {
            // Step 1: Simulate outgoing call
            console.log('Step 1: Simulating outgoing call...');
            socketService.emitCallEvent('callUser', { rideId: testRideId });
            
            // Step 2: Simulate caller token after 2 seconds
            setTimeout(() => {
              console.log('Step 2: Simulating caller token...');
              this.simulateCallToken('caller');
            }, 2000);
            
            // Step 3: Simulate call accepted after 5 seconds
            setTimeout(() => {
              console.log('Step 3: Simulating call accepted...');
              const callSocket = socketService.getCallSocket();
              callSocket.emit('callAccepted', { rideId: testRideId });
            }, 5000);
            
            // Step 4: Simulate call ended after 10 seconds
            setTimeout(() => {
              console.log('Step 4: Simulating call ended...');
              const callSocket = socketService.getCallSocket();
              callSocket.emit('callEnded', { rideId: testRideId });
            }, 10000);
          }
        }
      ]
    );
  }

  static logCallState(callState) {
    console.log('=== Current Call State ===');
    console.log('Call Status:', callState.callStatus);
    console.log('Is Call Active:', callState.isCallActive);
    console.log('Call Type:', callState.callType);
    console.log('Call ID:', callState.callId);
    console.log('Ride ID:', callState.rideId);
    console.log('Has Incoming Call:', !!callState.incomingCall);
    console.log('Has Stream Client:', !!callState.streamClient);
    console.log('Has Current Call:', !!callState.currentCall);
    console.log('Error:', callState.error);
    console.log('========================');
  }

  static testPermissions() {
    console.log('=== Testing Call Permissions ===');
    
    // This would typically test microphone and camera permissions
    // For now, just log that permissions should be tested
    console.log('TODO: Implement permission testing');
    console.log('- Microphone permission');
    console.log('- Camera permission (for video calls)');
    
    Alert.alert(
      'Permission Test',
      'Check console for permission testing info. Implement actual permission checks as needed.'
    );
  }

  static clearCallState() {
    console.log('=== Clearing Call State ===');
    
    // This would reset the call context to initial state
    // Implementation depends on how you want to expose this from CallContext
    console.log('TODO: Implement call state clearing');
    
    Alert.alert('Test Utility', 'Call state clearing logged to console');
  }

  static testSocketEvents() {
    console.log('=== Testing Socket Events ===');
    
    const callSocket = socketService.getCallSocket();
    
    // Test event listeners
    const testEvents = [
      'incomingCall',
      'callerToken', 
      'receiverToken',
      'callAccepted',
      'callEnded',
      'callRejected'
    ];
    
    testEvents.forEach(event => {
      const listenerCount = callSocket.listenerCount(event);
      console.log(`${event}: ${listenerCount} listeners`);
    });
    
    Alert.alert(
      'Socket Events Test',
      'Check console for event listener counts'
    );
  }

  static generateTestRideData() {
    return {
      rideId: `test_ride_${Date.now()}`,
      driver: {
        name: 'Test Driver',
        phone: '+1234567890',
        profileImg: 'https://i.pravatar.cc/300'
      },
      passenger: {
        name: 'Test Passenger',
        phone: '+0987654321'
      },
      status: 'active'
    };
  }

  static showTestMenu() {
    Alert.alert(
      'Call Test Menu',
      'Choose a test to run:',
      [
        { text: 'Test Connections', onPress: () => this.testSocketConnections() },
        { text: 'Simulate Incoming Call', onPress: () => this.simulateIncomingCall() },
        { text: 'Test Call Flow', onPress: () => this.testCallFlow() },
        { text: 'Test Permissions', onPress: () => this.testPermissions() },
        { text: 'Test Socket Events', onPress: () => this.testSocketEvents() },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  }
}

export default CallTestUtils;
