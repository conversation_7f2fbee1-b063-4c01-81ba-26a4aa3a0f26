import { createStackNavigator } from '@react-navigation/stack';
import Home from '../screens/HomeBreak/Home';
import HeadToDestination1 from '../screens/HomeBreak/HeadToDestination1';
import HeadToDestination2 from '../screens/HomeBreak/HeadToDestination2';
import QueuedRide from '../screens/HomeBreak/QueuedRide';
import RideType from '../screens/HomeBreak/RideType';
import StayInArea from '../screens/HomeBreak/StayInArea';

const Stack = createStackNavigator();

function HomeStack() {
return (
    <Stack.Navigator>
        <Stack.Screen name="Home" component={Home} options={{ headerShown: false }} />
        <Stack.Screen name="HeadToDestination1" component={HeadToDestination1} options={{ headerShown: false }} />
        <Stack.Screen name="HeadToDestination2" component={HeadToDestination2} options={{ headerShown: false }} />
        <Stack.Screen name="QueuedRide" component={QueuedRide} options={{ headerShown: false }} />
        <Stack.Screen name="RideType" component={RideType} options={{ headerShown: false }} />
        <Stack.Screen name="StayInArea" component={StayInArea} options={{ headerShown: false }} />
    </Stack.Navigator>
);
}

export default HomeStack;