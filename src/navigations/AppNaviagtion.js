import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { COLORS } from '../constants';
import SplashScreen from '../screens/SplashScreen';
import Toast from 'react-native-toast-message';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// import GetStarted from '../screens/AuthScreens/GetStarted';
import AddProfilePicture from '../screens/AuthScreens/Onboarding/AddProfilePicture';
import MobileNumber from '../screens/AuthScreens/Onboarding/MobileNumber';
import AddSocialSecurity from '../screens/AuthScreens/Onboarding/AddSocialSecurity';
import AddSocialSecurityNumber from '../screens/AuthScreens/Onboarding/AddSocialSecurityNumber';
import AddYourCar from '../screens/AuthScreens/Onboarding/AddYourCar';
import CheckConsent from '../screens/AuthScreens/Onboarding/CheckConsent';
import DriverLicense from '../screens/AuthScreens/Onboarding/DriverLicense';
import IdentificationSubmitted from '../screens/AuthScreens/Onboarding/IdentificationSubmitted';
import Notification from '../screens/AuthScreens/Onboarding/Notification';
import TermOfService from '../screens/AuthScreens/Onboarding/TermOfService';
import WhereToDrive from '../screens/AuthScreens/Onboarding/WhereToDrive';
import WelcomeBack from '../screens/AuthScreens/SignIn/WelcomeBack';
import SecretCode from '../screens/AuthScreens/SignIn/SecretCode';
import AddNewBank from '../screens/Earnings/AddNewBank';
import CashOut from '../screens/Earnings/CashOut';
import EarningHistory from '../screens/Earnings/EarningHistory';
import EarningSummary from '../screens/Earnings/EarningSummary';
import PayOutHistory from '../screens/Earnings/PayOutHistory';
import PayOutMethods from '../screens/Earnings/PayOutMethods';
import RecentRides from '../screens/Earnings/RecentRides';
import RideDetails from '../screens/Earnings/RideDetails';
import WeeklyBreakDown from '../screens/Earnings/WeeklyBreakDown';
import AddACarDetails from '../screens/AuthScreens/Onboarding/AddACarDetails';
import AddCarDetails from '../screens/AuthScreens/Onboarding/AddCarDetails';
import CCamera from '../screens/AuthScreens/Onboarding/Camera';
import { getToken } from '../../redux/shared';
import GetStarted from '../screens/AuthScreens/Onboarding/GetStarted';
import RSecretCode from '../screens/AuthScreens/Onboarding/RSecretCode';
import AddName from '../screens/AuthScreens/Onboarding/AddName';
import AddEmail from '../screens/AuthScreens/Onboarding/AddEmail';
import DriverLicenseImage from '../screens/AuthScreens/Onboarding/DriverLicenseImage';
import CarFormDetails from '../screens/AuthScreens/Onboarding/CarFormDetails';
import SNotification from '../screens/AuthScreens/SignIn/SNotification';
import HomeStack from './HomeStack';
import ArrangeYourRide from '../screens/HomeBreak/ArrangeYourRide';
import LocationViaMap from '../screens/HomeBreak/LocationViaMap';
import ChooseADriver from '../screens/HomeBreak/ChooseADriver';
import RideDetailScreen from '../screens/HomeBreak/RideDetailScreen';
import RideTrackingScreen from '../screens/HomeBreak/RideTrackingScreen';
import RideFeedbackScreen from '../screens/HomeBreak/RideFeedbackScreen';
import SplitRideScreen from '../screens/SplitRide/SplitRideScreen';
import ShareRideScreen from '../screens/HomeBreak/ShareRideScreen';
import ShareRideWithFriendsScreen from '../screens/HomeBreak/ShareRideWithFriendsScreen';
import AboutUs from '../screens/Account/AboutUs';
import AddHomeAddress from '../screens/Account/AddHomeAddress';
import Message from '../screens/Account/Message';
import AddWorkAddress from '../screens/Account/AddWorkAddress';
import ConfirmLocation from '../screens/Account/ConfirmLocation';
import EditProfileScreen from '../screens/Account/EditProfileScreen';
import FAQ from '../screens/Account/FAQ';
import LanguageScreen from '../screens/Account/LanguageScreen';
import NotificationScreen from '../screens/Account/NotificationScreen';
import NotificationSettings from '../screens/Account/NotificationSettings';
import Payment from '../screens/Account/Payment';
import RideFuzeFunds from '../screens/Account/RideFuzeFunds';
import VerifyEmailScreen from '../screens/Account/VerifyEmailScreen';
import ViewProfileScreen from '../screens/Account/ViewProfileScreen';
import ProfileScreen from '../screens/Account/ProfileScreen';
import MyRidesScreen from '../screens/Account/MyRidesScreen';
import RideHistoryScreen from '../screens/Ride/RideScreen';
import PackageDelivery from '../screens/HomeBreak/Delivery/PackageDelivery';
import DeliveryViaMap from '../screens/HomeBreak/Delivery/DeliveryViaMap';
import DeliveryChooseDriver from '../screens/HomeBreak/Delivery/DeliveryChooseDriver';
import DeliveryPickUpSpot from '../screens/HomeBreak/Delivery/DeliveryPickUpSpot';
import DeliveryDetails from '../screens/HomeBreak/Delivery/DeliveryDetails';
import ApproveDelivery from '../screens/HomeBreak/Delivery/ApproveDelivery';
import Requestmade from '../screens/HomeBreak/Delivery/Requestmade';
import ViewFare from '../screens/HomeBreak/TaxVan/ViewFare';
import SearchResult from '../screens/HomeBreak/TaxVan/SearchResult';
import OurService from '../screens/HomeBreak/TaxVan/OurService';
import ApproveReserve from '../screens/HomeBreak/ReserveRide/ApproveReserve';
import ArrangeReserveRide from '../screens/HomeBreak/ReserveRide/ArrangeReserveRide';
import PickDate from '../screens/HomeBreak/ReserveRide/PickDate';
import Reservationmade from '../screens/HomeBreak/ReserveRide/Reservationmade';
import ReserveChooseDriver from '../screens/HomeBreak/ReserveRide/ReserveChooseDriver';
import ReserveRide from '../screens/HomeBreak/ReserveRide/ReserveRide';
import ViewReserve from '../screens/HomeBreak/ReserveRide/ViewReserve';
import ChatScreen from '../screens/HomeBreak/ChatScreen';
import CallScreen from '../screens/HomeBreak/CallScreen';
import RideDetailss from '../screens/Ride/RideDetailss';
import { CallProvider } from '../contexts/CallContext';
import IncomingCallScreen from '../components/IncomingCallScreen';
import CardDetails from '../screens/Account/Card/CardDetails';
import CardList from '../screens/Account/Card/CardList';
import AddCard from '../screens/Account/Card/AddCard';
import StripePaymentScreen from '../screens/Account/Card/StripePaymentScreen';
import ConfirmPickUp from '../screens/HomeBreak/ConfirmPickUp';
import RidePayment from '../screens/HomeBreak/RidePayment';
import RideForgotSomethingScreen from '../screens/HomeBreak/RideForgotSomethingScreen';
import MessageScreen from '../screens/Account/MessageScreen';
import AuthUtils from '../utils/AuthUtils';
import { useDispatch, useSelector } from 'react-redux';
import { overwriteStore } from '../../redux/ActionCreator';


const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const AppNavigation = () => {
  const [initialRouteName, setInitialRouteName] = useState('')
  const dispatch = useDispatch();

  // Add selector to monitor Redux state
  const reduxStoreUser = useSelector(state => state?.store?.user);

  // const fetchToken = async () => {
  //   try {
  //     const storedToken = await getToken();
  //    console.log('stored stokennn.....................', storedToken)
  //     if (storedToken) {
  //       setInitialRouteName(URL_WELCOME)}
  //     else {
  //       setInitialRouteName(URL_INTRO_SLIDER)
  //     }
  //    } catch (error) {

  //   }
  // }
  const fetchToken = async () => {
    try {
      console.log('🔍 Checking authentication status...');

      // First, let's manually check what's in AsyncStorage
      const token = await AsyncStorage.getItem('token');
      const isLoggedIn = await AsyncStorage.getItem('isLoggedIn');
      const userData = await AsyncStorage.getItem('userData');

      console.log('📱 Manual AsyncStorage Check:');
      console.log('- Token exists:', !!token);
      console.log('- Token value:', token);
      console.log('- isLoggedIn:', isLoggedIn);
      console.log('- userData exists:', !!userData);

      // Use AuthUtils to check authentication
      const isAuthenticated = await AuthUtils.isAuthenticated();
      console.log('🔐 AuthUtils result:', isAuthenticated);

      if (isAuthenticated) {
        // User is authenticated, restore user data to Redux and go to main app
        const storedUserData = await AuthUtils.getUserData();
        if (storedUserData) {
          console.log('🔄 Restoring user data to Redux:', storedUserData);
          dispatch(overwriteStore({
            name: 'user',
            value: storedUserData
          }));
          console.log('✅ User data dispatched to Redux store');
        }

        setInitialRouteName('TabStack');
        console.log('✅ User is authenticated, navigating to TabStack');
      } else {
        // User is not authenticated, go to login
        setInitialRouteName('WelcomeBack');
        console.log('❌ User is not authenticated, navigating to WelcomeBack');
      }

      // Debug: Log all auth data in development
      if (__DEV__) {
        await AuthUtils.debugAuthData();
      }

    } catch (error) {
      console.error('❌ Error checking authentication:', error);
      // On error, default to login screen
      setInitialRouteName('WelcomeBack');
    }
  }
  // Monitor Redux state changes
  useEffect(() => {
    if (reduxStoreUser && Object.keys(reduxStoreUser).length > 0) {
      console.log('🔍 Redux state updated - user data available:', reduxStoreUser);
    }
  }, [reduxStoreUser]);

  useEffect(() => {
    setTimeout(() => {
      fetchToken()
    }, 1000);
  }, [])
  return (
    <CallProvider>
      <NavigationContainer>
        {
          !initialRouteName ? (
            <SplashScreen />
          ) :
          (
            <Stack.Navigator
              initialRouteName={initialRouteName}
              screenOptions={() => ({
                headerShown: false,
              })}>
              <Stack.Screen name={'TabStack'} component={TabStack} />

              {/* Onboarding */}
              <Stack.Screen name={'GetStarted'} component={GetStarted} />
              <Stack.Screen name={'DriverLicenseImage'} component={DriverLicenseImage} />
              <Stack.Screen name={'AddACarDetails'} component={AddACarDetails} />
              <Stack.Screen name={'CarFormDetails'} component={CarFormDetails} />
              <Stack.Screen name={'AddCarDetails'} component={AddCarDetails} />
              <Stack.Screen name={'AddProfilePicture'} component={AddProfilePicture} />
              <Stack.Screen name={'AddSocialSecurity'} component={AddSocialSecurity} />
              <Stack.Screen name={'AddSocialSecurityNumber'} component={AddSocialSecurityNumber} />
              <Stack.Screen name={'AddYourCar'} component={AddYourCar} />
              <Stack.Screen name={'CCamera'} component={CCamera} />
              <Stack.Screen name={'CheckConsent'} component={CheckConsent} />
              <Stack.Screen name={'DriverLicense'} component={DriverLicense} />
              <Stack.Screen name={'IdentificationSubmitted'} component={IdentificationSubmitted} />
              <Stack.Screen name={'Notification'} component={Notification} />
              <Stack.Screen name={'TermOfService'} component={TermOfService} />
              <Stack.Screen name={'WhereToDrive'} component={WhereToDrive} />
              <Stack.Screen name={'MobileNumber'} component={MobileNumber} />
              <Stack.Screen name={'RSecretCode'} component={RSecretCode} />
              <Stack.Screen name={'ConfirmPickUp'} component={ConfirmPickUp} />
              <Stack.Screen name={'RidePayment'} component={RidePayment} />
              <Stack.Screen name={'RideForgotSomethingScreen'} component={RideForgotSomethingScreen} />
              <Stack.Screen name={'MessageScreen'} component={MessageScreen} />

              

              {/* SignIn */}
              <Stack.Screen name={'WelcomeBack'} component={WelcomeBack} />
              <Stack.Screen name={'SecretCode'} component={SecretCode} />
              <Stack.Screen name={'SNotification'} component={SNotification} />
              <Stack.Screen name={'AddName'} component={AddName} />
              <Stack.Screen name={'AddEmail'} component={AddEmail} />

              {/* Earning */}
              <Stack.Screen name={'AddNewBank'} component={AddNewBank} />
              <Stack.Screen name={'CashOut'} component={CashOut} />
              <Stack.Screen name={'EarningHistory'} component={EarningHistory} />
              {/* <Stack.Screen name={'SecretCode'} component={SecretCode} /> */}
              <Stack.Screen name={'EarningSummary'} component={EarningSummary} />
              <Stack.Screen name={'PayOutHistory'} component={PayOutHistory} />
              <Stack.Screen name={'PayOutMethods'} component={PayOutMethods} />
              <Stack.Screen name={'RecentRides'} component={RecentRides} />
              <Stack.Screen name={'RideDetails'} component={RideDetails} />
              <Stack.Screen name={'WeeklyBreakDown'} component={WeeklyBreakDown} />
              <Stack.Screen name={'ArrangeYourRide'} component={ArrangeYourRide} />
              <Stack.Screen name={'LocationViaMap'} component={LocationViaMap} />
              <Stack.Screen name={'ChooseADriver'} component={ChooseADriver} />
              <Stack.Screen name={'RideDetailScreen'} component={RideDetailScreen} />
              <Stack.Screen name={'RideFeedbackScreen'} component={RideFeedbackScreen} />
              <Stack.Screen name={'RideTrackingScreen'} component={RideTrackingScreen} />
              <Stack.Screen name={'ShareRideScreen'} component={ShareRideScreen} />
              <Stack.Screen name={'SplitRideScreen'} component={SplitRideScreen} />
              <Stack.Screen name={'ShareRideWithFriendsScreen'} component={ShareRideWithFriendsScreen} />
              <Stack.Screen name={'OurService'} component={OurService} />


              {/* //Account */}

              <Stack.Screen name={'AboutUs'} component={AboutUs} />
              <Stack.Screen name={'AddHomeAddress'} component={AddHomeAddress} />
              <Stack.Screen name={'AddWorkAddress'} component={AddWorkAddress} />
              <Stack.Screen name={'ConfirmLocation'} component={ConfirmLocation} />
              <Stack.Screen name={'EditProfileScreen'} component={EditProfileScreen} />

              <Stack.Screen name={'FAQ'} component={FAQ} />
              <Stack.Screen name={'LanguageScreen'} component={LanguageScreen} />
              <Stack.Screen name={'NotificationScreen'} component={NotificationScreen} />
              <Stack.Screen name={'NotificationSettings'} component={NotificationSettings} />
              <Stack.Screen name={'Payment'} component={Payment} />
              <Stack.Screen name={'ProfileScreen'} component={ProfileScreen} />
              <Stack.Screen name={'RideFuzeFunds'} component={RideFuzeFunds} />
              <Stack.Screen name={'VerifyEmailScreen'} component={VerifyEmailScreen} />
              <Stack.Screen name={'ViewProfileScreen'} component={ViewProfileScreen} />
              <Stack.Screen name={'MyRidesScreen'} component={MyRidesScreen} />
              <Stack.Screen name={'RideHistoryScreen'} component={RideHistoryScreen} />


              {/* //Delivery */}
              <Stack.Screen name={'PackageDelivery'} component={PackageDelivery} />
              <Stack.Screen name={'DeliveryViaMap'} component={DeliveryViaMap} />
              <Stack.Screen name={'DeliveryChooseDriver'} component={DeliveryChooseDriver} />
              <Stack.Screen name={'DeliveryPickUpSpot'} component={DeliveryPickUpSpot} />
              <Stack.Screen name={'DeliveryDetails'} component={DeliveryDetails} />
              <Stack.Screen name={'ApproveDelivery'} component={ApproveDelivery} />
              <Stack.Screen name={'Requestmade'} component={Requestmade} />



              {/* Taxi- van */}
              <Stack.Screen name={'ViewFare'} component={ViewFare} />
              <Stack.Screen name={'SearchResult'} component={SearchResult} />

              <Stack.Screen name={'ApproveReserve'} component={ApproveReserve} />
              <Stack.Screen name={'ArrangeReserveRide'} component={ArrangeReserveRide} />
              <Stack.Screen name={'PickDate'} component={PickDate} />
              <Stack.Screen name={'Reservationmade'} component={Reservationmade} />
              <Stack.Screen name={'ReserveChooseDriver'} component={ReserveChooseDriver} />
              <Stack.Screen name={'ReserveRide'} component={ReserveRide} />
              <Stack.Screen name={'ViewReserve'} component={ViewReserve} />

              {/* ChatScreen */}
              <Stack.Screen name={'ChatScreen'} component={ChatScreen} />
              <Stack.Screen name={'CallScreen'} component={CallScreen} />
              <Stack.Screen name={'RideDetailss'} component={RideDetailss} />

              <Stack.Screen name={'CardDetails'} component={CardDetails} />
              <Stack.Screen name={'CardList'} component={CardList} />
              <Stack.Screen name={'AddCard'} component={AddCard} />
              <Stack.Screen name={'Message'} component={Message} />
              <Stack.Screen name="StripePaymentScreen" component={StripePaymentScreen} />




            </Stack.Navigator>
          )
      }

      {/* Global Incoming Call Modal */}
      <IncomingCallScreen />

      <Toast refs={(ref) => { Toast.setRef(ref) }} />
    </NavigationContainer>
    </CallProvider>
  );
};

export default AppNavigation;

const TabStack = () => {
  return (
    <View style={{ flex: 1, backgroundColor: COLORS.white }}>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          headerShown: 'false',
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            if (route.name === 'Home') {
              iconName = focused ? 'home' : 'home-outline';
            } else if (route.name === 'Service') {
              iconName = focused ? 'layers' : 'layers-outline';
            } else if (route.name === 'Rides') {
              iconName = focused ? 'car' : 'car-outline';
            } else if (route.name === 'Profile') {
              iconName = focused ? 'person' : 'person-outline';
            }

            // Return the icon
            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: COLORS.primary,
          tabBarInactiveTintColor: 'gray',
        })}
      >
        <Tab.Screen name="Home" component={HomeStack} options={{ headerShown: false }} />
        <Tab.Screen name="Service" component={OurService} options={{ headerShown: false }} />
        <Tab.Screen name="Rides" component={RideHistoryScreen} options={{ headerShown: false }} />
        <Tab.Screen name="Profile" component={ProfileScreen} options={{ headerShown: false }} />
      </Tab.Navigator>
    </View>
  );
};



function InformationScreen() {
  return (
    <View style={styles.container}>
      <Text>Information Screen</Text>
    </View>
  );
}

function RidesScreen() {
  return (
    <View style={styles.container}>
      <Text>Rides Screen</Text>
    </View>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

