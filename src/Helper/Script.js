import messaging from '@react-native-firebase/messaging';

export const checkPermission = () => {
    if (!messaging().isSupported()) {
        throw new Error("No support for messaging!");
    }
};

export const registerSW = async (email, accountType) => {
    const token = await messaging().getToken();
    await saveSubscription(token, email, accountType);
};

export const requestNotificationPermission = async () => {
    const authStatus = await messaging().requestPermission();
    const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (!enabled) {
        throw new Error("Notification permission not granted");
    }
};

const saveSubscription = async (token, email, accountType) => {
    const body = { data: token, email, accountType };
    const response = await fetch(`http://inride-server.onrender.com/api/pushNotification/saveSubscription`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
    });

    return response.json();
};
