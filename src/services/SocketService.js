import io from 'socket.io-client';
import { BASE_URL } from '../../Baseurl';

const PASSENGER_SOCKET_URL = `${BASE_URL}/passenger`;
const CALL_SOCKET_URL = `${BASE_URL}/general`;

class SocketService {
  constructor() {
    // Main passenger socket for ride operations
    this.passengerSocket = io(PASSENGER_SOCKET_URL, {
      transports: ['websocket'],
      withCredentials: true,
    });

    // Dedicated call socket for call operations
    this.callSocket = io(CALL_SOCKET_URL, {
      transports: ['websocket'],
      withCredentials: true,
    });

    console.log("Passenger socket initialized with URL:", PASSENGER_SOCKET_URL);
    console.log("Call socket initialized with URL:", CALL_SOCKET_URL);

    this.setupConnectionHandlers();

    // For backward compatibility
    this.socket = this.passengerSocket;
  }

  setupConnectionHandlers() {
    // Passenger socket handlers
    this.passengerSocket.on('connect', () => console.log('Passenger socket connected successfully'));
    this.passengerSocket.on('connect_error', (err) => console.log('Passenger socket connection error:', err));
    this.passengerSocket.on('disconnect', () => console.log('Passenger socket disconnected'));

    // Call socket handlers
    this.callSocket.on('connect', () => console.log('Call socket connected successfully'));
    this.callSocket.on('connect_error', (err) => console.log('Call socket connection error:', err));
    this.callSocket.on('disconnect', () => console.log('Call socket disconnected'));
  }

 

  connect() {
    this.passengerSocket.connect();
    this.callSocket.connect();
    console.log("Socket connection attempt started");
  }

  disconnect() {
    if (this.passengerSocket) {
      this.passengerSocket.disconnect();
      console.log("Passenger socket disconnected");
    }
    if (this.callSocket) {
      this.callSocket.disconnect();
      console.log("Call socket disconnected");
    }
  }

  // Get specific socket instances
  getPassengerSocket() {
    return this.passengerSocket;
  }

  getCallSocket() {
    return this.callSocket;
  }

  onEvent(event, func) {
    this.passengerSocket.on(event, (data) => {
      console.log(`Passenger event ${event} received with data:`, JSON.stringify(data,null,2));
      func(data);
    });
  }

  onCallEvent(event, func) {
    this.callSocket.on(event, (data) => {
      console.log(`Call event ${event} received with data:`, JSON.stringify(data,null,2));
      func(data);
    });
  }

  // emitEvent(event, data) {
  //   this.socket.emit(event, data);
  //   console.log(`Event ${event} emitted with data:`, JSON.stringify(data,null,2));
  // }

  emitEvent(event, data, callback) {
    if (callback) {
        this.passengerSocket.emit(event, data, (response) => {
            console.log(`Passenger response received for event ${event}:`, JSON.stringify(response,null,2));
            callback(response);
        });
    } else {
        this.passengerSocket.emit(event, data);
        console.log(`Passenger event ${event} emitted with data:`, JSON.stringify(data,null,2));
    }
  }

  emitCallEvent(event, data, callback) {
    if (callback) {
        this.callSocket.emit(event, data, (response) => {
            console.log(`Call response received for event ${event}:`, JSON.stringify(response,null,2));
            callback(response);
        });
    } else {
        this.callSocket.emit(event, data);
        console.log(`Call event ${event} emitted with data:`, JSON.stringify(data,null,2));
    }
  }
}

const socketService = new SocketService();
export default socketService;
