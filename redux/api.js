import { api, api2, apiSocial, apiUrl, baseLink, handleSignInChange } from './shared';
import { signIn, updUser, signOut, overwriteStore } from './ActionCreator';
import { store } from './store';
import AsyncStorage from '@react-native-async-storage/async-storage';




//Driver Authentication

export const signOutApi = async () => {
  try {
    await AsyncStorage.removeItem('token');
    store.dispatch(signIn({ success: false }));

    return true;

  }

  catch (error) {
    console.log(error, "error in api.js");
  };
}





// export const SignIn = async data => {
//   try {
//     const response = await api('POST', 'user/signin', data);
//     // console.log(response, 'in the login api');
//     store.dispatch(
//       signIn({
//         success: true,
//         user: response,
//       }), 
//     );

//     await AsyncStorage.setItem('token', JSON.stringify(response.token));
    
//     GetWalletBalance()
 
//     return response;
//   } catch (error) {
//     // console.log(error, "error in api.js");
//     throw error;
//   }
// };

export const VerifyLoginOtp = async (data) => {
  try {
    const response = await api('POST', 'passenger/auth/verifyLoginOtp', data)
   
    store.dispatch(
      signIn({
        success: true,
        user: response?.data,
      }),
    );

    // await AsyncStorage.setItem('token', JSON.stringify(response?.message));
    console.log(response?.message, "response in verifyLoginOtp");
    
    
    return response;
  } catch (error) {
    throw error;
  }
};




export const RegisterWithPassengerAccount = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/registerWithPassengerAccount', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const SignIn = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/signin', data)
    return response;
  } catch (error) {
    throw error;
  }
};
export const CompleteDriverRegistration = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/completeDriverRegistration', data)
    return response;
  } catch (error) {
    throw error;
  }
};


export const RegisterNewPassenger = async (data) => {
  try {
    const response = await api('POST', 'passenger/auth/registerNumber', data)
    console.log(response, "response in registerNewPassenger");
    
    // const response = await api('POST', 'driver/auth/registerNewDriver', data)registerNumber
    return response;
  } catch (error) {
    throw error;
  }
};

export const ResendOtp = async (data) => {
  try {
    const response = await api('POST', 'passenger/auth/resendOtp', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const CompleteNewDriverRegistration = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/completeDriverRegistration', data,{ file: true })
    log(response, "response in completeNewDriverRegistration")
    return response;
  } catch (error) {
    throw error;
  }
};

export const VerifyPersonalDetails = async (data) => {
  try {
    
    const response = await api('POST', 'passenger/auth/verifyPersonalDetails', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const VerifySSN = async (data) => {
  try {
    const response = await api('POST', 'passenger/auth/verifySSN', data)
    return response;
  } catch (error) {
    throw error;
  }
};


export const VerifyOtp = async (data) => {
  try {
    const response = await api('POST', 'auth/verifyOtp', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const VerifyToken = async (data) => {
  try {
    const response = await api('POST', 'passenger/auth/verifyToken', data)
    return response;
  } catch (error) {
    throw error;
  }
};





//fetchreference 
export const fetchReference = async (reference) => {
  try {
    const response = await api('GET', 'user/verifypayments' + reference);
    // console.log(response, "response in fetchReference");
    store.dispatch(overwriteStore({ name: "assignment", value: response.data }));
    return response;
  }
  catch (error) {
    // console.log(error, "error in api.js");

  }
};




export const ValidateBank = async (data) => {
  try {
    const response = await api('POST', 'airtime/banks', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const ManageMonnify = async (data) => {
  try {
    const response = await api('PUT', 'wallet/manage-monnify', data)
    return response;
  } catch (error) {
    throw error;
  }
};






export const ImageUpload = async data => {
  console.log(data, '...data');
  try {
    const response = await api('POST', 'file', data, {
      file: true,
    });


    return response;
  } catch (error) {
    throw error;
  }
};



